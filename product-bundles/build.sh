LOG_FILE="$HOME/.pm2/logs/grouptify-pwacommerce-out.log"

echo "Start building home $(date +%Y-%m-%d-%T%Z)" >> $LOG_FILE

export NODE_OPTIONS=--max-old-space-size=4096

source ~/.nvm/nvm.sh
nvm use 20 || {
  echo "Error: Failed to switch to Node.js version 20" >> $LOG_FILE
  exit 1
}

yarn install || {
  echo "Error: Yarn install in root directory failed" >> $LOG_FILE
  exit 1
}

cd web || {
  echo "Error: Failed to change directory to web" >> $LOG_FILE
  exit 1
}

yarn install || {
  echo "Error: Yarn install in web directory failed" >> $LOG_FILE
  exit 1
}

cd frontend || {
  echo "Error: Failed to change directory to frontend" >> $LOG_FILE
  exit 1
}

yarn install || {
  echo "Error: Yarn install failed" >> $LOG_FILE
  exit 1
}

NODE_ENV='production' SHOPIFY_API_KEY="f3c7e2f5de24101b794516ae2211c449" yarn run build || {
  echo "Error: Build process failed" >> $LOG_FILE
  exit 1
}

echo "Done building home $(date +%Y-%m-%d-%T%Z)" >> $LOG_FILE
