'use strict';
function flyingPages() {
  var e = new Set(),
    n = new Set(),
    t = document.createElement('link'),
    r =
      t.relList &&
      t.relList.supports &&
      t.relList.supports('prefetch') &&
      window.IntersectionObserver &&
      'isIntersecting' in IntersectionObserverEntry.prototype;
  if (
    !(
      navigator.connection &&
      (navigator.connection.saveData || (navigator.connection.effectiveType || '').includes('2g'))
    ) &&
    r
  ) {
    var o = function (e) {
        var n = setTimeout(function () {
          return l();
        }, 5e3);
        (function (e) {
          return new Promise(function (n, t) {
            var r = document.createElement('link');
            (r.rel = 'prefetch'), (r.href = e), (r.onload = n), (r.onerror = t), document.head.appendChild(r);
          });
        })(e)
          .catch(function () {
            return l();
          })
          .finally(function () {
            return clearTimeout(n);
          });
      },
      i = function (t) {
        try {
          var r = new URL(t),
            i = new URL(window.location.href);
          if (r.origin === i.origin && r.pathname === i.pathname) return;
        } catch (e) {
          return;
        }

        var a = !!(1 < arguments.length && void 0 !== arguments[1]) && arguments[1];
        if (!n.has(t) && !e.has(t)) {
          var c = window.location.origin;
          if (t.substring(0, c.length) === c && window.location.href !== t) {
            for (var u = 0; u < window.FPConfig.ignoreKeywords.length; u++)
              if (t.includes(window.FPConfig.ignoreKeywords[u])) return;
            a ? (o(t), n.add(t)) : e.add(t);
          }
        }
      },
      a = new IntersectionObserver(function (e) {
        e.forEach(function (e) {
          if (e.isIntersecting) {
            var n = e.target.href;
            i(n, !window.FPConfig.maxRPS);
          }
        });
      }),
      c = null,
      u = function (e) {
        var t = e.target.closest('a');
        t &&
          t.href &&
          !n.has(t.href) &&
          (c = setTimeout(function () {
            i(t.href, !0);
          }, window.FPConfig.hoverDelay));
      },
      s = function (e) {
        var t = e.target.closest('a');
        t && t.href && !n.has(t.href) && i(t.href, !0);
      },
      f = function (e) {
        var t = e.target.closest('a');
        t && t.href && !n.has(t.href) && clearTimeout(c);
      },
      d =
        window.requestIdleCallback ||
        function (e) {
          var n = Date.now();
          return setTimeout(function () {
            e({
              didTimeout: !1,
              timeRemaining: function () {
                return (0, Math.max)(0, 50 - (Date.now() - n));
              },
            });
          }, 1);
        },
      l = function () {
        document.querySelectorAll('a').forEach(function (e) {
          return a.unobserve(e);
        }),
          e.clear(),
          document.removeEventListener('mouseover', u, !0),
          document.removeEventListener('mouseout', f, !0),
          document.removeEventListener('touchstart', s, !0);
      };
    (window.FPConfig = Object.assign({ delay: 0, ignoreKeywords: [], maxRPS: 3, hoverDelay: 50 }, window.FPConfig)),
      setInterval(function () {
        Array.from(e)
          .slice(0, window.FPConfig.maxRPS)
          .forEach(function (t) {
            o(t), n.add(t), e.delete(t);
          });
      }, 1e3),
      d(function () {
        return setTimeout(function () {
          return document.querySelectorAll('a').forEach(function (e) {
            return a.observe(e);
          });
        }, 1e3 * window.FPConfig.delay);
      });
    var v = { capture: !0, passive: !0 };
    document.addEventListener('mouseover', u, v),
      document.addEventListener('mouseout', f, v),
      document.addEventListener('touchstart', s, v);
  }
}
flyingPages();
