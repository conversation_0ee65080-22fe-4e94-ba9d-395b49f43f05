window.mpgInitAppScript = (mpg) => {
  // process string utils
  mpg.normalizeStr = (str) => str?.replace(/[^\p{L}\p{N}]/gu, '')?.toLowerCase();
  mpg.isSimilarStr = (str1, str2) => {
    const normal1 = mpg.normalizeStr(str1);
    const normal2 = mpg.normalizeStr(str2);
    return normal1.includes(normal2) || normal2.includes(normal1);
  };
  mpg.isSimilarStrStrict = (str1, str2) => {
    const normal1 = mpg.normalizeStr(str1);
    const normal2 = mpg.normalizeStr(str2);
    return normal1 === normal2;
  };
  mpg.stripHTML = (html) => html.replace(/<\/?[^>]+(>|$)/g, '');
  mpg.isSwatchOption = (optionName) => {
    if (!optionName || !Array.isArray(mpg.appSettings.swatchIdentifiers)) return false;
    return mpg.appSettings.swatchIdentifiers.some((identifier) => mpg.isSimilarStrStrict(optionName, identifier));
  };
  mpg.getTemplateForOption = (optionName, forCard = true) => {
    const isSwatch = mpg.isSwatchOption(optionName);
    const defaultTemplate = isSwatch ? (forCard ? 'mpg-swatch-circle' : 'mpg-swatch-polaroid') : 'mpg-button';

    const configTemplate = mpg.optionConfigs?.find((config) =>
      mpg.isSimilarStrStrict(mpg.t(config.option), mpg.t(optionName))
    )?.[forCard ? 'template_card' : 'template_prod'];

    return configTemplate || defaultTemplate;
  };
  mpg.findMatchingProduct = (activeValueSet, group, valueId = null, optionName = null) => {
    // find product directly matching all active values
    let matchProd = group.products.find((p) => p.options.every((o) => activeValueSet.includes(o)));

    // if no match and valueId is provided, find with all combinations containing valueId
    if (!matchProd && valueId) {
      const combinations = mpg.genCombinations(group.options);
      for (const combination of combinations) {
        if (!combination.includes(valueId)) continue;
        matchProd = group.products.find((p) => p.options.every((o) => combination.includes(o)));
        if (matchProd) break;
      }
    }

    return matchProd;
  };

  // process translation utils
  mpg.translation = mpg.translation.find((t) => t.isoCode === Shopify.locale)?.mapper || {};
  mpg.t = (text) => mpg.translation[text] || text;

  mpg.toggleShowValue = (valueLinker, valueCount) => {
    const buttonEls = [...document.querySelectorAll(`.mpg-swatch__nav-limit[v-linker="${valueLinker}"]`)];
    const valueEls = [...document.querySelectorAll(`.mpg-swatch__option[v-linker="${valueLinker}"]`)].filter((el) => {
      const index = Number(el.getAttribute('v-index'));
      if (index + 1 > mpg.merchantConfig?.prod_style?.value_limit_max) return true;
      return false;
    });

    if (!valueEls.length) return;
    const isHide = valueEls[0].style.display === 'none';

    if (isHide) {
      valueEls.forEach((el) => {
        el.style.display = '';
      });
      buttonEls.forEach((el) => {
        el.innerText = '—';
      });
    } else {
      valueEls.forEach((el) => {
        el.style.display = 'none';
      });
      buttonEls.forEach((el) => {
        el.textContent = `+${valueCount - mpg.merchantConfig?.prod_style?.value_limit_max}`;
      });
    }
  };

  // dropdown utils
  mpg.toggleDropdown = (triggerEl) => {
    const linker = triggerEl.getAttribute('drd-linker');

    const dropdownEl = document.querySelector(`.mpg-swatch__dropdown-wrapper[drd-linker="${linker}"]`);
    if (!dropdownEl) return;

    const triggerRect = triggerEl.getBoundingClientRect();
    dropdownEl.style.width = triggerRect.width + 'px';
    dropdownEl.style.left = triggerRect.left + 'px';
    dropdownEl.style.top = triggerRect.top + triggerRect.height - 1 + 'px';

    if (dropdownEl.classList.contains('mpg-show')) dropdownEl.classList.remove('mpg-show');
    else dropdownEl.classList.add('mpg-show');
  };

  mpg.hideDropdown = (triggerEl) => {
    const linker = triggerEl.getAttribute('drd-linker');

    const dropdownEl = document.querySelector(`.mpg-swatch__dropdown-wrapper[drd-linker="${linker}"]`);
    if (!dropdownEl) return;

    dropdownEl.classList.remove('mpg-show');
  };

  mpg.handleBrowserSelectBoxChange = (selectEl) => {
    if (!selectEl) return;
    const selectedValue = selectEl.value;
    const dropdownLinker = selectEl.getAttribute('drd-linker');

    const fixedSelectedValue = selectedValue.replaceAll('"', '\\"');

    const originalOptionEl = document.querySelector(
      `.mpg-swatch__dropdown-wrapper[drd-linker="${dropdownLinker}"] .mpg-swatch__option[o-value="${fixedSelectedValue}"]`
    );

    if (!originalOptionEl) return;
    originalOptionEl.click();
    const optionSetEl = selectEl.closest('.mpg-swatch__option-set');

    if (!optionSetEl) return;
    const optionLabelValueEl = optionSetEl.querySelector('.mpg-swatch__label-value');

    if (!optionLabelValueEl) return;
    optionLabelValueEl.innerText = selectedValue;

    const oldSelectedEls = selectEl.querySelectorAll('option[selected]');
    const newSelectedEl = selectEl.querySelector(`option[value="${fixedSelectedValue}"]`);
    oldSelectedEls.forEach((ose) => ose.removeAttribute('selected'));

    if (!newSelectedEl) return;
    newSelectedEl.setAttribute('selected', true);
  };

  // other
  mpg.genCombinations = (options) => {
    const result = [];

    const helper = (index, currentCombo) => {
      if (index === options.length) {
        result.push([...currentCombo]);
        return;
      }

      for (const value of options[index].values) {
        currentCombo.push(value.id);
        helper(index + 1, currentCombo);
        currentCombo.pop();
      }
    };

    helper(0, []);
    return result;
  };

  mpg.swapProductHandle = (originalUrl, newHandle) => {
    let urlObj;
    let isPathOnly = false;

    try {
      urlObj = new URL(originalUrl);
    } catch (e) {
      urlObj = new URL(originalUrl, 'https://example.com');
      isPathOnly = true;
    }

    const parts = urlObj.pathname.split('/').filter(Boolean);
    let locale = '';
    let startIndex = 0;

    if (parts[0] !== 'products' && parts[0] !== 'collections') {
      locale = parts[0];
      startIndex = 1;
    }

    if (parts[startIndex] === 'collections') {
      startIndex += 2;
    }

    if (parts[startIndex] === 'products') {
      const newPath = `/${locale ? locale + '/' : ''}products/${newHandle}`;
      const finalUrl = newPath + urlObj.search + urlObj.hash;

      return isPathOnly ? finalUrl : urlObj.origin + finalUrl;
    }

    console.warn('URL does not contain string "products"');
    return originalUrl;
  };

  // get handle from a link
  mpg.getHandle = (link) => {
    try {
      const url = new URL(link);
      const parts = url?.pathname?.split('/products/');
      const handle = parts.pop();
      return decodeURI(handle);
    } catch (err) {
      return '';
    }
  };

  // get products from card elements
  mpg.getProducts = () => {
    const CARD_ID = mpg.themeSetting.product_card.card_id;
    const LINK_ID = mpg.themeSetting.product_card.link_id;

    const allCards = [...document.querySelectorAll(CARD_ID)];

    const products = allCards
      .map((c) => {
        const link = c.querySelector(LINK_ID);
        const handle = mpg.getHandle(link?.href);
        return { handle };
      })
      .filter((p) => p.handle);

    return products;
  };

  mpg.getProductByHandle = async (handle) => {
    const storedProduct = window.sessionStorage.getItem(`mpg_stored_product[${handle}]`);
    if (storedProduct) return JSON.parse(storedProduct);

    const query = `
      query getProductByHandle @inContext(language: ${window.Shopify.locale?.toUpperCase()?.split('-')?.[0] || 'EN'}) {
        productByHandle(handle: "${handle}") {
          id
          title
          handle
          featuredImage {
            url(transform: {maxWidth: 500})
          }
          options(first: 3) {
            name
            values
          }
          variants(first: 250) {
            nodes {
              id
              title
              availableForSale
              image {
                url(transform: {maxWidth: 500})
              }
              compareAtPrice {
                amount
              }
              price {
                amount
              }
            }
          }
          selectedOrFirstAvailableVariant {
            id
            availableForSale
          }
        }
      }
    `;

    const response = await fetch(`https://${window.Shopify.shop}/api/2024-10/graphql.json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Storefront-Access-Token': mpg.merchantConfig.storefront_access_token,
      },
      body: JSON.stringify({ query }),
    });

    if (!response.ok) {
      console.error('Error:', response.status, await response.text());
      return;
    }

    const data = await response.json();
    const product = data.data.productByHandle;

    product.options.forEach((option) => {
      option.values = [...new Set(option.values)];
    });

    if (
      product.options.length === 1 &&
      ['Title', 'Titre', 'Titel', 'Titula'].includes(product.options[0].name) &&
      product.options[0].values.length === 1
    )
      product.options = [];

    window.sessionStorage.setItem(`mpg_stored_product[${handle}]`, JSON.stringify(product));

    return product;
  };

  mpg.roundingStandard =
    {
      AED: 1,
      AFN: 100,
      ALL: 100,
      AMD: 100,
      ANG: 1,
      AUD: 1,
      AWG: 1,
      AZN: 1,
      BAM: 1,
      BBD: 1,
      BDT: 100,
      BGN: 1,
      BIF: 1,
      BND: 1,
      BOB: 1,
      BSD: 1,
      BWP: 1,
      BZD: 1,
      CAD: 1,
      CDF: 1,
      CHF: 1,
      CNY: 1,
      CRC: 100,
      CVE: 100,
      CZK: 1,
      DJF: 100,
      DKK: 1,
      DOP: 100,
      DZD: 100,
      EGP: 1,
      ETB: 1,
      EUR: 0.95,
      FJD: 1,
      FKP: 1,
      GBP: 1,
      GMD: 1,
      GNF: 1,
      GTQ: 1,
      GYD: 100,
      HKD: 1,
      HNL: 1,
      HUF: 100,
      IDR: 1,
      ILS: 1,
      INR: 100,
      ISK: 100,
      JMD: 100,
      JPY: 100,
      KES: 100,
      KGS: 100,
      KHR: 1,
      KMF: 100,
      KRW: 1,
      KYD: 1,
      KZT: 100,
      LAK: 1,
      LBP: 1,
      LKR: 100,
      MAD: 1,
      MDL: 1,
      MKD: 100,
      MMK: 1,
      MNT: 1,
      MOP: 1,
      MUR: 1,
      MVR: 1,
      MWK: 100,
      MYR: 1,
      NGN: 100,
      NIO: 1,
      NPR: 100,
      NZD: 1,
      PEN: 1,
      PGK: 1,
      PHP: 100,
      PKR: 100,
      PLN: 1,
      PYG: 1,
      QAR: 1,
      RON: 1,
      RSD: 100,
      RWF: 100,
      SAR: 1,
      SBD: 1,
      SEK: 1,
      SGD: 1,
      SHP: 1,
      SLL: 1,
      STD: 1,
      THB: 1,
      TJS: 1,
      TOP: 1,
      TTD: 1,
      TWD: 1,
      TZS: 1,
      UAH: 1,
      UGX: 1,
      USD: 1,
      UYU: 1,
      UZS: 1,
      VND: 1000,
      VUV: 100,
      WST: 1,
      XAF: 100,
      XCD: 1,
      XOF: 100,
      XPF: 100,
      YER: 100,
    }[mpg.currency] || 1;

  mpg.roundingMoney = (money = 0, standard = mpg.roundingStandard) => {
    const frac = money - Math.floor(money);
    if (standard > 1) return Math.ceil(money / standard) * standard;
    if (frac > standard) return Math.ceil(money) + standard;
    if (frac < standard) return Math.floor(money) + standard;
    return money;
  };

  mpg.formatMoney = (cents, format = mpg.moneyFormat) => {
    if (typeof cents == 'string') cents = cents.replace('.', '');

    let value = '';
    const placeholderRegex = /\{\{\s*(\w+)\s*\}\}/;
    const formatString = format || this.money_format;

    const defaultOption = (opt, def) => {
      return typeof opt == 'undefined' ? def : opt;
    };

    const formatWithDelimiters = (number, precision, thousands, decimal) => {
      precision = defaultOption(precision, 2);
      thousands = defaultOption(thousands, ',');
      decimal = defaultOption(decimal, '.');

      if (isNaN(number) || number == null) return 0;

      number = (number / 100.0).toFixed(precision);

      const parts = number.split('.');
      const dollars = parts[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1' + thousands);
      const cents = parts[1] ? decimal + parts[1] : '';

      return dollars + cents;
    };

    switch (formatString.match(placeholderRegex)[1]) {
      case 'amount':
        value = formatWithDelimiters(cents, 2);
        break;
      case 'amount_no_decimals':
        value = formatWithDelimiters(cents, 0);
        break;
      case 'amount_with_comma_separator':
        value = formatWithDelimiters(cents, 2, '.', ',');
        break;
      case 'amount_no_decimals_with_comma_separator':
        value = formatWithDelimiters(cents, 0, '.', ',');
        break;
    }

    const formattedMoney = formatString.replace(placeholderRegex, value);

    if (window.Shopify.shop === 'evereve-prod.myshopify.com') {
      if (formattedMoney.endsWith('.00')) return formattedMoney.slice(0, -3);
    }

    return formattedMoney;
  };

  mpg.updatePrice = (priceElement, price) => {
    if (!priceElement || price === undefined) return;

    let format = mpg.moneyFormat;
    let priceValue = Number(price);

    if (mpg.currencyRate !== 1) {
      priceValue = mpg.roundingMoney(priceValue * mpg.currencyRate);
    }

    if (priceElement.innerText.includes(mpg.currency)) {
      format = mpg.moneyWithCurrencyFormat;
    }

    priceElement.innerText = mpg.stripHTML(mpg.formatMoney(priceValue * 100, format));
  };

  mpg.updatePriceTemplate = (oldPriceEl, regularPrice, salePrice = null) => {
    if (!oldPriceEl || regularPrice === undefined) return;

    let format = mpg.moneyFormat;
    let regularPriceValue = Number(regularPrice);
    let salePriceValue = Number(salePrice);

    if (mpg.currencyRate !== 1) {
      regularPriceValue = mpg.roundingMoney(regularPriceValue * mpg.currencyRate);
      salePriceValue = mpg.roundingMoney(salePriceValue * mpg.currencyRate);
    }

    if (oldPriceEl.innerText.includes(mpg.currency)) {
      format = mpg.moneyWithCurrencyFormat;
    }

    const haveSalePrice = salePrice && salePrice < regularPrice;

    let newPriceTemplate = mpg.themeSetting.product_card.price_regular_template;
    if (haveSalePrice) newPriceTemplate = mpg.themeSetting.product_card.price_sale_template;

    newPriceTemplate = newPriceTemplate.replace(
      '[MPG_REGULAR_PRICE]',
      mpg.formatMoney(regularPriceValue * 100, format)
    );
    if (haveSalePrice)
      newPriceTemplate = newPriceTemplate.replace('[MPG_SALE_PRICE]', mpg.formatMoney(salePriceValue * 100, format));

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = newPriceTemplate;
    const newPriceElement = tempDiv.firstElementChild;

    oldPriceEl.replaceWith(newPriceElement);
  };

  mpg.updateProductImage = (imageElement, imageUrl, imageType) => {
    if (!imageElement || !imageUrl) return;

    if (imageType === 'image') {
      imageElement.src = imageUrl;
      imageElement.srcset = imageUrl;
    } else if (imageType === 'picture') {
      // Xử lý picture nếu cần
    } else if (imageType === 'background') {
      imageElement.style.backgroundImage = `url("${imageUrl}")`;
    }
  };

  mpg.setupDropdownHandlers = (dropdownTriggerElements) => {
    if (!dropdownTriggerElements || !dropdownTriggerElements.length) return;

    dropdownTriggerElements.forEach((dte) => {
      dte.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        mpg.toggleDropdown(dte);
      });

      // Ghi chú: Phương pháp này có thể gây vấn đề, nhưng giữ nguyên theo yêu cầu
      const originalAddEventListener = dte.addEventListener;
      dte.addEventListener = function (type, listener, options) {
        if (type === 'click') return;
        originalAddEventListener.call(dte, type, listener, options);
      };
    });
  };

  mpg.moveDropdownsToOverlay = (containerSelector, overlaySelector = '.mpg-overlay') => {
    const overlayEl = document.querySelector(overlaySelector);
    if (!overlayEl) return;

    const dropdownWrapperEls = document.querySelectorAll(containerSelector);
    dropdownWrapperEls.forEach((dropdownWrapperEl) => {
      overlayEl.appendChild(dropdownWrapperEl);
    });
  };

  mpg.setupOptionHoverHandlers = (optionSetElement) => {
    const labelValueEl = optionSetElement.querySelector('.mpg-swatch__label-value');
    if (!labelValueEl) return;

    const originalValue = labelValueEl.innerText;
    const optionEls = optionSetElement.querySelectorAll('.mpg-swatch__option');

    optionEls.forEach((oe) => {
      const optionValue = oe.getAttribute('o-value');

      oe.addEventListener('mouseenter', () => {
        labelValueEl.innerText = optionValue;
      });

      oe.addEventListener('mouseleave', () => {
        const currentSelected = oe.parentElement.querySelector('.mpg-swatch__option.selected');
        if (currentSelected) {
          labelValueEl.innerText = currentSelected.getAttribute('o-value');
        } else {
          labelValueEl.innerText = originalValue;
        }
      });
    });

    return { optionEls, originalValue, labelValueEl };
  };

  // render variant
  mpg.variantSitOnCardThrone = (variantHTML, product) => {
    const throneCards = [...mpg.getCardsByProduct(product)];

    throneCards.forEach((throneCard) => {
      let willReplace = false;
      const swatchLinker = Math.random().toString();
      const linkedHTML = variantHTML.replaceAll('[MTM_SWATCH_LINKER]', swatchLinker);

      const existedVariant = throneCard.querySelector('.mpg-swatch.mpg-variant.mpg-card');
      if (existedVariant) willReplace = true;

      const idealThrones = mpg.themeSetting.product_card.thrones;
      idealThrones.unshift({
        id: '.mpg-swatch',
        hide: false,
        sit_pos: 'afterend',
      });

      for (const throne of idealThrones) {
        const throneEl = throneCard.querySelector(throne.id);
        if (throneEl) {
          if (willReplace) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = linkedHTML;
            const variantEl = tempDiv.firstElementChild;
            existedVariant.replaceWith(variantEl);
          } else {
            throneEl.insertAdjacentHTML(throne.sit_pos, linkedHTML);
          }
          break;
        }
      }
    });
  };

  mpg.renderVariantCardContent = (product) => {
    // when hover variant button, change label
    let fullProd = window.sessionStorage.getItem(`mpg_stored_product[${product.handle}]`);
    if (!fullProd) return;
    fullProd = JSON.parse(fullProd);

    const CARD_ID = mpg.themeSetting.product_card.card_id;
    const variantEls = document.querySelectorAll(`.mpg-swatch.mpg-variant[p-handle="${product.handle}"]`);

    variantEls.forEach((variantEl) => {
      const prodCardEl = variantEl.closest(CARD_ID);
      const swatchLinker = variantEl.getAttribute('sw-linker');
      const dropdownTriggerEls = prodCardEl.querySelectorAll('.mpg-card .mpg-swatch__dropdown-trigger');

      mpg.setupDropdownHandlers(dropdownTriggerEls);

      const optionSetEls = variantEl.querySelectorAll(`.mpg-swatch__option-set`);
      optionSetEls.forEach((ose) => {
        const LINK_ID = mpg.themeSetting.product_card.link_id;
        const PRICE_WRAPPER_ID = mpg.themeSetting.product_card.price_wrapper_id;
        const PRICE_REGULAR_ID = mpg.themeSetting.product_card.price_regular_id;
        const PRICE_SALE_ID = mpg.themeSetting.product_card.price_sale_id;
        const IMAGE_1_ID = mpg.themeSetting.product_card.image_1_id;
        const IMAGE_2_ID = mpg.themeSetting.product_card.image_2_id;

        const dropdownTriggerEl = ose.querySelector('.mpg-swatch__dropdown-trigger');
        const { optionEls } = mpg.setupOptionHoverHandlers(ose) || {};
        if (!optionEls) return;

        optionEls.forEach((oe) => {
          const optionName = oe.getAttribute('o-name');
          const optionValue = oe.getAttribute('o-value');

          oe.addEventListener('click', (event) => {
            event.preventDefault();
            event.stopPropagation();

            const activeValueSet = [
              ...document.querySelectorAll(
                `.mpg-swatch__nav[sw-linker="${swatchLinker}"] .mpg-swatch__option.selected`
              ),
            ].map((el) => {
              const value = el.getAttribute('o-value');
              const option = el.getAttribute('o-name');
              if (option === optionName) return optionValue;
              return value;
            });

            const activeVariant = fullProd.variants.nodes.find((variant) =>
              activeValueSet.every((value) => variant.title.split(' / ').includes(value))
            );

            if (!activeVariant) return;

            const BASE_LINK_ID = mpg.themeSetting.product_card.link_id;
            const baseLinkEl = prodCardEl.querySelector(BASE_LINK_ID);

            if (mpg.merchantConfig?.change_variant_on_card_behavior === 'redirect') {
              console.log(mpg.merchantConfig?.change_variant_on_card_behavior, 'redirect');
              if (!baseLinkEl) return;
              let variantUrlStr = baseLinkEl.href;
              if (!variantUrlStr.includes(window.location.origin)) {
                variantUrlStr = window.location.origin + variantUrlStr;
              }
              const variantUrl = new URL(variantUrlStr);
              const variantId = activeVariant.id.split('gid://shopify/ProductVariant/')[1];
              variantUrl.searchParams.set('variant', variantId);
              variantUrlStr = variantUrl.toString();

              window.location.href = variantUrlStr;
              return;
            }

            if (oe.classList.contains('selected')) {
              if (dropdownTriggerEl) mpg.hideDropdown(dropdownTriggerEl);
              return;
            }

            // all possible value set when change option value
            const futureValueSets = [];

            fullProd.options.forEach((o, idx) => {
              o.values.forEach((v) => {
                let tmpValueSet = [...activeValueSet];
                tmpValueSet[idx] = v;
                futureValueSets.push(tmpValueSet);
              });
            });

            futureValueSets.push(activeValueSet);

            futureValueSets.forEach((valueSet) => {
              const matchVariant = fullProd.variants.nodes.find((variant) =>
                valueSet.every((value) => variant.title.split(' / ').includes(value))
              );

              valueSet.forEach((value, idx) => {
                const option = fullProd.options[idx]?.name;
                const fixedOption = option.replaceAll('"', '\\"');
                const fixedValue = value.replaceAll('"', '\\"');

                const matchEl = document.querySelector(
                  `.mpg-swatch__nav[sw-linker="${swatchLinker}"] .mpg-swatch__option[o-name="${fixedOption}"][o-value="${fixedValue}"]`
                );
                if (!matchEl) return;
                matchEl.classList.remove('blocked', 'disabled');
                if (!matchVariant && value !== activeValueSet[idx]) matchEl.classList.add('blocked');
                if (!matchVariant?.availableForSale) matchEl.classList.add('disabled');

                // browser dropdown
                const matchEl2 = document.querySelector(
                  `.mpg-swatch__nav[sw-linker="${swatchLinker}"] option[o-value="${fixedValue}"]`
                );
                if (!matchEl2) return;
                matchEl2.removeAttribute('disabled');
                if (!matchVariant && value !== activeValueSet[idx]) matchEl2.setAttribute('disabled', true);
              });
            });

            // re-render selected option
            optionEls.forEach((e) => e.classList.remove('selected'));
            originalValue = optionValue;
            oe.classList.add('selected');

            if (dropdownTriggerEl) {
              dropdownTriggerEl.innerText = optionValue;
              mpg.hideDropdown(dropdownTriggerEl);
            }

            if (prodCardEl) {
              // dynamic url
              const linkEls = prodCardEl.querySelectorAll(LINK_ID);
              linkEls.forEach((e) => {
                let variantUrlStr = e.href;

                if (!variantUrlStr.includes(window.location.origin)) {
                  variantUrlStr = window.location.origin + variantUrlStr;
                }

                const variantUrl = new URL(variantUrlStr);
                const variantId = activeVariant.id.split('gid://shopify/ProductVariant/')[1];
                variantUrl.searchParams.set('variant', variantId);

                variantUrlStr = variantUrl.toString();
                e.href = variantUrlStr;
              });

              // dynamic price
              let priceRegular = Number(activeVariant.compareAtPrice?.amount);
              let priceSale = Number(activeVariant.price.amount);
              console.log('priceRegular', priceRegular, priceSale);

              if (!priceRegular) {
                priceRegular = priceSale;
                priceSale = null;
              }

              // new method to update price
              if (PRICE_WRAPPER_ID) {
                const oldPriceEl = prodCardEl.querySelector(PRICE_WRAPPER_ID);

                if (oldPriceEl) {
                  mpg.updatePriceTemplate(oldPriceEl, priceRegular, priceSale || null);
                }
              }
              // old method to update price
              else {
                if (priceRegular) {
                  const priceRegularEls = prodCardEl.querySelectorAll(PRICE_REGULAR_ID);
                  priceRegularEls.forEach((e) => mpg.updatePrice(e, priceRegular));
                }
                if (priceSale) {
                  const priceSaleEls = prodCardEl.querySelectorAll(PRICE_SALE_ID);
                  priceSaleEls.forEach((e) => mpg.updatePrice(e, priceSale));
                }
              }

              // dynamic images
              const activeVariantImage =
                activeVariant?.image?.url ||
                fullProd?.featuredImage?.url ||
                'https://placehold.co/500x500/EEE/31343C?font=oswald&text=NO%20IMAGE';

              const img1El = prodCardEl.querySelector(IMAGE_1_ID);
              mpg.updateProductImage(img1El, activeVariantImage, mpg.themeSetting.product_card.image_1_type);

              const img2El = prodCardEl.querySelector(IMAGE_2_ID);
              mpg.updateProductImage(img2El, activeVariantImage, mpg.themeSetting.product_card.image_2_type);
            }

            if (mpg.handleVariantOnCardChange && typeof mpg.handleVariantOnCardChange === 'function') {
              mpg.handleVariantOnCardChange({
                prodCardEl,
                prod: fullProd,
                variant: activeVariant,
              });
            }
          });
        });
      });

      // move dropdown to overlay element
      mpg.moveDropdownsToOverlay(
        '.mpg-swatch.mpg-variant.mpg-card .mpg-swatch__dropdown-wrapper',
        '.mpg-overlay .mpg-card'
      );
    });
  };

  mpg.renderVariantCard = async (product, index) => {
    if (
      (!mpg.merchantConfig.swatch_enabled || !mpg.featureLimitation.unlocked_swatch) &&
      !mpg.appSettings.showSwatchOnProductCard &&
      !mpg.appSettings.showOptionsOnProductCard
    )
      return;

    let fullProd = await mpg.getProductByHandle(product.handle);
    window.mpgFullProd = fullProd;

    if (!fullProd) return;

    const activeVariantId = fullProd.selectedOrFirstAvailableVariant.id;
    const activeVariant = fullProd.variants.nodes.find((variant) => variant.id === activeVariantId);
    const activeValueSet = activeVariant.title.split(' / ');

    const variantElStr = `
      <div class="mpg-swatch mpg-variant mpg-card" p-handle="${product.handle}" sw-linker="[MTM_SWATCH_LINKER]">
        ${fullProd.options
          .map((go) => {
            const isSwatch = mpg.isSwatchOption(go.name);

            if (!mpg.merchantConfig.swatch_enabled || !mpg.featureLimitation.unlocked_swatch) {
              if (isSwatch && !mpg.appSettings.showSwatchOnProductCard) return '';
              if (!isSwatch && !mpg.appSettings.showOptionsOnProductCard) return '';
            }

            const template = mpg.getTemplateForOption(go.name, true);

            const activeValue = go.values.find((v) => activeValueSet.includes(v));
            const dropdownLinker = Math.random().toString();
            const hideLabel = mpg.merchantConfig.hide_card_option_label;

            let isGrouped = false;

            if (go.values.length === 1) {
              // check if this option is a grouped option or not
              const matchGroup = mpg.groups.find((g) => g.products.find((p) => p.handle === product.handle));
              if (matchGroup) {
                const matchOption = matchGroup.options.find((o) => o.name === go.name);
                if (matchOption) isGrouped = true;
              }
            }

            return `
            <div class="${template} mpg-swatch__option-set" os-type="group-option" os-name="${go.name.replaceAll('"', '&quot;')}" style="${isGrouped ? 'display: none;' : ''}">
              <legend class="${template} mpg-swatch__label" style="${hideLabel ? 'display: none;' : ''}">
                <span class="mpg-swatch__label-option">${go.name}</span>
                <span class="mpg-swatch__label-value">${activeValue}</span>
              </legend>
              ${template.includes('mpg-dropdown') ? `<div class="${template} mpg-swatch__dropdown-trigger" drd-linker="${dropdownLinker}">${activeValue}</div>` : ''}
              ${template.includes('mpg-dropdown') ? `<div class="${template} mpg-swatch__dropdown-wrapper" drd-linker="${dropdownLinker}">` : ''}
                <div class="${template} mpg-swatch__nav" sw-linker="[MTM_SWATCH_LINKER]">
                  ${
                    !template.includes('mpg-dropdown') &&
                    mpg.merchantConfig?.card_style?.value_limit_display_position === 'left' &&
                    mpg.merchantConfig?.card_style?.value_limit_max < go.values.length
                      ? `
                      <div class="mpg-swatch__nav-limit" onclick="window.location.pathname='/products/${product.handle}'" tabindex="0" onkeydown="if(event.key==='Enter') this.click();">
                        +${go.values.length - mpg.merchantConfig?.card_style?.value_limit_max}
                      </div>
                  `
                      : ''
                  }
                  
                  ${go.values
                    .map((v, iv) => {
                      const currValueSet = [...activeValueSet];
                      const replaceValueIndex = currValueSet.findIndex((value) => go.values.includes(value));
                      if (replaceValueIndex !== -1) currValueSet[replaceValueIndex] = v;

                      const matchVariant = fullProd.variants.nodes.find((variant) =>
                        currValueSet.every((value) => variant.title.split(' / ').includes(value))
                      );

                      const matchSwatch = mpg.swatchConfigs?.find(
                        (config) =>
                          mpg.isSimilarStrStrict(mpg.t(config.option), mpg.t(go.name)) &&
                          mpg.isSimilarStrStrict(mpg.t(config.value), mpg.t(v))
                      ) || {
                        style: 'variant_image',
                        color1: '#000',
                        color2: '#fff',
                        image: 'https://placehold.co/500x500/EEE/31343C?font=oswald&text=NO%20IMAGE',
                      };

                      matchSwatch.variant_image =
                        matchVariant?.image?.url ||
                        fullProd?.featuredImage?.url ||
                        'https://placehold.co/500x500/EEE/31343C?font=oswald&text=NO%20IMAGE';

                      const isActive = activeValueSet[replaceValueIndex] === v;

                      let inner = v;
                      if (template.includes('mpg-swatch')) {
                        if (matchSwatch.style === 'color1')
                          inner = `<div style="background: ${matchSwatch.color1}">${v}</div>`;
                        else if (matchSwatch.style === 'color2')
                          inner = `<div style="background-image: -webkit-linear-gradient(0, ${matchSwatch.color1} 50%, ${matchSwatch.color2} 50%)">${v}</div>`;
                        else if (matchSwatch.style === 'custom_image')
                          inner = `<div style="background-image: url('${matchSwatch.image}')">${v}</div>`;
                        else if (matchSwatch.style === 'variant_image')
                          inner = `<div style="background-image: url('${matchSwatch.variant_image}')">${v}</div>`;
                      }

                      if (
                        ['mpg-swatch-circle-button', 'mpg-swatch-square-button', 'mpg-swatch-image-button'].includes(
                          template
                        )
                      )
                        inner += `<label>${v}</label>`;

                      const classes = [template, 'mpg-swatch__option'];
                      if (isActive) classes.push('selected');
                      if (!matchVariant) classes.push('blocked');
                      if (!matchVariant?.availableForSale) classes.push('disabled');
                      if (template?.includes('mpg-swatch')) classes.push('mpg-tooltip-container');

                      const styles = [];
                      if (
                        !template.includes('mpg-dropdown') &&
                        mpg.merchantConfig?.card_style?.value_limit_display_position !== 'hide' &&
                        iv + 1 > mpg.merchantConfig?.card_style?.value_limit_max
                      )
                        styles.push('display: none;');

                      return `
                      <span tabindex="0" style="${styles.join(' ')}" class="${classes.join(' ')}" o-name="${go.name.replaceAll('"', '&quot;')}" o-value="${v.replaceAll('"', '&quot;')}" onkeydown="if(event.key==='Enter') this.click();">
                        ${template?.includes('mpg-swatch') ? `<span class="mpg-tooltip">${v}</span>` : ''}
                        ${inner}
                      </span>
                    `;
                    })
                    .join('')}
                  
                  ${
                    !template.includes('mpg-dropdown') &&
                    mpg.merchantConfig?.card_style?.value_limit_display_position === 'right' &&
                    mpg.merchantConfig?.card_style?.value_limit_max < go.values.length
                      ? `
                      <div class="mpg-swatch__nav-limit" onclick="window.location.pathname='/products/${product.handle}'" tabindex="0" onkeydown="if(event.key==='Enter') this.click();">
                        +${go.values.length - mpg.merchantConfig?.card_style?.value_limit_max}
                      </div>
                  `
                      : ''
                  }
                </div>
              ${template.includes('mpg-dropdown') ? `</div>` : ''}
              ${
                template === 'mpg-dropdown' && mpg.merchantConfig.browser_select_box_enabled
                  ? `
                <select
                  class="mpg-dropdown-browser mpg-swatch__nav"
                  os-type="group-option"
                  os-name="${go.name.replaceAll('"', '&quot;')}"
                  sw-linker="[MTM_SWATCH_LINKER]"
                  drd-linker="${dropdownLinker}"
                  onchange="mpg.handleBrowserSelectBoxChange(this)"
                >
                  ${go.values
                    .map((v) => {
                      const currValueSet = [...activeValueSet];
                      const replaceValueIndex = currValueSet.findIndex((value) => go.values.includes(value));
                      if (replaceValueIndex !== -1) currValueSet[replaceValueIndex] = v;

                      const matchVariant = fullProd.variants.nodes.find((variant) =>
                        currValueSet.every((value) => variant.title.split(' / ').includes(value))
                      );

                      const isActive = activeValueSet[replaceValueIndex] === v;
                      const isDisable = !matchVariant?.availableForSale;
                      return `
                      <option
                        value="${v.replaceAll('"', '&quot;')}"
                        ${isActive ? 'selected' : ''}
                        ${isDisable ? 'disabled' : ''}
                      >
                        ${v.replaceAll('"', '&quot;')}
                      </option>
                    `;
                    })
                    .join('')}
                </select>
              `
                  : ''
              }
            </div>
          `;
          })
          .join('')}
      </div>
    `;

    mpg.variantSitOnCardThrone(variantElStr, product);
    mpg.renderVariantCardContent(product);
  };

  mpg.changePriceAdvanced = async (handle, priceRegularEls, priceSaleEls) => {
    try {
      const sectionId = 'template--24437413609845__tapita-product-info';
      const url =
        window.location.origin +
        window.Shopify.routes.root +
        `collections/all?sections=${sectionId}&mtm_product_handle=${handle}`;
      const response = await fetch(url);
      const data = await response.json();

      let priceStr = data?.[sectionId];
      priceStr = priceStr?.match(/<div style="display: none">\s*([\s\S]*?)\s*<\/div>/)?.[1];
      const priceData = JSON.parse(priceStr);

      let priceRegular = priceData?.compare_at_price;
      let priceSale = priceData?.price;

      if (!priceRegular) {
        priceRegular = priceSale;
        priceSale = null;
      }

      if (priceRegular) {
        priceRegularEls.forEach((e) => {
          e.innerText = priceRegular;
        });
      }

      if (priceSale) {
        priceSaleEls.forEach((e) => {
          e.innerText = priceSale;
        });
      }
    } catch (error) {
      console.log('Error in mpg.changePriceAdvanced', error);
    }
  };

  // let the swatch sit on its throne
  mpg.getCardsByProduct = (product) => {
    const CARD_ID = mpg.themeSetting.product_card.card_id;
    const LINK_ID = mpg.themeSetting.product_card.link_id;

    const cards = [...document.querySelectorAll(CARD_ID)];

    const matchCards = cards.filter((c) => {
      const link = c.querySelector(LINK_ID);
      return mpg.getHandle(link?.href) === product.handle;
    });

    return matchCards;
  };

  // insert swatch
  mpg.sitOnCardThrone = (swatch, product) => {
    const throneCards = mpg.getCardsByProduct(product);

    throneCards.forEach((throneCard) => {
      const existedSwatch = throneCard.querySelector('.mpg-swatch.mpg-card');
      if (existedSwatch) return;

      for (const throne of mpg.themeSetting.product_card.thrones) {
        const throneEl = throneCard.querySelector(throne.id);
        if (throneEl) {
          throneEl.insertAdjacentHTML(throne.sit_pos, swatch);
          break;
        }
      }
    });
  };

  // render swatch content
  mpg.renderSwatchCardContent = (product) => {
    // when hover variant button, change label
    const group = mpg.groups.find((g) => g.products.some((p) => mpg.t(p.handle) === product.handle));
    if (!group) return;

    const CARD_ID = mpg.themeSetting.product_card.card_id;
    const swatchEls = document.querySelectorAll(`.mpg-swatch:not(.mpg-variant)[p-handle="${product.handle}"]`);

    swatchEls.forEach((swatchEl) => {
      const prodCardEl = swatchEl.closest(CARD_ID);
      const swatchLinker = swatchEl.getAttribute('sw-linker');
      const dropdownTriggerEls = prodCardEl.querySelectorAll('.mpg-card .mpg-swatch__dropdown-trigger');

      mpg.setupDropdownHandlers(dropdownTriggerEls);

      const optionSetEls = swatchEl.querySelectorAll(`.mpg-swatch__option-set`);
      optionSetEls.forEach((ose) => {
        const TITLE_ID = mpg.themeSetting.product_card.title_id;
        const LINK_ID = mpg.themeSetting.product_card.link_id;
        const PRICE_WRAPPER_ID = mpg.themeSetting.product_card.price_wrapper_id;
        const PRICE_REGULAR_ID = mpg.themeSetting.product_card.price_regular_id;
        const PRICE_SALE_ID = mpg.themeSetting.product_card.price_sale_id;
        const IMAGE_1_ID = mpg.themeSetting.product_card.image_1_id;
        const IMAGE_2_ID = mpg.themeSetting.product_card.image_2_id;

        const dropdownTriggerEl = ose.querySelector('.mpg-swatch__dropdown-trigger');
        const { optionEls } = mpg.setupOptionHoverHandlers(ose) || {};
        if (!optionEls) return;

        optionEls.forEach((oe) => {
          const valueId = oe.getAttribute('v-id');
          const optionName = oe.getAttribute('o-name');
          const optionValue = oe.getAttribute('o-value');

          oe.addEventListener('click', (event) => {
            event.preventDefault();
            event.stopPropagation();

            const activeValueEls = [
              ...document.querySelectorAll(
                `.mpg-swatch__nav[sw-linker="${swatchLinker}"] .mpg-swatch__option.selected`
              ),
            ];
            const activeValues = activeValueEls.map((el) => {
              const id = el.getAttribute('v-id');
              const option = el.getAttribute('o-name');
              if (option === optionName) return { option, value: valueId };
              return { option, value: id };
            });

            const activeValueSet = [];

            group.options.forEach((option) => {
              const matchValue = activeValues.find((value) => value.option === option.name);
              activeValueSet.push(matchValue?.value);
            });

            const matchProd = mpg.findMatchingProduct(activeValueSet, group);
            let alternateProd = matchProd;

            // base link element to redirect
            const BASE_LINK_ID = mpg.themeSetting.product_card.link_id;
            let baseLinkEl = prodCardEl.querySelector(BASE_LINK_ID);

            if (oe.classList.contains('selected')) {
              if (dropdownTriggerEl) mpg.hideDropdown(dropdownTriggerEl);

              if (mpg.merchantConfig?.change_variant_on_card_behavior === 'redirect' && baseLinkEl && alternateProd) {
                const newHref = mpg.swapProductHandle(baseLinkEl.href, mpg.t(alternateProd.handle));
                window.location.href = newHref;
              }

              return;
            }

            if (!alternateProd) {
              alternateProd = mpg.findMatchingProduct(activeValueSet, group, valueId);
            }
            if (!alternateProd) return;

            if (mpg.merchantConfig?.change_variant_on_card_behavior === 'redirect') {
              if (!baseLinkEl) return;
              const newHref = mpg.swapProductHandle(baseLinkEl.href, mpg.t(alternateProd.handle));
              window.location.href = newHref;
              return;
            }

            activeValueEls.forEach((el) => {
              el.classList.remove('selected');
            });

            const alternateActiveValueSet = alternateProd.options;

            // all possible value set when change option value
            const futureValueSets = [];

            group.options.forEach((o, idx) => {
              o.values.forEach((v) => {
                let tmpActiveValueSet = [...alternateActiveValueSet];
                tmpActiveValueSet[idx] = v.id;
                futureValueSets.push(tmpActiveValueSet);
              });
            });

            futureValueSets.push(alternateActiveValueSet);

            futureValueSets.forEach((valueSet) => {
              const matchProd = group.products.find((p) => p.options.every((o) => valueSet.includes(o)));

              valueSet.forEach((value, idx) => {
                const fixedValue = value.replaceAll('"', '\\"');

                const matchEl = document.querySelector(
                  `.mpg-swatch__nav[sw-linker="${swatchLinker}"] .mpg-swatch__option[v-id="${fixedValue}"]`
                );
                if (!matchEl) return;

                matchEl.classList.remove('disabled', 'selected');
                if (matchProd && value === alternateActiveValueSet[idx]) {
                  matchEl.classList.add('selected');

                  let labelValueEl;
                  let dropdownTriggerEl;
                  const dropdownWrapperEl = matchEl.closest('[drd-linker]');

                  if (dropdownWrapperEl) {
                    dropdownTriggerEl = document.querySelector(
                      `[drd-linker="${dropdownWrapperEl.getAttribute('drd-linker')}"]`
                    );
                    labelValueEl = dropdownTriggerEl
                      ?.closest('.mpg-swatch__option-set')
                      ?.querySelector('.mpg-swatch__label-value');
                  } else {
                    labelValueEl = matchEl
                      ?.closest('.mpg-swatch__option-set')
                      ?.querySelector('.mpg-swatch__label-value');
                  }

                  if (labelValueEl) labelValueEl.innerText = matchEl.getAttribute('o-value');
                  if (dropdownTriggerEl) dropdownTriggerEl.innerText = matchEl.getAttribute('o-value');
                }
                if (!matchProd?.available) matchEl.classList.add('disabled');

                // browser dropdown
                const matchEl2 = document.querySelector(
                  `.mpg-swatch__nav[sw-linker="${swatchLinker}"] option[v-id="${fixedValue}"]`
                );
                if (!matchEl2) return;
                matchEl2.removeAttribute('disabled');
                if (!matchProd && value !== alternateActiveValueSet[idx]) matchEl2.setAttribute('disabled', true);
              });
            });

            // re-render selected option
            optionEls.forEach((e) => e.classList.remove('selected'));
            originalValue = optionValue;
            oe.classList.add('selected');

            if (dropdownTriggerEl) {
              dropdownTriggerEl.innerText = optionValue;
              mpg.hideDropdown(dropdownTriggerEl);
            }

            if (prodCardEl) {
              // dynamic title
              const titleEls = prodCardEl.querySelectorAll(TITLE_ID);
              titleEls.forEach((e) => {
                e.innerText = mpg.t(alternateProd.title);
              });

              // dynamic url
              const linkEls = prodCardEl.querySelectorAll(LINK_ID);
              linkEls.forEach((e) => {
                const newHref = mpg.swapProductHandle(e.href, mpg.t(alternateProd.handle));
                e.href = newHref;
              });

              // dynamic price
              const priceRegularEls = prodCardEl.querySelectorAll(PRICE_REGULAR_ID);
              const priceSaleEls = prodCardEl.querySelectorAll(PRICE_SALE_ID);

              let priceRegular = alternateProd.regular_price;
              let priceSale = alternateProd.sale_price;

              if (!priceRegular) {
                priceRegular = priceSale;
                priceSale = null;
              }

              // custom method to update price
              if (window.Shopify.shop === 'finlay-uk.myshopify.com') {
                mpg.changePriceAdvanced(alternateProd?.handle, priceRegularEls, priceSaleEls);
              }
              // new method to update price
              else if (PRICE_WRAPPER_ID) {
                const oldPriceEl = prodCardEl.querySelector(PRICE_WRAPPER_ID);

                if (oldPriceEl) {
                  mpg.updatePriceTemplate(oldPriceEl, priceRegular, priceSale || null);
                }
              }
              // old method to update price
              else {
                if (priceRegular) {
                  priceRegularEls.forEach((e) => mpg.updatePrice(e, priceRegular));
                }
                if (priceSale) {
                  priceSaleEls.forEach((e) => mpg.updatePrice(e, priceSale));
                }
              }

              // dynamic images
              const img1El = prodCardEl.querySelector(IMAGE_1_ID);
              mpg.updateProductImage(img1El, alternateProd.images[0], mpg.themeSetting.product_card.image_1_type);

              const img2El = prodCardEl.querySelector(IMAGE_2_ID);
              mpg.updateProductImage(
                img2El,
                alternateProd.images[1] || alternateProd.images[0],
                mpg.themeSetting.product_card.image_2_type
              );
            }

            // render variant
            mpg.renderVariantCard(alternateProd);

            if (mpg.handleSwatchOnCardChange && typeof mpg.handleSwatchOnCardChange === 'function') {
              mpg.handleSwatchOnCardChange({
                group,
                prodCardEl,
                prod: alternateProd,
              });
            }
          });
        });
      });

      // move dropdown to overlay element
      mpg.moveDropdownsToOverlay(
        '.mpg-swatch:not(.mpg-variant).mpg-card .mpg-swatch__dropdown-wrapper',
        '.mpg-overlay .mpg-card'
      );
    });
  };

  // render swatch and add to DOM
  mpg.renderSwatchCard = (product) => {
    const group = mpg.groups.find((g) => g.products.some((p) => mpg.t(p.handle) === product.handle));
    if (!group) return;

    const activeProd = group.products.find((p) => mpg.t(p.handle) === product.handle);
    const swatchLinker = Math.random().toString();

    const groupElStr = `
          <div class="mpg-swatch mpg-card" p-handle="${product.handle}" sw-linker="${swatchLinker}">
            ${group.options
              .map((go) => {
                const template = go.template_card;
                const valueIds = go.values.map((v) => v.id);
                const activeValue = go.values.find((v) => activeProd.options.includes(v.id));
                const dropdownLinker = Math.random().toString();
                const hideLabel = mpg.merchantConfig.hide_card_option_label;

                return `
                <div class="${template} mpg-swatch__option-set" os-type="group-option" os-name="${mpg.t(go.name).replaceAll('"', '&quot;')}">
                  <legend class="${template} mpg-swatch__label" style="${hideLabel ? 'display: none;' : ''}">
                    <span class="mpg-swatch__label-option">${mpg.t(go.name)}</span>
                    <span class="mpg-swatch__label-value">${mpg.t(activeValue.name)}</span>
                  </legend>
                  ${template.includes('mpg-dropdown') ? `<div class="${template} mpg-swatch__dropdown-trigger" drd-linker="${dropdownLinker}">${mpg.t(activeValue.name)}</div>` : ''}
                  ${template.includes('mpg-dropdown') ? `<div class="${template} mpg-swatch__dropdown-wrapper" drd-linker="${dropdownLinker}">` : ''}
                    <div class="${template} mpg-swatch__nav" sw-linker="${swatchLinker}">
                      ${
                        !template.includes('mpg-dropdown') &&
                        mpg.merchantConfig?.card_style?.value_limit_display_position === 'left' &&
                        mpg.merchantConfig?.card_style?.value_limit_max < go.values.length
                          ? `
                          <div class="mpg-swatch__nav-limit" onclick="window.location.pathname='/products/${product.handle}'" tabindex="0" onkeydown="if(event.key==='Enter') this.click();">
                            +${go.values.length - mpg.merchantConfig?.card_style?.value_limit_max}
                          </div>
                      `
                          : ''
                      }
      
                      ${go.values
                        .map((v, i) => {
                          const activeValues = [...activeProd.options];
                          const replaceValueIndex = activeValues.findIndex((o) => valueIds.includes(o));
                          if (replaceValueIndex !== -1) activeValues[replaceValueIndex] = v.id;

                          const matchProd = mpg.findMatchingProduct(activeValues, group);

                          let alternateProd = matchProd;

                          if (!alternateProd) {
                            alternateProd = mpg.findMatchingProduct(activeValues, group, v.id);
                          }

                          const isActive = activeProd.options.includes(v.id);

                          let inner = mpg.t(v.name);
                          if (template.includes('mpg-swatch')) {
                            if (v.style === 'color1')
                              inner = `<div style="background: ${v.color1}">${mpg.t(v.name)}</div>`;
                            else if (v.style === 'color2')
                              inner = `<div style="background-image: -webkit-linear-gradient(0, ${v.color1} 50%, ${v.color2} 50%)">${mpg.t(v.name)}</div>`;
                            else if (v.style === 'first_image')
                              inner = `<div style="background-image: url('${v.image}')">${mpg.t(v.name)}</div>`;
                            else if (v.style === 'variant_image')
                              inner = `<div style="background-image: url('${v.image}')">${mpg.t(v.name)}</div>`;
                            else if (v.style === 'custom_image')
                              inner = `<div style="background-image: url('${v.image_custom}')">${mpg.t(v.name)}</div>`;
                          }

                          if (
                            [
                              'mpg-swatch-circle-button',
                              'mpg-swatch-square-button',
                              'mpg-swatch-image-button',
                            ].includes(template)
                          )
                            inner += `<label>${mpg.t(v.name)}</label>`;

                          const classes = [template, 'mpg-swatch__option'];
                          if (isActive) classes.push('selected');
                          if (!alternateProd) classes.push('blocked');
                          if (!matchProd?.available) classes.push('disabled');
                          if (template?.includes('mpg-swatch')) classes.push('mpg-tooltip-container');

                          const styles = [];
                          if (
                            !template.includes('mpg-dropdown') &&
                            mpg.merchantConfig?.card_style?.value_limit_display_position !== 'hide' &&
                            i + 1 > mpg.merchantConfig?.card_style?.value_limit_max
                          )
                            styles.push('display: none;');

                          return `
                          <span tabindex="0" style="${styles.join(' ')}" class="${classes.join(' ')}" o-name="${mpg.t(go.name).replaceAll('"', '&quot;')}" o-value="${mpg.t(v.name).replaceAll('"', '&quot;')}" v-id="${v.id}" onkeydown="if(event.key==='Enter') this.click();">
                            ${template?.includes('mpg-swatch') ? `<span class="mpg-tooltip">${mpg.t(v.name)}</span>` : ''}
                            ${inner}
                          </span>
                        `;
                        })
                        .join('')}
                      
                      ${
                        !template.includes('mpg-dropdown') &&
                        mpg.merchantConfig?.card_style?.value_limit_display_position === 'right' &&
                        mpg.merchantConfig?.card_style?.value_limit_max < go.values.length
                          ? `
                          <div class="mpg-swatch__nav-limit" onclick="window.location.pathname='/products/${product.handle}'" tabindex="0" onkeydown="if(event.key==='Enter') this.click();">
                            +${go.values.length - mpg.merchantConfig?.card_style?.value_limit_max}
                          </div>
                      `
                          : ''
                      }
                    </div>
                  ${template.includes('mpg-dropdown') ? `</div>` : ''}
                  ${
                    template === 'mpg-dropdown' && mpg.merchantConfig.browser_select_box_enabled
                      ? `
                    <select
                      class="mpg-dropdown-browser mpg-swatch__nav"
                      os-type="group-option"
                      os-name="${mpg.t(go.name).replaceAll('"', '&quot;')}"
                      sw-linker="${swatchLinker}"
                      drd-linker="${dropdownLinker}"
                      onchange="mpg.handleBrowserSelectBoxChange(this)"
                    >
                      ${go.values
                        .map((v) => {
                          const activeValues = [...activeProd.options];
                          const replaceValueIndex = activeValues.findIndex((o) => valueIds.includes(o));
                          if (replaceValueIndex !== -1) activeValues[replaceValueIndex] = v.id;
                          const matchProd = group.products.find((p) =>
                            p.options.every((o) => activeValues.includes(o))
                          );

                          const isActive = activeProd.options.includes(v.id);
                          const isDisable = !matchProd;
                          return `
                          <option
                            value="${mpg.t(v.name).replaceAll('"', '&quot;')}"
                            v-id="${v.id}"
                            ${isActive ? 'selected' : ''}
                            ${isDisable ? 'disabled' : ''}
                          >
                            ${mpg.t(v.name).replaceAll('"', '&quot;')}
                          </option>
                        `;
                        })
                        .join('')}
                    </select>
                  `
                      : ''
                  }
                </div>
              `;
              })
              .join('')}
          </div>
        `;

    mpg.sitOnCardThrone(groupElStr, product);
    mpg.renderSwatchCardContent(product);
  };

  // render swatch and add to DOM
  mpg.renderSwatchProd = () => {
    if (!mpg.group.options) return;
    const existedSwatch = document.querySelector('.mpg-swatch:not(.mpg-card)');
    if (existedSwatch) return;

    let leftOptions = mpg.product.options.filter(
      (o) => o.values.length !== 1 || !['Title', 'Titre', 'Titel', 'Titula'].includes(o.name)
    );
    const activeProd = mpg.group.products.find((p) => p.id === mpg.product.id);
    const swatchLinker = Math.random().toString();

    const mapper = {};
    mpg.group.options.forEach((option) => {
      option.values.forEach((value) => {
        mapper[value.id] = value.name;
      });
    });

    combinations = mpg.genCombinations(mpg.group.options);

    const groupElStr = `
      <div class="mpg-swatch" group-id="${mpg.group.id}">
        ${mpg.group.options
          .map((go) => {
            leftOptions = leftOptions.filter((lo) => mpg.t(lo.name) !== mpg.t(go.name) || lo.values.length >= 2);
            const template = go.template_prod;
            const valueIds = go.values.map((v) => v.id);
            const activeValue = go.values.find((v) => activeProd.options.includes(v.id));
            const dropdownLinker = Math.random().toString();
            const valueLinker = Math.random().toString();

            return `
            <div class="${template} mpg-swatch__option-set" os-type="group-option" os-name="${mpg.t(go.name).replaceAll('"', '&quot;')}">
              <legend class="${template} mpg-swatch__label">
                <span class="mpg-swatch__label-option">${mpg.t(go.name)}</span>
                <span class="mpg-swatch__label-value">${mpg.t(activeValue.name)}</span>
              </legend>
              ${template.includes('mpg-dropdown') ? `<div class="${template} mpg-swatch__dropdown-trigger" drd-linker="${dropdownLinker}">${mpg.t(activeValue.name)}</div>` : ''}
              ${template.includes('mpg-dropdown') ? `<div class="${template} mpg-swatch__dropdown-wrapper" drd-linker="${dropdownLinker}">` : ''}
                <div class="${template} mpg-swatch__nav" os-type="group-option" os-name="${mpg.t(go.name).replaceAll('"', '&quot;')}" sw-linker="${swatchLinker}">
                  ${
                    !template.includes('mpg-dropdown') &&
                    mpg.merchantConfig?.prod_style?.value_limit_display_position === 'left' &&
                    mpg.merchantConfig?.prod_style?.value_limit_max < go.values.length
                      ? `
                      <div class="mpg-swatch__nav-limit" v-linker="${valueLinker}" onclick="mpg.toggleShowValue('${valueLinker}', ${go.values.length})" tabindex="0" onkeydown="if(event.key==='Enter') this.click();">
                        +${go.values.length - mpg.merchantConfig?.prod_style?.value_limit_max}
                      </div>
                  `
                      : ''
                  }
                
                  ${go.values
                    .map((v, i) => {
                      const activeValues = [...activeProd.options];
                      const replaceValueIndex = activeValues.findIndex((o) => valueIds.includes(o));
                      if (replaceValueIndex !== -1) activeValues[replaceValueIndex] = v.id;

                      const matchProd = mpg.findMatchingProduct(activeValues, mpg.group);
                      let alternateProd = matchProd;

                      if (!alternateProd) {
                        alternateProd = mpg.findMatchingProduct(activeValues, mpg.group, v.id);
                      }

                      const urlProd = mpg.swapProductHandle(window.location.href, mpg.t(alternateProd?.handle));
                      const isActive = activeProd.options.includes(v.id);

                      let inner = mpg.t(v.name);
                      if (template.includes('mpg-swatch')) {
                        if (v.style === 'color1') inner = `<div style="background: ${v.color1}">${mpg.t(v.name)}</div>`;
                        else if (v.style === 'color2')
                          inner = `<div style="background-image: -webkit-linear-gradient(0, ${v.color1} 50%, ${v.color2} 50%)">${mpg.t(v.name)}</div>`;
                        else if (v.style === 'first_image')
                          inner = `<div style="background-image: url('${v.image}')">${mpg.t(v.name)}</div>`;
                        else if (v.style === 'variant_image')
                          inner = `<div style="background-image: url('${v.image}')">${mpg.t(v.name)}</div>`;
                        else if (v.style === 'custom_image')
                          inner = `<div style="background-image: url('${v.image_custom}')">${mpg.t(v.name)}</div>`;
                      }

                      if (
                        ['mpg-swatch-circle-button', 'mpg-swatch-square-button', 'mpg-swatch-image-button'].includes(
                          template
                        )
                      )
                        inner += `<label>${mpg.t(v.name)}</label>`;

                      const classes = [template, 'mpg-swatch__option'];
                      if (isActive) {
                        classes.push('selected');
                        matchProd.available = mpg.product.available;
                      }
                      if (!alternateProd) classes.push('blocked');
                      if (!matchProd?.available) classes.push('disabled');
                      if (template?.includes('mpg-swatch')) classes.push('mpg-tooltip-container');

                      const styles = [];
                      if (
                        !template.includes('mpg-dropdown') &&
                        mpg.merchantConfig?.prod_style?.value_limit_display_position !== 'hide' &&
                        i + 1 > mpg.merchantConfig?.prod_style?.value_limit_max
                      )
                        styles.push('display: none;');

                      if (isActive || !alternateProd)
                        return `
                          <span tabindex="0" v-index="${i}" v-linker="${valueLinker}" style="${styles.join(' ')}" class="${classes.join(' ')}" o-name="${mpg.t(go.name).replaceAll('"', '&quot;')}" o-value="${mpg.t(v.name).replaceAll('"', '&quot;')}" v-id="${v.id}">
                            ${template?.includes('mpg-swatch') ? `<span class="mpg-tooltip">${mpg.t(v.name)}</span>` : ''}
                            ${inner}
                          </span>
                        `;

                      return `
                        <a tabindex="0" v-index="${i}" v-linker="${valueLinker}" style="${styles.join(' ')}" class="${classes.join(' ')}" href="${urlProd}" o-name="${mpg.t(go.name).replaceAll('"', '&quot;')}" o-value="${mpg.t(v.name).replaceAll('"', '&quot;')}" v-id="${v.id}">
                          ${template?.includes('mpg-swatch') ? `<span class="mpg-tooltip">${mpg.t(v.name)}</span>` : ''}
                          ${inner}
                        </a>
                      `;
                    })
                    .join('')}
                  
                  ${
                    !template.includes('mpg-dropdown') &&
                    mpg.merchantConfig?.prod_style?.value_limit_display_position === 'right' &&
                    mpg.merchantConfig?.prod_style?.value_limit_max < go.values.length
                      ? `
                        <div class="mpg-swatch__nav-limit" v-linker="${valueLinker}" onclick="mpg.toggleShowValue('${valueLinker}', ${go.values.length})" tabindex="0" onkeydown="if(event.key==='Enter') this.click();">
                          +${go.values.length - mpg.merchantConfig?.prod_style?.value_limit_max}
                        </div>
                      `
                      : ''
                  }
                </div>
              ${template.includes('mpg-dropdown') ? `</div>` : ''}
              ${
                template === 'mpg-dropdown' && mpg.merchantConfig.browser_select_box_enabled
                  ? `
                      <select
                        class="mpg-dropdown-browser mpg-swatch__nav"
                        os-type="group-option"
                        os-name="${mpg.t(go.name).replaceAll('"', '&quot;')}"
                        sw-linker="${swatchLinker}"
                        drd-linker="${dropdownLinker}"
                        onchange="mpg.handleBrowserSelectBoxChange(this)"
                      >
                        ${go.values
                          .map((v) => {
                            const activeValues = [...activeProd.options];
                            const replaceValueIndex = activeValues.findIndex((o) => valueIds.includes(o));
                            if (replaceValueIndex !== -1) activeValues[replaceValueIndex] = v.id;
                            const matchProd = mpg.group.products.find((p) =>
                              p.options.every((o) => activeValues.includes(o))
                            );

                            const isActive = activeProd.options.includes(v.id);
                            const isDisable = !matchProd;
                            return `
                              <option
                                value="${mpg.t(v.name).replaceAll('"', '&quot;')}"
                                v-id="${v.id}"
                                ${isActive ? 'selected' : ''}
                                ${isDisable ? 'disabled' : ''}
                              >
                                ${mpg.t(v.name).replaceAll('"', '&quot;')}
                              </option>
                            `;
                          })
                          .join('')}
                      </select>
                    `
                  : ''
              }
            </div>
          `;
          })
          .join('')}

        ${leftOptions
          .map((lo) => {
            if (
              !mpg.appSettings.redesignVariantPickerOnProductPage &&
              !mpg.merchantConfig.override_grouped_product_option &&
              !mpg.merchantConfig.swatch_enabled
            )
              return '';

            const dropdownLinker = Math.random().toString();
            const valueLinker = Math.random().toString();
            const template = mpg.getTemplateForOption(lo.name, false);

            return `
              <div class="${template} mpg-swatch__option-set" os-type="shopify-option" os-name="${lo.name.replaceAll('"', '&quot;')}">
                <legend class="${template} mpg-swatch__label">
                  <span class="mpg-swatch__label-option">${lo.name}</span>
                  <span class="mpg-swatch__label-value"></span>
                </legend>
                ${template.includes('mpg-dropdown') ? `<div class="${template} mpg-swatch__dropdown-trigger" drd-linker="${dropdownLinker}"></div>` : ''}
                ${template.includes('mpg-dropdown') ? `<div class="${template} mpg-swatch__dropdown-wrapper" drd-linker="${dropdownLinker}">` : ''}
                <div class="${template} mpg-swatch__nav" os-type="shopify-option" os-name="${lo.name.replaceAll('"', '&quot;')}" sw-linker="${swatchLinker}">
                  ${
                    !template.includes('mpg-dropdown') &&
                    mpg.merchantConfig?.prod_style?.value_limit_display_position === 'left' &&
                    mpg.merchantConfig?.prod_style?.value_limit_max < lo.values.length
                      ? `
                      <div class="mpg-swatch__nav-limit" v-linker="${valueLinker}" onclick="mpg.toggleShowValue('${valueLinker}', ${lo.values.length})" tabindex="0" onkeydown="if(event.key==='Enter') this.click();">
                        +${lo.values.length - mpg.merchantConfig?.prod_style?.value_limit_max}
                      </div>
                  `
                      : ''
                  }
                
                  ${lo.values
                    .map((v, i) => {
                      const matchSwatch = mpg.swatchConfigs?.find(
                        (config) =>
                          mpg.isSimilarStrStrict(mpg.t(config.option), mpg.t(lo.name)) &&
                          mpg.isSimilarStrStrict(mpg.t(config.value), mpg.t(v))
                      ) || {
                        style: 'variant_image',
                        color1: '#000',
                        color2: '#fff',
                        image: 'https://placehold.co/200x200/EEE/31343C?font=oswald&text=NO%20IMAGE',
                      };

                      const firstMatchVariant = mpg.product.variants.find((variant) => variant.options.includes(v));
                      matchSwatch.variant_image =
                        firstMatchVariant?.featured_image?.src ||
                        firstMatchVariant?.featured_media?.preview_image?.src ||
                        mpg.product.featured_image ||
                        'https://placehold.co/200x200/EEE/31343C?font=oswald&text=NO%20IMAGE';
                      if (
                        !matchSwatch.variant_image?.includes('width=') &&
                        !matchSwatch.variant_image?.includes('placehold')
                      )
                        matchSwatch.variant_image += '&width=200';

                      let inner = v;
                      if (template.includes('mpg-swatch')) {
                        if (matchSwatch.style === 'color1')
                          inner = `<div style="background: ${matchSwatch.color1}">${v}</div>`;
                        else if (matchSwatch.style === 'color2')
                          inner = `<div style="background-image: -webkit-linear-gradient(0, ${matchSwatch.color1} 50%, ${matchSwatch.color2} 50%)">${v}</div>`;
                        else if (matchSwatch.style === 'custom_image')
                          inner = `<div style="background-image: url('${matchSwatch.image}')">${v}</div>`;
                        else if (matchSwatch.style === 'variant_image')
                          inner = `<div style="background-image: url('${matchSwatch.variant_image}')">${v}</div>`;
                      }

                      if (
                        ['mpg-swatch-circle-button', 'mpg-swatch-square-button', 'mpg-swatch-image-button'].includes(
                          template
                        )
                      )
                        inner += `<label>${v}</label>`;

                      const classes = [template, 'mpg-swatch__option'];
                      if (template?.includes('mpg-swatch')) classes.push('mpg-tooltip-container');

                      const styles = [];
                      if (
                        !template.includes('mpg-dropdown') &&
                        mpg.merchantConfig?.prod_style?.value_limit_display_position !== 'hide' &&
                        i + 1 > mpg.merchantConfig?.prod_style?.value_limit_max
                      )
                        styles.push('display: none;');

                      return `
                        <span tabindex="0" v-index="${i}" v-linker="${valueLinker}" style="${styles.join(' ')}" class="${classes.join(' ')}" o-name="${lo.name.replaceAll('"', '&quot;')}" o-value="${v.replaceAll('"', '&quot;')}" onclick="mpg.handleLeftOptionChange(event)" onkeydown="if(event.key==='Enter') this.click();">
                          ${template?.includes('mpg-swatch') ? `<span class="mpg-tooltip">${v}</span>` : ''}
                          ${inner}
                        </span>
                      `;
                    })
                    .join('')}

                  
                  ${
                    !template.includes('mpg-dropdown') &&
                    mpg.merchantConfig?.prod_style?.value_limit_display_position === 'right' &&
                    mpg.merchantConfig?.prod_style?.value_limit_max < lo.values.length
                      ? `
                      <div class="mpg-swatch__nav-limit" v-linker="${valueLinker}" onclick="mpg.toggleShowValue('${valueLinker}', ${lo.values.length})" onkeydown="if(event.key==='Enter') this.click();" tabindex="0" onkeydown="if(event.key==='Enter') this.click();">
                        +${lo.values.length - mpg.merchantConfig?.prod_style?.value_limit_max}
                      </div>
                  `
                      : ''
                  }
                </div>
                ${template.includes('mpg-dropdown') ? `</div>` : ''}
                ${
                  template === 'mpg-dropdown' && mpg.merchantConfig.browser_select_box_enabled
                    ? `
                  <select
                    class="mpg-dropdown-browser mpg-swatch__nav"
                    os-type="shopify-option"
                    os-name="${lo.name.replaceAll('"', '&quot;')}"
                    sw-linker="${swatchLinker}"
                    drd-linker="${dropdownLinker}"
                    onchange="mpg.handleBrowserSelectBoxChange(this)"
                  >
                    ${lo.values
                      .map((v) => {
                        return `
                        <option value="${v.replaceAll('"', '&quot;')}">
                          ${v.replaceAll('"', '&quot;')}
                        </option>
                      `;
                      })
                      .join('')}
                  </select>
                `
                    : ''
                }
              </div>
            `;
          })
          .join('')}

          ${
            !mpg.merchantConfig.removed_trademark
              ? `
              <div style="text-align: right !important; font-size: 14px !important; display: block !important; visibility: visible !important; opacity: 1 !important; height: auto !important; width: auto !important; overflow: visible !important; position: static !important; clip: auto !important; clip-path: none !important; transform: none !important; filter: none !important; z-index: auto !important;">
                Powered by 
                <a
                  href="https://apps.shopify.com/grouptify-combined-listings"
                  target="_blank"
                  style="color: #E86046; font-weight: 600; text-decoration: underline"
                >
                  Grouptify
                </a>
              </div>
            `
              : ''
          }
      </div>
    `;

    for (const throne of mpg.themeSetting.product_page.thrones) {
      const throneEl = document.querySelector(throne.id);
      if (throneEl) {
        throneEl.insertAdjacentHTML(throne.sit_pos, groupElStr);
        break;
      }
    }
  };

  // render swatch content (original option)
  mpg.renderSwatchProdContent = () => {
    if (!mpg.group.options) return;
    const selectedVariantId =
      Number(new URLSearchParams(location.search).get('variant')) || Number(mpg.product.selected_variant_id);
    const selectedVariant = mpg.product.variants.find((pv) => pv.id === selectedVariantId) || mpg.product.variants[0];
    const selectedValues = mpg.product.options.map((po, i) => selectedVariant.options[i]);

    const dropdownTriggerEls = document.querySelectorAll('.mpg-swatch:not(.mpg-card) .mpg-swatch__dropdown-trigger');
    mpg.setupDropdownHandlers(dropdownTriggerEls);

    mpg.product.options.forEach((pv, i1) => {
      const optionEls = [
        ...document.querySelectorAll(
          `.mpg-swatch__nav[os-type=shopify-option][os-name="${pv.name}"] .mpg-swatch__option`
        ),
      ];

      optionEls.forEach((oe) => {
        const optionValue = oe.getAttribute('o-value');
        const selectedValue = selectedVariant.options[i1];
        const values = [...selectedValues];
        values[i1] = optionValue;

        const variant = mpg.product.variants.find((pv) => pv.options.every((option) => values.includes(option)));

        if (optionValue === selectedValue) {
          let selectedLabelEl = oe.parentNode.parentNode.querySelector('.mpg-swatch__label-value');
          let dropdownTriggerEl;

          if (!selectedLabelEl) {
            // dropdown
            const dropdownWrapperEl = oe.parentNode.parentNode;
            const dropdownLinker = dropdownWrapperEl?.getAttribute('drd-linker');
            dropdownTriggerEl = document.querySelector(`[drd-linker="${dropdownLinker}"]`);
            selectedLabelEl = dropdownTriggerEl?.parentNode?.querySelector('.mpg-swatch__label-value');
          }

          if (dropdownTriggerEl) dropdownTriggerEl.innerText = optionValue;
          if (selectedLabelEl) selectedLabelEl.innerText = optionValue;

          if (!oe.classList.contains('selected')) {
            oe.classList.add('selected');
            const fixedSelectedValue = selectedValue.replaceAll('"', '\\"');
            // browser dropdown
            const boe = document.querySelector(
              `.mpg-swatch__nav[os-type=shopify-option][os-name="${pv.name.replaceAll('"', '\\"')}"] option[value="${fixedSelectedValue}"]`
            );
            if (boe) boe.setAttribute('selected', true);
          }
        } else oe.classList.remove('selected');

        if (!variant || !variant.available) {
          if (!oe.classList.contains('disabled')) oe.classList.add('disabled');
        } else oe.classList.remove('disabled');
      });
    });

    // when hover variant button, change label
    const optionSetEls = document.querySelectorAll('.mpg-swatch__option-set');
    optionSetEls.forEach((ose) => mpg.setupOptionHoverHandlers(ose));

    // move dropdown to overlay element
    mpg.moveDropdownsToOverlay('.mpg-swatch:not(.mpg-card) .mpg-swatch__dropdown-wrapper', '.mpg-overlay');
  };

  document.addEventListener('click', (event) => {
    if (
      event.target.classList.contains('mpg-swatch__dropdown-trigger') ||
      event.target.classList.contains('mpg-swatch__dropdown-wrapper')
    )
      return;

    const dropdownWrapperEls = document.querySelectorAll('.mpg-swatch__dropdown-wrapper');
    dropdownWrapperEls.forEach((dropdownWrapperEl) => dropdownWrapperEl.classList.remove('mpg-show'));
  });

  window.addEventListener('scroll', () => {
    const dropdownWrapperEls = document.querySelectorAll('.mpg-swatch__dropdown-wrapper');
    dropdownWrapperEls.forEach((dropdownWrapperEl) => dropdownWrapperEl.classList.remove('mpg-show'));
  });
};
