<!-- MTM VERSION: *********** -->
<script src='{{ 'swatch-scripts.js' | asset_url }}' type='text/javascript'></script>

{% comment %} POSITION TO RENDER DROPDOWN INTO {% endcomment %}
<div class='mpg-overlay'>
  <div class='mpg-card'></div>
</div>

{% comment %} FLYING PAGES CONFIG {% endcomment %}
{% if shop.metafields.mtm_product_group.merchant_config.value.app_enabled_2 %}
  <script>
    window.FPConfig = {
      delay: 0,
      maxRPS: 3,
      hoverDelay: 50,
      ignoreKeywords: {{ shop.metafields.mtm_product_group.merchant_config.value.fp_ignore_keywords | json }} || [],
    };
    if (!window.FPConfig.ignoreKeywords.length) {
      window.FPConfig.ignoreKeywords = ['/cart', '/account/login', '/account/logout', '/account', '/checkout'];
    }
  </script>
  <script src='{{ 'flying-pages.js' | asset_url }}' type='text/javascript' defer></script>
{% endif %}

{% comment %} CHECK CONDITION FOR SHOWING GROUP ON CARD OR NOT {% endcomment %}
{% if shop.metafields.mtm_product_group.merchant_config.value.app_enabled_1 %}
  {% assign mpg_show_on_card = false %}
  {% assign mpg_show_on_card_timeout = 0 %}
  {% assign mpg_show_on_basf = false %}

  {% if shop.metafields.mtm_product_group.feature_limitation.value.allowed_show_option_on_card %}
    {% if request.page_type == 'article'
      and shop.metafields.mtm_product_group.merchant_config.value.show_on_article == true
    %}
      {% assign mpg_show_on_card = true %}
      {% assign mpg_show_on_card_timeout = shop.metafields.mtm_product_group.merchant_config.value.timeout_article %}
    {% endif %}

    {% if request.page_type == 'blog'
      and shop.metafields.mtm_product_group.merchant_config.value.show_on_blog == true
    %}
      {% assign mpg_show_on_card = true %}
      {% assign mpg_show_on_card_timeout = shop.metafields.mtm_product_group.merchant_config.value.timeout_blog %}
    {% endif %}

    {% if request.page_type == 'cart'
      and shop.metafields.mtm_product_group.merchant_config.value.show_on_cart == true
    %}
      {% assign mpg_show_on_card = true %}
      {% assign mpg_show_on_card_timeout = shop.metafields.mtm_product_group.merchant_config.value.timeout_cart %}
    {% endif %}

    {% if request.page_type == 'collection'
      and shop.metafields.mtm_product_group.merchant_config.value.show_on_collection == true
    %}
      {% assign mpg_show_on_card = true %}
      {% assign mpg_show_on_card_timeout = shop.metafields.mtm_product_group.merchant_config.value.timeout_collection %}
    {% endif %}

    {% if request.page_type == 'index'
      and shop.metafields.mtm_product_group.merchant_config.value.show_on_home == true
    %}
      {% assign mpg_show_on_card = true %}
      {% assign mpg_show_on_card_timeout = shop.metafields.mtm_product_group.merchant_config.value.timeout_home %}
    {% endif %}

    {% if request.page_type == 'page'
      and shop.metafields.mtm_product_group.merchant_config.value.show_on_page == true
    %}
      {% assign mpg_show_on_card = true %}
      {% assign mpg_show_on_card_timeout = shop.metafields.mtm_product_group.merchant_config.value.timeout_page %}
    {% endif %}

    {% if request.page_type == 'product'
      and shop.metafields.mtm_product_group.merchant_config.value.show_on_product == true
    %}
      {% assign mpg_show_on_card = true %}
      {% assign mpg_show_on_card_timeout = shop.metafields.mtm_product_group.merchant_config.value.timeout_product %}
    {% endif %}

    {% if request.page_type == 'search'
      and shop.metafields.mtm_product_group.merchant_config.value.show_on_search == true
    %}
      {% assign mpg_show_on_card = true %}
      {% assign mpg_show_on_card_timeout = shop.metafields.mtm_product_group.merchant_config.value.timeout_search %}
    {% endif %}

    <!-- SHOW ON BASF CARD -->
    {% if request.page_type == 'collection'
      and shop.metafields.mtm_product_group.merchant_config.value.show_on_collection == true
      and shop.metafields.mtm_product_group.merchant_config.value.use_basf_app == true
    %}
      {% assign mpg_show_on_basf = true %}
      {% assign mpg_show_on_card_timeout = shop.metafields.mtm_product_group.merchant_config.value.timeout_basf_app %}
    {% endif %}

    {% if request.page_type == 'search'
      and shop.metafields.mtm_product_group.merchant_config.value.show_on_search == true
      and shop.metafields.mtm_product_group.merchant_config.value.use_basf_app == true
    %}
      {% assign mpg_show_on_basf = true %}
      {% assign mpg_show_on_card_timeout = shop.metafields.mtm_product_group.merchant_config.value.timeout_basf_app %}
    {% endif %}
  {% endif %}

  {% render 'swatch_styles', mpg_show_on_card: mpg_show_on_card %}

  {% comment %} GLOBAL OBJECT AND BASE FUNCTION {% endcomment %}
  <script>
    window.mpg = window.mpg || {};
    mpg.merchantConfig = {{ shop.metafields.mtm_product_group.merchant_config.value | json }} || {};
    mpg.themeSetting = {{ shop.metafields.mtm_product_group.theme_setting.value | json }} || {};
    mpg.featureLimitation = {{ shop.metafields.mtm_product_group.feature_limitation.value | json }} || {};
    mpg.optionConfigs = {{ shop.metafields.mtm_product_group.option_configs.value | json }} || [];
    mpg.swatchConfigs = {{ shop.metafields.mtm_product_group.swatch_configs.value | json }} || [];
    mpg.translation = {{ shop.metafields.mtm_product_group.translations.value | json }} || [];

    {% unless shop.metafields.mtm_product_group.feature_limitation.value.removed_trademark %}
      mpg.merchantConfig.removed_trademark = false;
    {% else %}
      mpg.merchantConfig.removed_trademark = true;
    {% endunless %}

    // embed block configs
    mpg.appSettings = {
      redesignVariantPickerOnProductPage: {{ block.settings.enable_swatch_product_page }},
      showSwatchOnProductCard: {{ block.settings.enable_swatch_product_card_1 }},
      showOptionsOnProductCard: {{ block.settings.enable_swatch_product_card_2 }},
      swatchIdentifiers: `{{ block.settings.swatch_option_identifiers }}`.split(/[\n,]+/).map(s => s.trim()).filter(Boolean),
    }

    if({{ mpg_show_on_basf }}) {
      mpg.themeSetting.product_card = {
        thrones: [{
          id: ".boost-sd__product-link:nth-child(2)",
          hide: false,
          sit_pos: "afterend",
        }],
        card_id: ".boost-sd__product-item",
        title_id: ".boost-sd__product-title",
        link_id: ".boost-sd__product-link",
        price_regular_id: ".boost-sd__format-currency",
        price_sale_id: ".boost-sd__product-price--sale .boost-sd__format-currency",
        image_1_id: ".boost-sd__product-image-img--main",
        image_2_id: ".boost-sd__product-image-img--second",
        image_1_type: "image",
        image_2_type: "image",
      }
    }
  </script>

  {% comment %} GET GROUPS FROM METAFIELD {% endcomment %}
  {% assign mpg_groups_first_chunk = shop.metafields.mtm_product_group.all_groups_1.value %}
  {% assign mpg_groups = mpg_groups_first_chunk.chunk %}
  {% for i in (2..mpg_groups_first_chunk.chunk_count) %}
    {% assign chunk_key = 'all_groups_' | append: i %}
    {% assign chunk = shop.metafields.mtm_product_group[chunk_key].value %}
    {% assign mpg_groups = mpg_groups | append: chunk.chunk %}
  {% endfor %}
  {% assign mpg_groups = mpg_groups | strip | replace: '"', '\"' | replace: '`', '\u0060' | replace: '\n', ' ' %}

  {% comment %} RENDER PRODUCT GROUP ON PROUDUCT CARD {% endcomment %}
  {% if mpg_show_on_card %}
    <script>
      mpg.groups = `{{ mpg_groups }}`.split('@matitmui@').filter(g => g).map(g => JSON.parse(g))
      mpg.groups.forEach(g => {
        g.products = g.products.filter(p => p?.id);
      })

      // format money
      mpg.currency = Shopify.currency.active;
      mpg.currencyRate = Number(Shopify.currency.rate)
      mpg.currencySymbol = "{{ shop.money_format | replace: '"', '\"' }}"
        .replaceAll('{', '[')
        .replaceAll('}', ']')
        .replace(/\s*\[\[.*?\]\]\s*/g, '')
        .replace(/\s+/g, ' ');
      mpg.moneyFormat = "{{ shop.money_format | replace: '"', '\"' }}"
      mpg.moneyWithCurrencyFormat = "{{ shop.money_with_currency_format | replace: '"', '\"' }}"
    </script>
  {% endif %}

  {% comment %} RENDER PRODUCT GROUP ON PRODUCT PAGE {% endcomment %}
  {% if template.name == 'product' %}
    <script>
      {% assign mpg_groups_arr = mpg_groups | split: '@matitmui@' %}
      {% capture mpg_group %}
        {% for mpg_g in mpg_groups_arr %}
          {% if mpg_g contains product.id %}
            {{ mpg_g }}
            {% break %}
          {% endif %}
        {% endfor %}
      {% endcapture %}
      {% assign mpg_group = mpg_group | strip %}

      mpg.group = JSON.parse(`{{ mpg_group }}` || '{"products":[]}');
      if (!mpg.group.options && ((mpg.merchantConfig.swatch_enabled && mpg.featureLimitation.unlocked_swatch) || mpg.appSettings.redesignVariantPickerOnProductPage)) {
        mpg.group.options = [];
      }
      mpg.group.products = mpg.group.products.filter(p => p?.id);

      // convert product to a format to process
      mpg.normalizeProduct = (product) => {
        const id = product.id.toString();
        const options = product.options.map((option, index) => {
          const values = product.variants
            .map(variant => variant[`option${index + 1}`])
            .filter((value, pos, self) => self.indexOf(value) === pos);
          return { name: option, values: values };
        });
        const selected_variant_id = `{{ product.selected_or_first_available_variant.id }}`;
        return { ...product, options, id, selected_variant_id };
      }
      mpg.product = mpg.normalizeProduct({{ product | json }});

      // two way binding for option value button
      mpg.getLeftOptionValueEl = {{ shop.metafields.mtm_product_group.theme_setting.value.product_page.option_value_button_getter }}

      // handle when click option button (left)
      mpg.handleLeftOptionChange = (e) => {
        const oName = e.target.getAttribute("o-name");
        const oValue = e.target.getAttribute("o-value");

        const optionValueEl = mpg.getLeftOptionValueEl(oName, oValue)
        if(optionValueEl) optionValueEl.click();

        const siblingEls = [...e.target.parentNode.children].filter((child) => child !== e.target);
        siblingEls.forEach((sibling) => sibling.classList.remove("selected"));

        if (!e.target.classList.contains("selected")) {
          e.target.classList.add("selected");
        }
      };
    </script>
  {% endif %}
{% endif %}

{% comment %} OVERRIDE ORIGINAL METHOD FOR RE-RENDERING GROUP {% endcomment %}
{% if template.name == 'product' or mpg_show_on_card == true %}
  <script>
    // override replace state
    mpg.overrideReplaceState = () => {
      const originalReplaceState = window.history.replaceState;

      window.history.replaceState = function(state, title, url) {
        originalReplaceState.apply(window.history, arguments);

        {% if template.name == 'product' %}
          window.setTimeout(() => {
            mpg.renderSwatchProd();
            mpg.renderSwatchProdContent();
          }, mpg.merchantConfig.timeout_location_prod || 0);
        {% endif %}

        {% if mpg_show_on_card %}
          window.setTimeout(() => {
            mpg.products = mpg.getProducts();
            mpg.products.forEach((product) => {
              mpg.renderSwatchCard(product);
              mpg.renderVariantCard(product);
            });
          }, mpg.merchantConfig.timeout_location_card || 0);
        {% endif %}
      };
    }

    // override push state
    mpg.overridePushState = () => {
      const originalPushState = window.history.pushState;

      window.history.pushState = function(state, title, url) {
        originalPushState.apply(window.history, arguments);

        {% if template.name == 'product' %}
          window.setTimeout(() => {
            mpg.renderSwatchProd();
            mpg.renderSwatchProdContent();
          }, mpg.merchantConfig.timeout_location_prod || 0);
        {% endif %}

        {% if mpg_show_on_card %}
          window.setTimeout(() => {
            mpg.products = mpg.getProducts();
            mpg.products.forEach((product) => {
              mpg.renderSwatchCard(product);
              mpg.renderVariantCard(product);
            });
          }, mpg.merchantConfig.timeout_location_card || 0);
        {% endif %}
      };
    }

    // override fetch
    mpg.overrideFetch = () => {
      const originalFetch = window.fetch;

      window.fetch = function (url, options) {
        {% if mpg_show_on_card %}
          const observe = typeof url === 'string' && mpg.merchantConfig.observe_urls.find(u => u.page === '{{ request.page_type }}' && url?.includes(u.url));
          if (observe) {
            return originalFetch.apply(this, arguments)
              .then((response) => {
                window.setTimeout(() => {
                  mpg.products = mpg.getProducts();
                  mpg.products.forEach((product) => {
                    mpg.renderSwatchCard(product);
                    mpg.renderVariantCard(product);
                  });
                }, observe.timeout)
                return response;
              })
              .catch((error) => {
                throw error;
              });
          }
        {% endif %}

        return originalFetch.apply(this, arguments);
      };
    }

    // override xhr
    mpg.overrideXhr = () => {
      const originalXhrOpen = window.XMLHttpRequest.prototype.open;
      const originalXhrSend = window.XMLHttpRequest.prototype.send;

      window.XMLHttpRequest.prototype.open = function (method, url) {
        this._url = url;
        originalXhrOpen.apply(this, arguments);
      };

      window.XMLHttpRequest.prototype.send = function (body) {
        const xhr = this;

        xhr.addEventListener('readystatechange', function () {
          if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
              {% if mpg_show_on_card %}
                const observe = mpg.merchantConfig.observe_urls.find(u => u.page === '{{ request.page_type }}' && xhr._url?.includes(u.url));
                if (observe) {
                  window.setTimeout(() => {
                    mpg.products = mpg.getProducts();
                    mpg.products.forEach((product) => {
                      mpg.renderSwatchCard(product);
                      mpg.renderVariantCard(product);
                    });
                  }, observe.timeout);
                }
              {% endif %}
            } else {
              console.error('XHR error');
            }
          }
        });

        originalXhrSend.apply(this, arguments);
      };
    };

    // Code for overriding methods
    window.setTimeout(mpg.overrideReplaceState, mpg.merchantConfig.timeout_override_replace_state || 0);
    window.setTimeout(mpg.overridePushState, mpg.merchantConfig.timeout_override_push_state || 0);
    window.setTimeout(mpg.overrideFetch, mpg.merchantConfig.timeout_override_fetch || 0);
    window.setTimeout(mpg.overrideXhr, mpg.merchantConfig.timeout_override_xhr || 0);
  </script>
{% endif %}

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Create a function to check for the existence of mpgInitAppScript
    function checkAndInitApp() {
      // Store all code that needs to be executed after initialization
      function executeAllDependentCode() {
        // Initialize the app
        window.mpgInitAppScript(mpg);

        // Code for product page
        {% if template.name == 'product' %}
          window.setTimeout(() => {
            mpg.renderSwatchProd();
            mpg.renderSwatchProdContent();
          }, mpg.merchantConfig.timeout_location_prod || 0);
        {% endif %}

        // Code for product card
        {% if mpg_show_on_card %}
          window.setTimeout(() => {
            mpg.products = mpg.getProducts();
            mpg.products.forEach((product) => {
              mpg.renderSwatchCard(product);
              mpg.renderVariantCard(product);
            });
          }, {{ mpg_show_on_card_timeout }});
        {% endif %}
      }

      // Check if mpgInitAppScript function exists
      if (typeof window.mpgInitAppScript === 'function') {
        // Function exists, execute dependent code
        executeAllDependentCode();

        // Clear interval as no further checks are needed
        clearInterval(initCheckInterval);
        console.log("MPG app successfully initialized");
      } else {
        console.log("Waiting for swatch-scripts.js to load...");
      }
    }

    // Create interval to check every 100ms
    const initCheckInterval = setInterval(checkAndInitApp, 100);

    // Set timeout to stop checking after 10 seconds (to avoid infinite loop)
    setTimeout(() => {
      if (typeof window.mpgInitAppScript !== 'function') {
        clearInterval(initCheckInterval);
        console.error("Could not load swatch-scripts.js after 10 seconds, please refresh the page");
      }
    }, 10000);
  });
</script>

{% schema %}
{
  "name": "Gtify: Combined Listings",
  "target": "body",
  "settings": [
    {
      "type": "header",
      "content": "Product Page"
    },
    {
      "type": "checkbox",
      "id": "enable_swatch_product_page",
      "label": "Restyle variant picker on product page",
      "default": false
    },
    {
      "type": "header",
      "content": "Product Card",
      "info": "Only works with the [Basic plan](https://www.tapita.io/pages/grouptify-combined-listings#shopify-section-template--18379972051099__tpt_pricing_pgrEMd) or higher."
    },
    {
      "type": "checkbox",
      "id": "enable_swatch_product_card_1",
      "label": "Show swatches on product cards",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "enable_swatch_product_card_2",
      "label": "Show non-swatch options on product cards",
      "default": false
    },
    {
      "type": "header",
      "content": "Swatch Config"
    },
    {
      "type": "textarea",
      "id": "swatch_option_identifiers",
      "label": "Swatch option labels",
      "info": "Option names to display as swatches, comma-separated. Should add translations if store is multilingual.",
      "default": "Color, Couleur, Colore, Farbe,\nPattern, Patrón, Motif, Muster, Motivo,\nMaterial, Matériau, Materiale,\nFinish, Finition, Finitura, Oberfläche, Acabado"
    }
  ]
}
{% endschema %}
