<style>
  .mpg-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    z-index: 99999;
    pointer-events: none;
  }
  .mpg-overlay .mpg-card {
    width: 100%;
    height: 100%;
  }
  .mpg-swatch__option.blocked {
    cursor: not-allowed !important;
  }

  .mpg-swatch__option [style*="background: white"],
  .mpg-swatch__option [style*="background: #fff"],
  .mpg-swatch__option [style*="background: #ffffff"],
  .mpg-swatch__option [style*="background: rgb(255, 255, 255)"],
  .mpg-swatch__option [style*="background: rgba(255, 255, 255"] {
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
  }

  .mpg-tooltip-container {
    position: relative;
  }

  .mpg-tooltip {
    pointer-events: none;
    position: absolute;
    bottom: calc(100% + 10px);
    left: 50%;
    transform: translateX(-50%);
    background-color: #555;
    color: white;
    text-align: center;
    padding: 2px 10px;
    border-radius: 40px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.1s;
  }

  .mpg-tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #555 transparent transparent transparent;
  }

  .mpg-tooltip-container:hover .mpg-tooltip {
    opacity: 1;
  }

  {% if shop.metafields.mtm_product_group.merchant_config.value.browser_select_box_enabled %}
    .mpg-dropdown-browser {
      padding: 10px;
      outline: none !important;
      box-shadow: none !important;
    }

    .mpg-dropdown-browser option {
      cursor: pointer;
    }

    .mpg-dropdown-browser option[disabled] {
      background: lightgray;
      color: white;
    }

    .mpg-card .mpg-dropdown-browser {
      padding: 5px;
    }

    .mpg-dropdown.mpg-swatch__dropdown-trigger,
    .mpg-dropdown.mpg-swatch__dropdown-wrapper {
      display: none !important;
    }
  {% endif %}

  {% if mpg_show_on_card == true %}
    {% assign mpg_card_thrones = shop.metafields.mtm_product_group.theme_setting.value.product_card.thrones | where: 'hide', true | map: 'id' %}

    {% capture mpg_card_thrones_selector %}
      {% for throne in mpg_card_thrones %}
        .mpg-swatch.mpg-card + {{ throne }},
      {% endfor %}
    {% endcapture %}

    {{ mpg_card_thrones_selector }} matitmui {
      display: none !important;
    }

    .mpg-swatch.mpg-card {
      width: 100%;
      z-index: 20;
      display: flex;
      position: relative;
      flex-direction: column;
      gap: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.option_spacing | default: 12 }}px !important;
      margin-top: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.margin_top | default: 12 }}px !important;
      margin-bottom: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.margin_bottom | default: 12 }}px !important;
    }

    .mpg-swatch.mpg-variant.mpg-card {
      margin-top: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.option_spacing | default: 12 }}px !important;
    }

    .mpg-card .mpg-swatch__option-set {
      display: flex;
      position: relative;
      flex-direction: column;
      gap: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.label_spacing | default: 4 }}px !important;
    }

    .mpg-card .mpg-swatch__nav-limit {
      cursor: pointer;
      color: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.value_limit_text_color | default: '#000000' }} !important;
      font-size: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.value_limit_font_size | default: 14 }}px !important;
      font-weight: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.value_limit_font_weight | default: 500 }} !important;
    }

    .mpg-card .mpg-swatch__label {
      {% if shop.metafields.mtm_product_group.merchant_config.value.card_style.show_label == false %}
        display: none;
      {% endif %}
    }

    .mpg-card .mpg-swatch__label-option {
      color: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.label_color | default: '#000000' }} !important;
      font-size: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.label_font_size | default: 12 }}px !important;
      font-weight: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.label_font_weight | default: 500 }} !important;
    }

    .mpg-card .mpg-swatch__label-value {
      {% if shop.metafields.mtm_product_group.merchant_config.value.card_style.show_label_value == false %}
        display: none;
      {% endif %}
      color: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.label_value_color | default: '#000000' }} !important;
      font-size: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.label_value_font_size | default: 12 }}px !important;
      font-weight: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.label_value_font_weight | default: 500 }} !important;
    }

    .mpg-card .mpg-swatch__label-value::before {
      {% if shop.metafields.mtm_product_group.merchant_config.value.card_style.show_label_value == false %}
        display: none;
      {% endif %}
      content: '{{ shop.metafields.mtm_product_group.merchant_config.value.card_style.label_value_connector | default: ': ' }}';
      color: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.label_color | default: '#000000' }} !important;
      font-size: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.label_font_size | default: 12 }}px !important;
      font-weight: {{ shop.metafields.mtm_product_group.merchant_config.value.card_style.label_font_weight | default: 500 }} !important;
    }

    {{ shop.metafields.mtm_product_group.style.value.card_css }}
    {{ shop.metafields.mtm_product_group.merchant_config.value.custom_card_css }}
    {{ shop.metafields.mtm_product_group.theme_setting.value.product_card.custom_css }}
  {% endif %}

  {% if request.page_type == 'product' %}
    {% assign mpg_page_thrones = shop.metafields.mtm_product_group.theme_setting.value.product_page.thrones | where: 'hide', true | map: 'id' %}

    {% capture mpg_page_thrones_selector %}
      {% for throne in mpg_page_thrones %}
        .mpg-swatch + {{ throne }},
      {% endfor %}
    {% endcapture %}

    {% unless shop.metafields.mtm_product_group.merchant_config.value.override_grouped_product_option == false %}
      {{ mpg_page_thrones_selector }} matitmui {
        display: none !important;
      }
    {% elsif shop.metafields.mtm_product_group.merchant_config.value.swatch_enabled %}
      {{ mpg_page_thrones_selector }} matitmui {
        display: none !important;
      }
    {% elsif block.settings.enable_swatch_product_page %}
      {{ mpg_page_thrones_selector }} matitmui {
        display: none !important;
      }
    {% else %}
      .mpg-swatch [os-type="group-option"] ~ *:not([os-type="group-option"]) {
        display: none;
      }
    {% endunless %}

    .mpg-swatch {
      width: 100%;
      z-index: 1;
      display: flex;
      position: relative;
      flex-direction: column;
      gap: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.option_spacing | default: 18 }}px !important;
      margin-top: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.margin_top | default: 18 }}px !important;
      margin-bottom: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.margin_bottom | default: 18 }}px !important;
    }

    .mpg-swatch__option-set {
      display: flex;
      position: relative;
      flex-direction: column;
      gap: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.label_spacing | default: 6 }}px !important;
    }

    .mpg-swatch__nav-limit {
      cursor: pointer;
      color: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.value_limit_text_color | default: '#000000' }} !important;
      font-size: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.value_limit_font_size | default: 16 }}px !important;
      font-weight: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.value_limit_font_weight | default: 500 }} !important;
    }

    .mpg-swatch__label {
      {% if shop.metafields.mtm_product_group.merchant_config.value.prod_style.show_label == false  %}
        display: none;
      {% endif %}
    }

    .mpg-swatch__label-option {
      color: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.label_color | default: '#000000' }} !important;
      font-size: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.label_font_size | default: 14 }}px !important;
      font-weight: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.label_font_weight | default: 500 }} !important;
    }

    .mpg-swatch__label-value {
      {% if shop.metafields.mtm_product_group.merchant_config.value.prod_style.show_label_value == false %}
        display: none;
      {% endif %}
      color: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.label_value_color | default: '#000000' }} !important;
      font-size: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.label_value_font_size | default: 14 }}px !important;
      font-weight: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.label_value_font_weight | default: 500 }} !important;
    }

    .mpg-swatch__label-value::before {
      {% if shop.metafields.mtm_product_group.merchant_config.value.prod_style.show_label_value == false %}
        display: none;
      {% endif %}
      content: '{{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.label_value_connector | default: ': ' }}' !important;
      color: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.label_color | default: '#000000' }} !important;
      font-size: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.label_font_size | default: 14 }}px !important;
      font-weight: {{ shop.metafields.mtm_product_group.merchant_config.value.prod_style.label_font_weight | default: 500 }} !important;
    }

    {{ shop.metafields.mtm_product_group.style.value.prod_css }}
    {{ shop.metafields.mtm_product_group.merchant_config.value.custom_prod_css }}
    {{ shop.metafields.mtm_product_group.theme_setting.value.product_page.custom_css }}
  {% endif %}
</style>
