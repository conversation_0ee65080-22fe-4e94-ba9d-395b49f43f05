# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "f3c7e2f5de24101b794516ae2211c449"
application_url = "https://grouptify.pwa-commerce.com/"
embedded = true
name = "G: Combined Listings"
handle = "grouptify-combined-listings"

[webhooks]
api_version = "2024-07"

  [[webhooks.subscriptions]]
  uri = "/api/webhooks/customers/data_request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "/api/webhooks/customers/redact"
  compliance_topics = [ "customers/redact" ]

  [[webhooks.subscriptions]]
  uri = "/api/webhooks/shop/redact"
  compliance_topics = [ "shop/redact" ]

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_channels,read_files,read_locales,read_metaobjects,read_products,read_themes,unauthenticated_read_product_listings,write_files,write_products"

[auth]
redirect_urls = [
  "https://grouptify.pwa-commerce.com/auth/callback",
  "https://grouptify.pwa-commerce.com/auth/shopify/callback",
  "https://grouptify.pwa-commerce.com/api/auth/callback"
]

[pos]
embedded = false

[build]
include_config_on_deploy = true
