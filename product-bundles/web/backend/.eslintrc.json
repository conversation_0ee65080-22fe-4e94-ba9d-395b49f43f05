{"env": {"node": true, "es2021": true}, "extends": [], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": [], "rules": {"no-console": "off", "semi": ["error", "always"], "quotes": ["error", "single"], "indent": ["error", 2], "comma-dangle": ["error", "only-multiline"], "no-unused-vars": ["warn", {"args": "all", "ignoreRestSiblings": true, "argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "no-undef": "error"}}