import { LATEST_API_VERSION } from '@shopify/shopify-api';
import shopify from '../../shopify.js';
import dotenv from 'dotenv';
import path from 'path';

dotenv.config();

const config = {
  env: {
    APP_URL: process.env.HOST,
    NODE_ENV: process.env.NODE_ENV,
    CLIENT_ID: process.env.CLIENT_ID,
    CLIENT_SECRET: process.env.CLIENT_SECRET,
    ADMIN_USERNAME: process.env.ADMIN_USERNAME,
    ADMIN_PASSWORD: process.env.ADMIN_PASSWORD,
    EXTENSION_ID: process.env.EXTENSION_ID,
    BACKEND_PORT: parseInt(process.env.BACKEND_PORT || process.env.PORT || '8080', 10),
    MONGODB_URI: process.env.MONGODB_URI,
    IS_TESTING: process.env.NODE_ENV === 'development',
    STATIC_PATH:
      process.env.NODE_ENV === 'production'
        ? path.resolve(process.cwd(), 'frontend', 'dist')
        : path.resolve(process.cwd(), 'frontend'),
  },
  shopify: {
    AUTH_PATH: shopify.config.auth.path,
    AUTH_CALLBACK_PATH: shopify.config.auth.callbackPath,
    WEBHOOK_PATH: shopify.config.webhooks.path,
    API_VERSION: LATEST_API_VERSION,
  },
};

export default config;

