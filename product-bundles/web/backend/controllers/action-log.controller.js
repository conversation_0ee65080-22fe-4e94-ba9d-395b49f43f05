import { ActionLogService } from '../services/_index.js';

class ActionLogController {
  constructor() {
    this.service = ActionLogService;
  }

  get = async (req, res) => {
    const shopId = req.session._id;
    const logs = await this.service.get({ ...req.query, shop: shopId });
    return res.status(200).json(logs);
  };

  search = async (req, res) => {
    const logs = await this.service.get({ ...req.query });
    return res.status(200).json(logs);
  };
}

export default new ActionLogController();

