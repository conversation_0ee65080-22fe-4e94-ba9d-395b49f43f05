import { AdminService, AuthService } from '../services/_index.js';
import { generateHash } from '../utils/generate-hash.js';

class AdminController {
  constructor() {
    this.adminService = AdminService;
    this.authService = AuthService;
  }

  login = async (req, res) => {
    try {
      const { username, password } = req.body;
      const hashedPass = generateHash(password);
      const admins = await this.adminService.get();
      const admin = admins.find((a) => a.username === username && a.password === hashedPass);

      if (!admin) {
        return res.status(401).json({ message: 'Username or password is not correct' });
      }

      const session = await this.authService.getSession({ id: admin.id, role: admin.role });
      return res.status(200).json(session);
      //
    } catch (error) {
      console.error('Error in AdminController.login: ', error);
      return res.status(500).json({ message: error.message });
    }
  };
}

export default new AdminController();

