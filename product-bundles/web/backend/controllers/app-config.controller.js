import { AppConfigService } from '../services/_index.js';

class AppConfigController {
  constructor() {
    this.service = AppConfigService;
  }

  get = async (_req, res) => {
    try {
      const data = await this.service.get();
      return res.status(200).json(data);
      //
    } catch (error) {
      console.error('Error in AppConfigController.get: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  update = async (req, res) => {
    try {
      const data = await this.service.upsert(req.body);
      return res.status(200).json(data);
      //
    } catch (error) {
      console.error('Error in AppConfigController.update: ', error);
      return res.status(500).json({ message: error.message });
    }
  };
}

export default new AppConfigController();

