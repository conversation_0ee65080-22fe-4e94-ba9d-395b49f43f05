import _ from 'lodash';
import { ShopService } from '../services/_index.js';
import { initGraphqlShopifyService } from '../services/shopify/graphql-shopify.service.js';

class AppDataController {
  constructor() {}

  getShop = async (req, res) => {
    try {
      const shopId = req.session._id;
      const shop = await ShopService.getById(shopId);
      if (!shop) return res.status(404).json({ message: 'Shop not found' });

      const shopObject = shop.toObject();
      delete shopObject.access_token;
      delete shopObject.access_scope;

      return res.status(200).json(shopObject);
      //
    } catch (error) {
      console.error('Error in AppDataController.getShop: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  getLocales = async (req, res) => {
    try {
      const shopId = req.session._id;
      const ShopifyGraphqlService = await initGraphqlShopifyService(shopId);
      const shopLocales = await ShopifyGraphqlService.getShopLocales();
      return res.status(200).json(shopLocales);
      //
    } catch (error) {
      console.error('Error in AppDataController.getLocales: ', error);
      return res.status(500).json({ message: error.message });
    }
  };
}

export default new AppDataController();

