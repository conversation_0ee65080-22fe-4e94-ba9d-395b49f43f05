class BaseController {
  /**
   * @param {BaseService} service - An instance of the BaseService.
   */
  constructor(service) {
    this.service = service;
  }

  // Handle GET requests to retrieve a list of documents
  get = async (req, res) => {
    try {
      req.query.page = req.query?.page || 1;
      req.query.page_size = req.query?.page_size || 10;
      const data = await this.service.get(req.query);
      return res.status(200).json(data);
      //
    } catch (error) {
      console.error('Error in BaseController.get: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  // Handle GET requests to retrieve a single document by ID
  getById = async (req, res) => {
    try {
      const data = await this.service.getById(req.params.id, {
        select: req.query.select,
        populate: req.query.populate,
      });

      if (!data) {
        return res.status(404).json({ message: 'Not found' });
      }

      return res.status(200).json(data);
      //
    } catch (error) {
      console.error('Error in BaseController.getById: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  // Handle POST requests to create a new document
  create = async (req, res) => {
    try {
      const data = await this.service.create(req.body);
      return res.status(201).json(data);
      //
    } catch (error) {
      console.error('Error in BaseController.create: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  // Handle PUT requests to update a document by ID
  updateById = async (req, res) => {
    try {
      const data = await this.service.updateById(req.params.id, req.body);

      if (!data) {
        return res.status(404).json({ message: 'Not found' });
      }

      return res.status(200).json(data);
      //
    } catch (error) {
      console.error('Error in BaseController.updateById: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  // Handle DELETE requests to remove a document by ID
  deleteById = async (req, res) => {
    try {
      const data = await this.service.deleteById(req.params.id);

      if (!data) {
        return res.status(404).json({ message: 'Not found' });
      }

      return res.status(200).json({ message: 'Deleted successfully' });
      //
    } catch (error) {
      console.error('Error in BaseController.deleteById: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  // Handle POST requests to duplicate a document by ID
  duplicateById = async (req, res) => {
    try {
      const originalDocument = await this.service.getById(req.params.id);

      if (!originalDocument) {
        return res.status(404).json({ message: 'Document not found' });
      }

      const duplicatedData = originalDocument.toObject();
      delete duplicatedData._id;
      delete duplicatedData.createdAt;
      delete duplicatedData.updatedAt;
      if (duplicatedData.name) duplicatedData.name = 'Copy of ' + duplicatedData.name;

      const duplicatedDocument = await this.service.create(duplicatedData);

      return res.status(201).json(duplicatedDocument);
      //
    } catch (error) {
      console.error('Error in BaseController.duplicateById: ', error);
      res.status(500).json({ message: error.message });
    }
  };
}

export default BaseController;

