import { DiscountService, ShopService } from '../services/_index.js';
import BaseController from './base/base.controller.js';

class DiscountController extends BaseController {
  constructor() {
    super(DiscountService);
    this.shopService = ShopService;
  }

  get = async (req, res) => {
    try {
      req.query.page = req.query?.page || 1;
      req.query.page_size = req.query?.page_size || 10000;
      const data = await this.service.get(req.query);
      return res.status(200).json(data);
      //
    } catch (error) {
      console.error('Error in DiscountController.get: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  validate = async (req, res) => {
    try {
      const shopId = req.session._id;
      const discount_code = req.body.discount_code;
      const shop = await this.shopService.getById(shopId);

      const matchDiscounts = await this.service.get({ query: { discount_code, is_active: true } });

      const currentTime = new Date().getTime();

      const validDiscount = matchDiscounts.results.find((discount) => {
        if (currentTime - discount.expired_at >= 0) return false; // expired

        if (discount.targeted_shop_ids) {
          const validShopIds = discount.targeted_shop_ids.split(',');
          if (!validShopIds.includes(shop._id)) return false;
        }
        if (discount.targeted_tapita_plans) {
          const validPlans = discount.targeted_tapita_plans.split(',');
          if (!validPlans.includes(shop.app_data.plan_name)) return false;
        }
        if (discount.targeted_shopify_plans) {
          const validPlans = discount.targeted_shopify_plans.split(',');
          if (!validPlans.includes(shop.shopify_data.plan_name)) return false;
        }

        return true;
      });

      if (!validDiscount) return res.status(200).json({ valid: false, message: 'Coupon is invalid or expired' });

      return res.status(200).json({ valid: true, message: 'Applied', discount: validDiscount });
      //
    } catch (error) {
      console.error('Error in DiscountController.validate: ', error);
      return res.status(500).json({ message: error.message });
    }
  };
}

export default new DiscountController();

