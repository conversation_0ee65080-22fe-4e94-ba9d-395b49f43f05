import { initGraphqlShopifyService } from '../services/shopify/graphql-shopify.service.js';
import axios from 'axios';
import FormData from 'form-data';

class FileController {
  constructor() {}

  upload = async (req, res) => {
    try {
      const { content, name, size, type } = req.body;
      const maxFileSize = 1 * 1024 * 1024;

      if (size > maxFileSize) {
        return res.status(400).json({ message: 'File size exceeds the 1MB limit.' });
      }

      const validImageTypes = ['image/gif', 'image/png', 'image/jpeg', 'image/webp', 'image/avif'];
      if (!validImageTypes.includes(type)) {
        return res.status(400).json({ message: 'Invalid file type. Only specific image formats are allowed.' });
      }

      // create staged upload
      const GraphqlShopifyService = await initGraphqlShopifyService(req.session._id);
      const stagedUpload = await GraphqlShopifyService.createStagedUpload({
        filename: name,
        mimeType: type,
        httpMethod: 'POST',
        resource: 'IMAGE',
      });

      const stagedUrl = stagedUpload?.stagedTargets?.[0]?.url;
      const stagedParams = stagedUpload?.stagedTargets?.[0]?.parameters;
      const stagedResourceUrl = stagedUpload?.stagedTargets?.[0]?.resourceUrl;

      // upload file
      const buffer = Buffer.from(content.replace(/^data:image\/\w+;base64,/, ''), 'base64');
      const formData = new FormData();
      stagedParams.forEach(({ name, value }) => formData.append(name, value));
      formData.append('file', buffer, { filename: name, contentType: type });

      await axios.post(stagedUrl, formData, {
        headers: { ...formData.getHeaders() },
      });

      // create shopify file
      const uploadedFile = await GraphqlShopifyService.createFile({
        alt: name?.split('.')?.[0] || '',
        contentType: 'IMAGE',
        originalSource: stagedResourceUrl,
      });

      const uploadedImageId = uploadedFile?.files?.[0]?.id;

      // Function to retry fetching the image
      const getImageWithRetry = async (id, attempts = 1, maxAttempts = 20) => {
        const result = await GraphqlShopifyService.getImageById(id);
        if (result || attempts >= maxAttempts) {
          return result;
        }
        await new Promise((resolve) => setTimeout(resolve, attempts * 50));
        return getImageWithRetry(id, attempts + 1, maxAttempts);
      };

      const uploadedImageUrl = await getImageWithRetry(uploadedImageId);

      return res.status(200).json({
        message: 'File uploaded successfully',
        data: uploadedImageUrl,
      });
      //
    } catch (error) {
      console.error('Error in FileController.upload: ', error);
      return res.status(500).json({ message: error.message });
    }
  };
}

export default new FileController();

