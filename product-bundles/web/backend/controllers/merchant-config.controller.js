import { MerchantConfigService, ShopService } from '../services/_index.js';

class MerchantConfigController {
  constructor() {
    this.service = MerchantConfigService;
    this.shopService = ShopService;
  }

  get = async (req, res) => {
    try {
      const shop = req.session._id;
      const data = await this.service.get(shop);
      return res.status(200).json(data);
      //
    } catch (error) {
      console.error('Error in MerchantConfigController.get: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  update = async (req, res) => {
    let responded = false;
    const shop = req.session._id;
    const updateData = { ...req.body };

    try {
      // get current config to compare
      const currentConfig = await this.service.get(shop);

      const data = await this.service.upsert(shop, updateData);
      res.status(200).json(data);
      responded = true;

      await this.service.syncToThemeApp(shop);

      if (req.body.password) {
        await this.shopService.updateById(shop, { $set: { 'app_data.password': req.body.password } });
      }

      // check if webhook setting change
      const stockSettingChanged =
        currentConfig?.auto_update_stock_instantly !== updateData.auto_update_stock_instantly &&
        updateData.auto_update_stock_instantly !== undefined;

      const automationSettingChanged =
        currentConfig?.auto_update_automation_group !== updateData.auto_update_automation_group &&
        updateData.auto_update_automation_group !== undefined;

      if (stockSettingChanged || automationSettingChanged) {
        const skipOptions = {
          skipUninstallAppWebhook: true,
          skipChangeThemeWebhook: true,
          skipCreateProductWebhook: !automationSettingChanged,
          skipChangeStockWebhook: !stockSettingChanged,
        };

        await this.shopService.registerWebhooks(shop, skipOptions);
      }
    } catch (error) {
      console.error(`Error in MerchantConfigController.update - ${shop}: `, error);
      if (!responded) res.status(500).json({ message: error.message });
    }
  };
}

export default new MerchantConfigController();

