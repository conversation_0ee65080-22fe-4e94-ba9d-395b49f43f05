import { OfferService } from '../services/_index.js';
import BaseController from './base/base.controller.js';

class ProductGroupController extends BaseController {
  constructor() {
    super(OfferService);
  }

  updateById = async (req, res) => {
    try {
      const shopId = req.session._id;
      const updated = await this.service.updateById(req.params.id, { ...req.body, shop: shopId });

      if (!updated) {
        return res.status(404).json({ message: 'Not found' });
      }

      res.status(200).json(updated);
      //
    } catch (error) {
      console.error('Error in OfferController.updateById: ', error);
    }
  };

  create = async (req, res) => {
    try {
      const shopId = req.session._id;
      const created = await this.service.create({ ...req.body, shop: shopId });

      res.status(201).json(created);
      //
    } catch (error) {
      console.error('Error in OfferController.create: ', error);
    }
  };
}

export default new ProductGroupController();

