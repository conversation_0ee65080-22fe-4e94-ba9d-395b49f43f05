import _ from 'lodash';
import config from '../configs/config.js';
import { ActionLogService, DiscountService, PricingPlanService, ShopService, TaskService } from '../services/_index.js';
import { initGraphqlShopifyService } from '../services/shopify/graphql-shopify.service.js';
import { DateFormatter } from '../utils/date-formatter.js';
import { updateZoho } from '../utils/update-zoho.js';

class PricingPlanController {
  constructor() {
    this.service = PricingPlanService;
    this.shopService = ShopService;
    this.taskService = TaskService;
    this.discountService = DiscountService;
  }

  getAllPlans = async (_req, res) => {
    try {
      const allPlans = this.service.getAllPlans();
      return res.status(200).json(allPlans);
      //
    } catch (error) {
      console.error('Error in PricingPlanController.getAllPlans: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  getShopPlans = async (req, res) => {
    try {
      const shop = req.session._id;
      const shopPlans = await this.service.getAllPlansByShop(shop);
      return res.status(200).json(shopPlans);
      //
    } catch (error) {
      console.error('Error in PricingPlanController.getPlans: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  getShopActivePlan = async (req, res) => {
    try {
      const shop = req.session._id;
      const activePlan = await this.service.getActivePlanByShop(shop);
      return res.status(200).json(activePlan);
      //
    } catch (error) {
      console.error('Error in PricingPlanController.getActivePlan: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  createChargeByPlan = async (req, res) => {
    try {
      const planId = req.params.id;
      const shopId = req.session._id;
      const planData = await this.service.getPlanByIdAndShop(planId, shopId);

      let discountData = null;
      if (req.body.discountId) discountData = await this.discountService.getById(req.body.discountId);

      const [GraphqlShopifyService, shopData] = await Promise.all([
        initGraphqlShopifyService(shopId),
        this.shopService.getById(shopId),
      ]);
      const shop = shopData?.toObject();

      const chargeId = shop?.app_data?.charge_id;

      // cancel charge for free plan
      if (planData?.plan_amount === 0) {
        if (chargeId) await GraphqlShopifyService.cancelChargeById(chargeId);

        console.log(`LIMITATION CHANGE - ${shopId} - createChargeByPlan`);

        // update shop
        const newAppData = {
          ...shop?.app_data,
          charge_id: '',
          charge_name: '',
          payment_date: '',
          charge_status: '',
          plan_name: planData?.plan_name,
          plan_amount: planData?.plan_amount,
          plan_display_name: planData?.plan_display_name,
          recurring_interval: '',
          freeze_limitation: false,
          feature_limitation: JSON.stringify(planData?.default_limitation),
          trial_status: 2,
        };
        const newShop = { ...shop, app_data: newAppData };

        await this.shopService.updateById(shopId, newShop);
        await this.shopService.syncToThemeApp(shopId);

        // write action log
        await ActionLogService.create({
          shop: shopId,
          type: 'other',
          message: 'Switch to free plan',
          detail: {},
        });

        // update zoho
        const zohoData = {
          shopId: shop._id,
          accountData: {
            Grouptify_Plan: planData?.plan_name,
            Grouptify_Charge_ID: '',
            Grouptify_Plan_Amount: '',
            Grouptify_Payment_Date: '',
            Grouptify_Recurring_Interval: '',
            Grouptify_Feature_Limitation: JSON.stringify(planData?.default_limitation),
          },
          contactData: {
            First_Name: _.truncate(shop?.shopify_data?.shop_owner || 'Unknown', { length: 40 }),
            Last_Name: _.truncate(shop?.shopify_data?.shop_owner || 'MTM', { length: 40 }),
            Email: shop?.shopify_data?.email,
            Grouptify_Plan: planData?.plan_name,
          },
        };

        if (shop?.shopify_data?.email !== shop?.shopify_data?.customer_email)
          zohoData.contactData2 = {
            First_Name: _.truncate(shop?.shopify_data?.shop_owner || 'Unknown', { length: 40 }),
            Last_Name: _.truncate(shop?.shopify_data?.shop_owner || 'MTM', { length: 40 }),
            Email: shop?.shopify_data?.customer_email,
            Grouptify_Plan: planData?.plan_name,
          };

        await updateZoho(zohoData);

        return res.status(200).json({
          message: 'Charge cancelled',
          confirmationUrl: `https://${shop?.shopify_data?.myshopify_domain}/admin/apps/${config.env.CLIENT_ID}`,
        });
      }

      // create new charge
      const intervalMapper = {
        monthly: 'EVERY_30_DAYS',
        yearly: 'ANNUAL',
      };

      let chargeName = `Grouptify ${planData?.plan_display_name} - Recurring Charge - $${planData?.plan_amount_base}`;
      let chargeReturnUrl = `${config.env.APP_URL}/api/app/payment?shop_id=${shopId}&plan_id=${planId}`;
      let chargeDiscount = undefined;

      if (planData?.plan_amount - planData?.plan_amount_base)
        chargeName += ` + $${Math.round((planData?.plan_amount - planData?.plan_amount_base) * 100) / 100} (${
          shop?.shopify_data?.plan_display_name
        })`;

      if (discountData) {
        chargeName += ' - ' + discountData.display_name || discountData.name;
        chargeReturnUrl += '&discount_id=' + discountData._id;
        chargeDiscount = {
          durationLimitInIntervals: discountData.discount_interval <= 100 ? discountData.discount_interval : undefined,
          value: { percentage: discountData.discount_percent / 100 },
        };
      }

      const testers = ['71187595477'];
      const isTestCharge = config.env.IS_TESTING || testers.includes(shopId);

      const chargeData = {
        test: isTestCharge,
        name: chargeName,
        returnUrl: chargeReturnUrl,
        lineItems: [
          {
            plan: {
              appRecurringPricingDetails: {
                price: { amount: planData?.plan_amount, currencyCode: 'USD' },
                interval: intervalMapper[planData?.plan_amount],
                discount: chargeDiscount,
              },
            },
          },
        ],
      };

      const createdCharge = await GraphqlShopifyService.createRecurringCharge(chargeData);

      return res.status(200).json(createdCharge);
      //
    } catch (error) {
      console.error('Error in PricingPlanController.createChargeByPlan: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  startTrial = async (req, res) => {
    try {
      const shopId = req.session._id;
      const trialFeature = req.body.feature;
      const trialDuration = req.body.duration;
      const trialEnd = new Date();
      trialEnd.setDate(trialEnd.getDate() + trialDuration);

      const shop = await this.shopService.getById(shopId);

      if (shop?.app_data?.trial_status) return res.status(400).json({ message: 'NO MORE TRIALS MY FRIEND!!!' });

      if (trialFeature === 'redesign_option') {
        const clearTrialTask = {
          shop: shopId,
          type: 'clear_trial',
          data: { trialFeature: 'redesign_option' },
          status: 'time_scheduled',
          auto_schedule: false,
          executed_date: DateFormatter.toYYYYMMDD(trialEnd),
          executed_time: '23:59',
        };

        await this.taskService.create(clearTrialTask);

        const limitation = JSON.parse(shop?.app_data?.feature_limitation || '{}');
        limitation.unlocked_swatch = true;
        limitation.allowed_show_option_on_card = true;

        await this.shopService.updateById(shopId, {
          $set: {
            'app_data.trial_status': 1,
            'app_data.trial_end': DateFormatter.toYYYYMMDD(trialEnd),
            'app_data.freeze_limitation': true,
            'app_data.feature_limitation': JSON.stringify(limitation),
          },
        });
        await this.shopService.syncToThemeApp(shopId);
      }

      return res.status(200).json({
        message: 'Trial started',
        confirmationUrl: `https://${shop?.shopify_data?.myshopify_domain}/admin/apps/${config.env.CLIENT_ID}`,
      });
      //
    } catch (error) {
      console.error('Error in PricingPlanController.trialFeature: ', error);
      return res.status(500).json({ message: error.message });
    }
  };
}

export default new PricingPlanController();

