import { ReleaseNoteService } from '../services/_index.js';
import BaseController from './base/base.controller.js';

class ReleaseNoteController extends BaseController {
  constructor() {
    super(ReleaseNoteService);
  }

  get = async (req, res) => {
    try {
      req.query.page = req.query?.page || 1;
      req.query.page_size = req.query?.page_size || 10000;
      const data = await this.service.get(req.query);
      return res.status(200).json(data);
      //
    } catch (error) {
      console.error('Error in ReleaseNoteController.get: ', error);
      return res.status(500).json({ message: error.message });
    }
  };
}

export default new ReleaseNoteController();

