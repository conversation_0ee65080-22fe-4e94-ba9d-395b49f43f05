import { ShopModel } from '../models/_index.js';
import { DateFormatter } from '../utils/date-formatter.js';

class ReportController {
  constructor() {
    // Define time periods for filtering
    this.periods = [
      {
        name: 'Trước 14/02/2025',
        filter: (doc) => doc.createdAt < new Date('2025-02-14T00:00:00.000Z'),
      },
      {
        name: '14/02/2025 - 27/04/2025',
        filter: (doc) =>
          doc.createdAt >= new Date('2025-02-14T00:00:00.000Z') &&
          doc.createdAt <= new Date('2025-04-27T00:00:00.000Z'),
      },
      {
        name: 'Sau 27/04/2025',
        filter: (doc) => doc.createdAt > new Date('2025-04-27T00:00:00.000Z'),
      },
    ];

    // Define statistics with label and filterFn
    this.statisticsConfig = [
      {
        label: 'Số store',
        filterFn: () => true, // Include all documents
      },
      {
        label: 'Số active store',
        filterFn: (doc) => doc.shop_status === 1,
      },
      {
        label: 'Churn rate',
        filterFn: () => true, // Special case, handled separately
      },
      {
        label: 'Số store dùng custom domain',
        filterFn: (doc) => !/myshopify\.com/.test(doc.shopify_data?.domain),
      },
      {
        label: 'Số store dùng domain myshopify.com',
        filterFn: (doc) => /myshopify\.com/.test(doc.shopify_data?.domain),
      },
      {
        label: 'Số uninstalled store dùng domain myshopify.com',
        filterFn: (doc) => doc.shop_status !== 1 && /myshopify\.com/.test(doc.shopify_data?.domain),
      },
      {
        label: 'Số khách bật app',
        filterFn: (doc) => doc.shop_status === 1 && doc.app_data?.app_enabled === true,
      },
      {
        label: 'Số khách có theme đã được support',
        filterFn: (doc) => doc.shop_status === 1 && doc.app_data?.theme_supported === true,
      },
      {
        label: 'Số khách tạo ít nhất 1 group',
        filterFn: (doc) => doc.shop_status === 1 && (doc.app_data?.group_count || 0) >= 1,
      },
      {
        label: 'Số khách tạo ít nhất 4 group',
        filterFn: (doc) => doc.shop_status === 1 && (doc.app_data?.group_count || 0) >= 4,
      },
      {
        label: 'Số khách tạo ít nhất 80 group',
        filterFn: (doc) => doc.shop_status === 1 && (doc.app_data?.group_count || 0) >= 80,
      },
      {
        label: 'Số khách tạo ít nhất 400 group',
        filterFn: (doc) => doc.shop_status === 1 && (doc.app_data?.group_count || 0) >= 400,
      },
      {
        label: 'Số khách tạo ít nhất 4000 group',
        filterFn: (doc) => doc.shop_status === 1 && (doc.app_data?.group_count || 0) >= 4000,
      },
      {
        label: 'Số khách group ít nhất 1 product',
        filterFn: (doc) => doc.shop_status === 1 && (doc.app_data?.product_count || 0) >= 1,
      },
      {
        label: 'Số khách group ít nhất 40 product',
        filterFn: (doc) => doc.shop_status === 1 && (doc.app_data?.product_count || 0) >= 40,
      },
      {
        label: 'Số khách group ít nhất 80 product',
        filterFn: (doc) => doc.shop_status === 1 && (doc.app_data?.product_count || 0) >= 80,
      },
      {
        label: 'Số khách group ít nhất 800 product',
        filterFn: (doc) => doc.shop_status === 1 && (doc.app_data?.product_count || 0) >= 800,
      },
      {
        label: 'Số khách group ít nhất 8000 product',
        filterFn: (doc) => doc.shop_status === 1 && (doc.app_data?.product_count || 0) >= 8000,
      },
      {
        label: 'Số khách đã tạo Single Option Group',
        filterFn: (doc) => doc.shop_status === 1 && (doc.app_data?.group_count_manual_single || 0) >= 1,
      },
      {
        label: 'Số khách đã tạo Multiple Options Group',
        filterFn: (doc) => doc.shop_status === 1 && (doc.app_data?.group_count_manual_multiple || 0) >= 1,
      },
      {
        label: 'Số khách đã tạo Group Automation',
        filterFn: (doc) => doc.shop_status === 1 && (doc.app_data?.group_count_automation_single || 0) >= 1,
      },
      {
        label: 'Số khách dùng chức năng Re-design',
        filterFn: (doc) =>
          doc.shop_status === 1 &&
          doc.app_data?.app_enabled === true &&
          (doc.merchant_config?.swatch_enabled === true ||
            doc.app_data?.swatch_on_pdp_enabled === true ||
            doc.app_data?.swatch_on_cdp_enabled === true),
      },
      {
        label: 'Số khách dùng Shopify Basic Plan',
        filterFn: (doc) => doc.shop_status === 1 && doc.shopify_data?.plan_name === 'basic',
      },
      {
        label: 'Số khách dùng Shopify Standard Plan',
        filterFn: (doc) => doc.shop_status === 1 && doc.shopify_data?.plan_name === 'professional',
      },
      {
        label: 'Số khách dùng Shopify Advanced Plan',
        filterFn: (doc) => doc.shop_status === 1 && doc.shopify_data?.plan_name === 'unlimited',
      },
      {
        label: 'Số khách dùng Shopify Plus Plan',
        filterFn: (doc) => doc.shop_status === 1 && doc.shopify_data?.plan_name === 'shopify_plus',
      },
      {
        label: 'Số khách dùng Shopify Plan khác (Partner Test, …)',
        filterFn: (doc) =>
          doc.shop_status === 1 &&
          !['basic', 'professional', 'unlimited', 'shopify_plus'].includes(doc.shopify_data?.plan_name),
      },
      {
        label: 'Số khách dùng Grouptify Free Plan',
        filterFn: (doc) => doc.shop_status === 1 && doc.app_data?.charge_id === '',
      },
      {
        label: 'Số khách dùng Grouptify Basic Plan',
        filterFn: (doc) =>
          doc.shop_status === 1 && doc.app_data?.plan_display_name === 'Basic' && doc.app_data?.charge_id !== '',
      },
      {
        label: 'Số khách dùng Grouptify Standard Plan',
        filterFn: (doc) =>
          doc.shop_status === 1 && doc.app_data?.plan_display_name === 'Standard' && doc.app_data?.charge_id !== '',
      },
      {
        label: 'Số khách dùng Grouptify Advanced Plan',
        filterFn: (doc) =>
          doc.shop_status === 1 && doc.app_data?.plan_display_name === 'Advanced' && doc.app_data?.charge_id !== '',
      },
    ];
  }

  getAppStatistics = async (_req, res) => {
    try {
      // Define fields to select from shops
      const shopFields = [
        'shop_status',
        'shopify_data.domain',
        'shopify_data.plan_name',
        'app_data.app_enabled',
        'app_data.theme_supported',
        'app_data.group_count',
        'app_data.product_count',
        'app_data.group_count_manual_single',
        'app_data.group_count_manual_multiple',
        'app_data.group_count_automation_single',
        'app_data.plan_display_name',
        'app_data.charge_id',
        'app_data.swatch_on_pdp_enabled',
        'app_data.swatch_on_cdp_enabled',
        'createdAt',
      ];

      // Create projection object from shopFields
      const projection = shopFields.reduce(
        (acc, field) => {
          acc[field] = 1;
          return acc;
        },
        { 'merchant_config.swatch_enabled': 1 }
      );

      // Aggregate shops with merchantconfigs
      const shops = await ShopModel.aggregate([
        {
          $lookup: {
            from: 'merchantconfigs',
            localField: '_id',
            foreignField: 'shop',
            as: 'merchant_config',
          },
        },
        {
          $unwind: {
            path: '$merchant_config',
            preserveNullAndEmptyArrays: true, // Keep shops without merchantconfigs
          },
        },
        {
          $project: projection,
        },
      ]).exec();

      // Calculate statistics for each period
      const results = [];
      for (const { name, filter } of this.periods) {
        // Filter shops by period
        const periodShops = shops.filter(filter);

        // Calculate statistics for this period
        for (const { label, filterFn } of this.statisticsConfig) {
          let value;
          if (label === 'Churn rate') {
            const total = periodShops.length;
            const active = periodShops.filter((doc) => doc.shop_status === 1).length;
            value = total > 0 ? ((total - active) / total) * 100 : 0;
          } else {
            value = periodShops.filter((doc) => filterFn(doc)).length;
          }

          // Add result with period-prefixed label
          results.push({
            label: `(Khách cài từ ${name}) ${label}`,
            value,
          });
        }
      }

      // Return response
      return res.status(200).json(results);
    } catch (error) {
      console.error('Error in ReportController.getAppStatistics: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  getMerchantActions = async (req, res) => {
    try {
      // Extract startDate and endDate from body
      const { start, end } = req.query;
      if (!start || !end) {
        return res.status(400).json({ message: 'start and end are required' });
      }

      // Parse dates in +7 timezone and convert to UTC
      const startDate = new Date(`${start}T00:00:00.000+07:00`);
      const endDate = new Date(`${end}T23:59:59.999+07:00`);
      const startUnix = startDate.getTime();
      const endUnix = endDate.getTime();

      // Validate dates
      if (isNaN(startUnix) || isNaN(endUnix)) {
        return res.status(400).json({ message: 'Invalid date format. Use yyyy-mm-dd' });
      }

      // Aggregate shops with actionlogs
      const shops = await ShopModel.aggregate([
        {
          $match: {
            shop_status: { $in: [1, 2] },
            $or: [
              { install_unix_time: { $gte: startUnix, $lte: endUnix } },
              { uninstall_unix_time: { $gte: startUnix, $lte: endUnix } },
            ],
          },
        },
        {
          $lookup: {
            from: 'actionlogs',
            localField: '_id',
            foreignField: 'shop',
            as: 'action_logs',
          },
        },
        {
          $sort: {
            install_unix_time: 1, // Sort by install_unix_time ascending
          },
        },
        {
          $project: {
            shop_name: '$shopify_data.name',
            myshopify_domain: '$shopify_data.myshopify_domain',
            shop_status: 1,
            install_unix_time: 1,
            uninstall_unix_time: 1,
            shopify_plan: '$shopify_data.plan_name',
            grouptify_plan: '$app_data.plan_name',
            rating_point: '$app_data.rating_point',
            theme_supported: '$app_data.theme_supported',
            app_enabled: '$app_data.app_enabled',
            swatch_on_pdp_enabled: '$app_data.swatch_on_pdp_enabled',
            swatch_on_cdp_enabled: '$app_data.swatch_on_cdp_enabled',
            group_count: '$app_data.group_count',
            product_count: '$app_data.product_count',
            single_group_count: '$app_data.group_count_manual_single',
            multiple_group_count: '$app_data.group_count_manual_multiple',
            automation_group_count: '$app_data.group_count_automation_single',
            active_group_count: '$app_data.active_group_count',
            active_product_count: '$app_data.active_product_count',
            action_logs: 1,
          },
        },
      ]).exec();

      // Format dates in +7 timezone
      const formatDate = (unixTime) => {
        if (!unixTime) return null;
        const date = new Date(unixTime);
        return date.toLocaleString('en-US', {
          month: 'long',
          day: 'numeric',
          year: 'numeric',
          hour: 'numeric',
          minute: 'numeric',
          hour12: true,
          timeZone: 'Asia/Ho_Chi_Minh',
        });
      };

      // Format results
      const results = shops.map((shop) => {
        // Calculate uninstall duration
        let uninstallDuration = null;
        if (shop.install_unix_time && shop.uninstall_unix_time) {
          const diffMs = shop.uninstall_unix_time - shop.install_unix_time;
          const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
          const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
          uninstallDuration = `${days} day ${hours} hour ${minutes} minute`;
        }

        return {
          shop_name: shop.shop_name || '',
          myshopify_domain: shop.myshopify_domain || '',
          status: shop.shop_status === 1 ? 'Installed' : 'Uninstalled',
          installed_date: formatDate(shop.install_unix_time) || '',
          uninstalled_date: formatDate(shop.uninstall_unix_time) || '',
          uninstall_duration: uninstallDuration || '',
          shopify_plan: shop.shopify_plan || '',
          grouptify_plan: shop.grouptify_plan || '',
          rating_point: shop.rating_point || '',
          theme_supported: shop.theme_supported ? 'Yes' : 'No',
          app_enabled: shop.app_enabled ? 'Yes' : 'No',
          swatch_on_pdp_enabled: shop.swatch_on_pdp_enabled ? 'Yes' : 'No',
          swatch_on_cdp_enabled: shop.swatch_on_cdp_enabled ? 'Yes' : 'No',
          group_count: shop.group_count || 0,
          product_count: shop.product_count || 0,
          single_group_count: shop.single_group_count || 0,
          multiple_group_count: shop.multiple_group_count || 0,
          automation_group_count: shop.automation_group_count || 0,
          active_group_count: shop.active_group_count || 0,
          active_product_count: shop.active_product_count || 0,
          action_logs: (shop.action_logs || [])
            .map((log) => `${DateFormatter.toHHMMSSDDMMYYYY(log.createdAt)} - ${log.message}`)
            .join('\n'),
        };
      });

      // Return response
      return res.status(200).json(results);
    } catch (error) {
      console.error('Error in ReportController.getMerchantActions: ', error);
      return res.status(500).json({ message: error.message });
    }
  };
}

export default new ReportController();

