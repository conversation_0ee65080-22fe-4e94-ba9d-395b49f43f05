import fetch from 'node-fetch';
import { ApiKeyService, AppConfigService, AuthService, PricingPlanService, ShopService } from '../services/_index.js';
import BaseController from './base/base.controller.js';

class ShopController extends BaseController {
  constructor() {
    super(ShopService);
    this.apiKeyService = ApiKeyService;
    this.appConfigService = AppConfigService;
  }

  get = async (req, res) => {
    try {
      const data = await this.service.getById(req.session._id);
      return res.status(200).json(data);
      //
    } catch (error) {
      console.error('Error in ShopController.get: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  search = async (req, res) => {
    try {
      const options = req.query;
      const queryString = options.query;
      const escapedString = queryString.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

      options.query = {
        $or: [
          { 'shopify_data.myshopify_domain': { $regex: escapedString, $options: 'i' } },
          { 'shopify_data.customer_email': { $regex: escapedString, $options: 'i' } },
          { 'shopify_data.shop_owner': { $regex: escapedString, $options: 'i' } },
          { 'shopify_data.domain': { $regex: escapedString, $options: 'i' } },
          { 'shopify_data.phone': { $regex: escapedString, $options: 'i' } },
          { 'shopify_data.email': { $regex: escapedString, $options: 'i' } },
          { 'shopify_data.name': { $regex: escapedString, $options: 'i' } },
          { _id: escapedString },
        ],
      };

      options.select = '-access_token -access_scope -custom_data.product_options';

      if (queryString.includes('mtm_custom:')) {
        const customQueryString = queryString?.split('mtm_custom:')?.map((s) => s.trim())?.[1];
        const customQueryObject = JSON.parse(customQueryString || '{}');
        options.query = customQueryObject;
      }

      const data = await this.service.get(options);

      const sessionPromises = data?.results?.map((r) => AuthService.getSession({ id: r._id, role: 'shop' }));
      const sessions = await Promise.all(sessionPromises);

      data.results = data.results?.map((r, i) => {
        const shopObj = r.toObject();
        shopObj.auth_token = sessions?.[i]?.token;
        return shopObj;
      });

      return res.status(200).json(data);
      //
    } catch (error) {
      console.error('Error in ShopController.search: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  getCompetitors = async (req, res) => {
    try {
      const shopId = req.params.id;
      const [shop, appConfig] = await Promise.all([this.service.getById(shopId), this.appConfigService.get()]);
      const shopDomain = shop.shopify_data.domain;

      // fetch html from shop domain
      const html = await fetch(`https://${shopDomain}`).then((res) => res.text());

      const competitors = appConfig.competitors || [];

      const installedCompetitors = [];

      competitors.forEach((competitor) => {
        if (html.includes(competitor.identifier)) {
          installedCompetitors.push(competitor.name);
        }
      });

      await this.service.updateById(shopId, {
        $set: { 'app_data.installed_competitors': installedCompetitors.join(',') },
      });

      return res.status(200).json({ installedCompetitors });
      //
    } catch (error) {
      console.error('Error in ShopController.getCompetitors: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  updateById = async (req, res) => {
    let responded = false;

    try {
      const shopId = req.params.id;

      const oldShop = await this.service.getById(shopId);

      if (!oldShop) {
        return res.status(404).json({ message: 'Not found' });
      }

      const newShop = await this.service.updateById(req.params.id, req.body);

      res.status(200).json(newShop);
      responded = true;

      await this.service.syncToThemeApp(req.params.id);

      //
    } catch (error) {
      console.error('Error in ShopController.updateById: ', error);
      if (!responded) res.status(500).json({ message: error.message });
    }
  };

  regenerateApiKey = async (req, res) => {
    try {
      const shopId = req.session._id;
      const shop = await ShopService.getById(shopId);
      const limitation = JSON.parse(shop?.app_data?.feature_limitation || '{}');

      if (!limitation.allowed_request_api_key) return res.status(400).json({ message: 'NICE TRY MY FRIEND' });
      const apiKey = await this.apiKeyService.refreshByShop(shopId);
      shop.gtf_access_token = apiKey.raw_key;
      await shop.save();

      return res.status(200).json({ message: 'API key regenerated' });
      //
    } catch (error) {
      console.error('Error in ShopController.regenerateApiKey: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  sync = async (req, res) => {
    try {
      const shopId = req.body.shop;
      const shop = await ShopService.getById(shopId);

      if (!shop.app_data.freeze_limitation) {
        const shopPlan = await PricingPlanService.getActivePlanByShop(shopId);
        if (shopPlan) {
          await ShopService.updateById(shopId, {
            $set: { 'app_data.feature_limitation': JSON.stringify(shopPlan.default_limitation) },
          });
        }
      }

      await this.service.syncToThemeApp(shopId);
      return res.status(200).json({ message: 'SYNCED' });
      //
    } catch (error) {
      console.error('Error in ShopController.sync: ', error);
      return res.status(500).json({ message: error.message });
    }
  };

  saveSurveyResult = async (req, res) => {
    try {
      const shopId = req.session._id;
      const { survey_status, survey_response, survey_result, tags } = req.body;

      // Get current shop data to check existing tags
      const shop = await this.service.getById(shopId);
      const existingTags = shop.app_data.tags || [];

      // Filter out tags that start with org_, feature_, or action_
      const filteredExistingTags = existingTags.filter(
        (tag) => !tag.startsWith('org_') && !tag.startsWith('feature_') && !tag.startsWith('action_')
      );

      // Filter out duplicate tags from new tags
      const newTags = tags.filter((tag) => !filteredExistingTags.includes(tag));
      const updatedTags = [...filteredExistingTags, ...newTags];

      const updateData = {
        'app_data.survey_status': survey_status,
        'app_data.survey_response': survey_response,
        'app_data.survey_result': survey_result,
        'app_data.tags': updatedTags,
      };

      await this.service.updateById(shopId, { $set: updateData });

      return res.status(200).json({ message: 'Survey result saved' });
      //
    } catch (error) {
      console.error('Error in ShopController.saveSurveyResult: ', error);
      return res.status(500).json({ message: error.message });
    }
  };
}

export default new ShopController();

