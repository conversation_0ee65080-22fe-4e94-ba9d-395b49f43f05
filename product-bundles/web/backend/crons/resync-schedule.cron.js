import cron from 'node-cron';
import { ShopModel, TaskModel } from '../models/_index.js';

// Constants for scheduling
const TIME_CONSTANTS = {
  EARLY_SYNC: 0, // Earliest hour to start synchronization
  LATELY_SYNC: 6, // Latest hour to start synchronization
  TIME_PER_BLOCK: 5, // Minutes per checkpoint block
  BLOCK_PER_HOUR: 60 / 5, // Number of blocks per hour (hardcoded 5 from TIME_PER_BLOCK)
  BLOCK_PER_DAY: (24 * 60) / 5, // Total blocks in a day
};

// Helper functions for time conversion and validation
const timeUtils = {
  validateTimeInRange: (time, minTime, maxTime) => {
    const BLOCK_PER_DAY = TIME_CONSTANTS.BLOCK_PER_DAY;
    if (minTime <= maxTime) return minTime <= time && time <= maxTime;
    return (minTime <= time && time <= BLOCK_PER_DAY) || (0 <= time && time <= maxTime);
  },

  numToTimeStr: (num = 0) => {
    const { BLOCK_PER_HOUR, TIME_PER_BLOCK } = TIME_CONSTANTS;
    const hour = Math.floor(num / BLOCK_PER_HOUR);
    const minute = (num % BLOCK_PER_HOUR) * TIME_PER_BLOCK;
    return `${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}`;
  },

  timeStrToNum: (timeStr = '00:00') => {
    const { BLOCK_PER_HOUR, TIME_PER_BLOCK } = TIME_CONSTANTS;
    const hour = Number(timeStr.split(':')[0]);
    const minute = Number(timeStr.split(':')[1]);
    return hour * BLOCK_PER_HOUR + Math.floor(minute / TIME_PER_BLOCK);
  },
};

// Initialize checkpoints for task scheduling
const initializeCheckpoints = () => {
  const checkpoints = [];
  const { BLOCK_PER_HOUR, TIME_PER_BLOCK } = TIME_CONSTANTS;

  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += TIME_PER_BLOCK) {
      const time = hour * BLOCK_PER_HOUR + minute / TIME_PER_BLOCK;
      checkpoints.push({ time, weight: 0 });
    }
  }
  return checkpoints;
};

// Fetch all shops with basic data needed for all task types
const fetchShopsBasicData = async () => {
  try {
    let shops = await ShopModel.aggregate([
      {
        $match: {
          $or: [
            { shop_status: 1 }, // Active shops
            { shop_status: 3 }, // Closed shops (for store status check)
          ],
        },
      },
      { $lookup: { from: 'merchantconfigs', localField: '_id', foreignField: 'shop', as: 'merchant_config' } },
      { $unwind: { path: '$merchant_config', preserveNullAndEmptyArrays: true } },
    ]);

    return shops.map((shop) => ({
      id: shop._id,
      shop_status: shop.shop_status,
      updatedAt: shop.updatedAt,
      timezone: shop.shopify_data?.timezone || '(GMT+00:00) Europe/London',
      feature_limitation: JSON.parse(shop.app_data?.feature_limitation || '{}'),
      product_count: shop.app_data?.product_count || 0,
      charge_id: shop.app_data?.charge_id,
      resync_group_mode: shop.merchant_config?.resync_group_mode || 'disable',
      resync_group_time: shop.merchant_config?.resync_group_time || '00:00',
      resync_group_interval: shop.merchant_config?.resync_group_interval || '1d',
      resync_group_times_per_day: shop.merchant_config?.resync_group_times_per_day || 1,
    }));
    //
  } catch (error) {
    console.error('Error fetching shops:', error);
    return [];
  }
};

// Filter shops for resync task
const filterShopsForResyncTask = (shops) => {
  return shops
    .filter(
      (shop) =>
        shop.shop_status === 1 &&
        shop.product_count &&
        shop.resync_group_mode !== 'disable' &&
        shop.feature_limitation.unlocked_auto_resync
    )
    .map((shop) => ({
      id: shop.id,
      type: 'resync_group',
      timezone: shop.timezone,
      timezone_offset: getTimezoneOffset(shop.timezone),
      task_weight: Math.ceil(shop.product_count / 500),
      resync_mode: shop.resync_group_mode,
      resync_time: shop.resync_group_time,
      resync_interval: parseInt(shop.resync_group_interval),
      resync_times_per_day: parseInt(shop.resync_group_times_per_day),
    }));
};

// Filter shops for store status check
const filterShopsForStoreStatusCheck = (shops) => {
  const twoDaysAgo = new Date();
  twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);

  return shops
    .filter((shop) => (shop.shop_status === 1 && new Date(shop.updatedAt) < twoDaysAgo) || shop.shop_status === 3)
    .map((shop) => ({
      id: shop.id,
      type: 'check_store_status',
      timezone: shop.timezone,
      timezone_offset: getTimezoneOffset(shop.timezone),
      task_weight: 1,
      resync_mode: 'enable_with_auto_time',
      resync_time: '00:00',
      resync_interval: 1,
      resync_times_per_day: 1,
    }));
};

// Filter shops for charge status check
const filterShopsForChargeStatusCheck = (shops) => {
  return shops
    .filter((shop) => (shop.shop_status === 1 || shop.shop_status === 3) && shop.charge_id)
    .map((shop) => ({
      id: shop.id,
      type: 'check_charge_status',
      timezone: shop.timezone,
      timezone_offset: getTimezoneOffset(shop.timezone),
      task_weight: 1,
      resync_mode: 'enable_with_auto_time',
      resync_time: '00:00',
      resync_interval: 1,
      resync_times_per_day: 1,
    }));
};

// Extract timezone offset from timezone string
const getTimezoneOffset = (timezone) => {
  const match = timezone?.match(/GMT([+-]\d+)/);
  return match ? (parseInt(match[1], 10) + 24) % 24 : 0;
};

// Create or find task for a shop
const getOrCreateTask = async (shop, tomorrowStr) => {
  try {
    // Check if a task already exists for the shop
    let task = await TaskModel.findOne({
      shop: shop.id,
      type: shop.type,
      executed_date: { $gte: tomorrowStr },
    });

    // If no task exists, create a new task based on shop settings
    if (!task) {
      let presetTime = shop.resync_time;
      let presetHour = presetTime?.split(':')?.[0] || '00';
      let presetMin = presetTime?.split(':')?.[1] || '00';
      presetMin = String(Math.floor(parseInt(presetMin) / 5) * 5).padStart(2, '0');
      presetHour = String(presetHour).padStart(2, '0');
      presetTime = `${presetHour}:${presetMin}`;

      const taskData = {
        shop: shop.id,
        type: shop.type,
        data: {},
        status: 'date_scheduled',
        auto_schedule: shop.resync_mode !== 'enable_with_preset_time',
        executed_date: tomorrowStr,
        executed_time: presetTime || '00:00',
      };

      task = await TaskModel.create(taskData);
    }

    return task;
    //
  } catch (error) {
    console.error(`Error while processing shop ${shop.id} for task ${shop.type}:`, error);
    return null;
  }
};

// Schedule tasks across checkpoints
const scheduleTasksForShops = async (shops, checkpoints, tomorrowStr) => {
  for (const shop of shops) {
    try {
      // Get or create task for this shop
      const task = await getOrCreateTask(shop, tomorrowStr);
      if (!task) continue;

      shop.task = task;

      // If task is not auto-scheduled, just update the weight at the specific time
      if (shop.task.auto_schedule === false) {
        const executedTime = timeUtils.timeStrToNum(shop.task.executed_time);
        const checkpoint = checkpoints.find((c) => c.time === executedTime);
        if (checkpoint) checkpoint.weight += shop.task_weight;
        shop.task.status = 'time_scheduled';
        await shop.task.save();
        continue;
      }

      // Calculate time blocks based on timezone offset for auto-scheduled tasks
      const { EARLY_SYNC, LATELY_SYNC, BLOCK_PER_HOUR, BLOCK_PER_DAY } = TIME_CONSTANTS;
      let earliestBlock =
        (EARLY_SYNC * BLOCK_PER_HOUR - shop.timezone_offset * BLOCK_PER_HOUR + BLOCK_PER_DAY) % BLOCK_PER_DAY;
      let latestBlock =
        (LATELY_SYNC * BLOCK_PER_HOUR - shop.timezone_offset * BLOCK_PER_HOUR + BLOCK_PER_DAY) % BLOCK_PER_DAY;

      // Find the checkpoint with minimal weight within the valid time range
      let minCheckpoint = checkpoints[0];
      checkpoints.forEach((c) => {
        if (timeUtils.validateTimeInRange(c.time, earliestBlock, latestBlock) && c.weight <= minCheckpoint.weight) {
          minCheckpoint = c;
        }
      });

      const BLOCK_PER_INTERVAL = BLOCK_PER_DAY / shop.resync_times_per_day;
      const firstExecutedTime = minCheckpoint.time % BLOCK_PER_INTERVAL;

      minCheckpoint.weight += shop.task_weight;
      shop.task.status = 'time_scheduled';
      shop.task.executed_time = timeUtils.numToTimeStr(firstExecutedTime);
      await shop.task.save();
      //
    } catch (error) {
      console.error(`Error while scheduling task for shop ${shop.id} (${shop.type}):`, error);
      continue; // Prevent one error from halting the loop
    }
  }
};

// Schedule the task scheduling job for 22:22 (UTC) daily
cron.schedule(
  '22 22 * * *',
  async () => {
    try {
      console.log('Cron job started for task scheduling.');

      // Get tomorrow's date in UTC format
      const now = new Date();
      const tomorrow = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() + 1));
      const tomorrowStr = tomorrow.toISOString().split('T')[0];

      // Fetch all shops with basic data
      const allShops = await fetchShopsBasicData();

      // Initialize checkpoints for task scheduling
      const checkpoints = initializeCheckpoints();

      // Process each task type
      const taskHandlers = [
        { name: 'check_store_status', filter: filterShopsForStoreStatusCheck },
        { name: 'check_charge_status', filter: filterShopsForChargeStatusCheck },
        { name: 'resync_group', filter: filterShopsForResyncTask },
      ];

      // Process each task type
      for (const handler of taskHandlers) {
        const filteredShops = handler.filter(allShops);
        console.log(`Processing ${filteredShops.length} shops for task type: ${handler.name}`);
        await scheduleTasksForShops(filteredShops, checkpoints, tomorrowStr);
      }

      console.log('Task scheduling completed successfully.');
      //
    } catch (error) {
      console.error('Failed to schedule tasks:', error);
    }
  },
  { timezone: 'UTC' }
);

