import cron from 'node-cron';
import { ActionLogService, ShopService, TaskService } from '../services/_index.js';
import _ from 'lodash';
import { initGraphqlShopifyService } from '../services/shopify/graphql-shopify.service.js';
import { updateZoho } from '../utils/update-zoho.js';
import { initRestShopifyService } from '../services/shopify/rest-shopify.service.js';
import moment from 'moment';

const CHUNK_SIZE = 20;

const APP_STATUS = {
  1: 'installed',
  2: 'uninstalled',
  3: 'closedstore',
  4: 'api_404',
};

// Common function to handle task rescheduling logic
const handleTaskRescheduling = async (task, taskType) => {
  try {
    // Get shop config for newest task settings
    // Default values if config not found
    let resyncMode = 'disable';
    let resyncTimesPerDay = 1;

    // For new task types, we'll use daily auto-scheduling by default
    if (taskType === 'check_store_status' || taskType === 'check_charge_status') {
      resyncMode = 'enable_with_auto_time';
      resyncTimesPerDay = 1;
    }

    const resyncInterval = Math.floor(24 / resyncTimesPerDay);

    // If task is enabled, reschedule it
    if (resyncMode?.includes('enable') && resyncInterval) {
      const timeTokens = task.executed_time.split(':');
      let nextHour = parseInt(timeTokens[0]) + resyncInterval;

      // Move to next time in day
      if (nextHour < 24) {
        timeTokens[0] = nextHour.toString().padStart(2, '0');
        task.executed_time = timeTokens.join(':');
      }
      // Move to tomorrow
      else {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        task.executed_date = tomorrow.toISOString().split('T')[0];
        task.status = 'date_scheduled';
      }

      await task.save();
      console.log(`Task ${task._id} rescheduled for shop ${task.shop}`);
    }
    // Else delete task
    else {
      await TaskService.deleteById(task._id);
      console.log(`Task ${task._id} deleted for shop ${task.shop}`);
    }
  } catch (error) {
    console.error(`Error in task rescheduling for ${task._id}:`, error);
  }
};

// Process store status check task
const processStoreStatusTask = async (task) => {
  if (!task.shop) return;

  try {
    console.log(`Starting store status check task ${task._id} for shop ${task.shop}`);

    // Get current shop status
    const shop = await ShopService.getById(task.shop);

    if (!shop) {
      console.warn(`Shop ${task.shop} not found`);
      await TaskService.deleteById(task._id);
      return;
    }

    const oldStatus = shop.shop_status;

    // Call to Shopify to check if shop is active
    const GraphqlShopifyService = await initGraphqlShopifyService(task.shop);
    const shopifyShop = await GraphqlShopifyService.getShopLite(task.shop);

    // Update shop status based on Shopify response
    let newStatus;

    // Shop is closed or invalid
    if (!shopifyShop) newStatus = 3; // closed
    // Shop is active
    else newStatus = 1; // installed

    // Update shop status if changed
    if (oldStatus !== newStatus) {
      console.log(`Updating shop ${task.shop} status from ${oldStatus} to ${newStatus}`);
      await ShopService.updateById(task.shop, { shop_status: newStatus });

      // write action log

      await ActionLogService.create({
        shop: task.shop,
        type: 'other',
        message: `Shop status changed from ${oldStatus} to ${newStatus}`,
        detail: {},
      });

      // Update Zoho if status changed
      const zohoData = {
        shopId: shop._id,
        accountData: {
          Grouptify_App_Status: APP_STATUS[newStatus],
        },
        contactData: {
          First_Name: _.truncate(shop?.shopify_data?.shop_owner || 'Unknown', { length: 40 }),
          Last_Name: _.truncate(shop?.shopify_data?.shop_owner || 'MTM', { length: 40 }),
          Email: shop?.shopify_data?.email,
          Grouptify_App_Status: APP_STATUS[newStatus],
        },
      };

      if (shop?.shopify_data?.email !== shop?.shopify_data?.customer_email)
        zohoData.contactData2 = {
          First_Name: _.truncate(shop?.shopify_data?.shop_owner || 'Unknown', { length: 40 }),
          Last_Name: _.truncate(shop?.shopify_data?.shop_owner || 'MTM', { length: 40 }),
          Email: shop?.shopify_data?.customer_email,
          Grouptify_App_Status: APP_STATUS[newStatus],
        };

      await updateZoho(zohoData);
    }

    console.log(`Completed store status check task ${task._id} for shop ${task.shop}`);

    // Handle task rescheduling
    await handleTaskRescheduling(task, 'check_store_status');
    //
  } catch (error) {
    console.error(`Error processing store status check task ${task._id} for shop ${task.shop}:`, error);
  }
};

// Process charge status check task
const processChargeStatusTask = async (task) => {
  if (!task.shop) return;

  try {
    console.log(`Starting charge status check task ${task._id} for shop ${task.shop}`);

    // Get shop data to access charge ID
    const shop = await ShopService.getById(task.shop);
    if (!shop?.app_data?.charge_id) {
      console.warn(`Shop ${task.shop} has no charge ID`);
      await TaskService.deleteById(task._id);
      return;
    }

    const chargeId = shop.app_data.charge_id;
    const paymentDate = shop.app_data.payment_date;
    const oldChargeStatus = shop.app_data.charge_status || null;

    // Get charge info from Shopify
    const RestShopifyService = await initRestShopifyService(task.shop);
    const chargeInfo = await RestShopifyService.getRecurringApplicationChargeById(chargeId);

    if (!chargeInfo) {
      console.warn(`Charge ${chargeId} not found for shop ${task.shop}`);
      await TaskService.deleteById(task._id);
      return;
    }

    const newChargeStatus = chargeInfo.status;

    // Update charge status if changed
    if (oldChargeStatus !== newChargeStatus) {
      console.log(`Updating shop ${task.shop} charge status from ${oldChargeStatus} to ${newChargeStatus}`);
      await ShopService.updateById(task.shop, { $set: { 'app_data.charge_status': newChargeStatus } });

      // write action log
      await ActionLogService.create({
        shop: task.shop,
        type: 'other',
        message: `Charge status changed from ${oldChargeStatus} to ${newChargeStatus}`,
        detail: {},
      });

      // Update Zoho if status changed
      const zohoData = {
        shopId: shop._id,
        accountData: {
          Grouptify_Charge_ID: newChargeStatus === 'active' ? chargeId : '',
          Grouptify_Payment_Date:
            newChargeStatus === 'active' ? moment(new Date(paymentDate)).utcOffset(7).format() : undefined,
        },
      };

      await updateZoho(zohoData);
    }

    console.log(`Completed charge status check task ${task._id} for shop ${task.shop}`);

    // Handle task rescheduling
    await handleTaskRescheduling(task, 'check_charge_status');
    //
  } catch (error) {
    console.error(`Error processing charge status check task ${task._id} for shop ${task.shop}:`, error);
  }
};

// Mapping of task types to their processing functions
const taskProcessors = {
  check_store_status: processStoreStatusTask,
  check_charge_status: processChargeStatusTask,
};

// Cron job definition
cron.schedule(
  '*/5 * * * *',
  async () => {
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
    const timeStr = `${String(now.getUTCHours()).padStart(2, '0')}:${String(
      Math.ceil(now.getUTCMinutes() / 5) * 5
    ).padStart(2, '0')}`;

    console.log(`Task execution job started at ${now.toISOString()}`);

    try {
      // Get all task types to process
      const taskTypes = Object.keys(taskProcessors);

      for (const taskType of taskTypes) {
        try {
          // Fetch tasks for current type
          const tasks = await TaskService.get({
            type: taskType,
            status: 'time_scheduled',
            executed_date: dateStr,
            executed_time: timeStr,
          });

          console.log(`Found ${tasks.length} ${taskType} tasks to process`);

          // Split tasks into chunks for parallel processing
          const taskChunks = _.chunk(tasks, CHUNK_SIZE);

          for (const taskChunk of taskChunks) {
            // Process tasks in parallel using the appropriate processor
            const results = await Promise.allSettled(taskChunk.map((task) => taskProcessors[taskType](task)));

            // Log results for failed tasks
            results.forEach((result, index) => {
              if (result.status === 'rejected') {
                console.error(`Task ${taskChunk[index]._id} failed:`, result.reason);
              }
            });
          }
        } catch (error) {
          console.error(`Error processing ${taskType} tasks:`, error);
        }
      }
    } catch (error) {
      console.error('Error executing task job:', error);
    }

    console.log('Task execution job completed');
  },
  { timezone: 'UTC' }
);

