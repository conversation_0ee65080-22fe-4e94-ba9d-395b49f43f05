import axios from 'axios';
import cron from 'node-cron';
import { config } from '../configs/_index.js';
import { getZohoToken } from '../utils/update-zoho.js';
import { ShopModel } from '../models/_index.js';

cron.schedule(
  '21 12 * * *',
  async () => {
    try {
      if (config.env.IS_TESTING) return;

      console.log('Cron job started for sync app rating from zoho.');

      const zohoToken = await getZohoToken();

      let page = 1;
      let per_page = 200;
      const ratedAccounts = [];

      const requestConfig = {
        headers: { Authorization: `Zoho-oauthtoken ${zohoToken}`, 'Content-Type': 'application/json' },
      };

      while (true) {
        const endpoint = `https://www.zohoapis.com/crm/v4/Accounts/search?criteria=Grouptify_Rating:greater_than:0&page=${page}&per_page=${per_page}`;

        const ratedAccountResponse = await axios.get(endpoint, requestConfig);
        const queryData = ratedAccountResponse?.data?.data;
        const queryInfo = ratedAccountResponse?.data?.info;

        if (!queryData || !queryInfo) throw new Error('Zoho query error');

        if (queryData?.length) ratedAccounts.push(...queryData);
        if (!queryInfo.more_records) break;
        page++;
      }

      const updateOperations = ratedAccounts.map((account) => ({
        updateOne: {
          filter: { 'shopify_data.myshopify_domain': account.Shopify_URL },
          update: { $set: { 'app_data.rating_point': account.Grouptify_Rating } },
        },
      }));

      if (updateOperations.length > 0) {
        await ShopModel.bulkWrite(updateOperations);
      }

      console.log('Sync app rating successful.');
      //
    } catch (error) {
      console.error('Failed to sync app rating from zoho', error);
    }
  },
  { timezone: 'UTC' }
);

