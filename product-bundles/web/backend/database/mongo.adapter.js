import mongoose from 'mongoose';
import { config } from '../configs/_index.js';

const { IS_TESTING, MONGODB_URI } = config.env;

const connectDatabase = async () => {
  try {
    mongoose.Promise = global.Promise;
    mongoose.set('strictQuery', true);
    await mongoose.connect(MONGODB_URI);

    console.info('Successfully connected to the mongo database!');

    if (IS_TESTING) mongoose.set('debug', true);
    //
  } catch (error) {
    console.error('Connect database failed: ', error);
  }
};

export { connectDatabase };

