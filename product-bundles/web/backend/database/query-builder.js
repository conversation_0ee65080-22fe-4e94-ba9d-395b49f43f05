class QueryBuilder {
  // Initializes QueryBuilder with a Mongoose model.
  constructor(model) {
    this.model = model;
    this.query = this.model.find();
  }

  // Adds a filter to the query.
  where = (query = {}) => {
    this.query = this.query.find(query);
    return this;
  };

  // Specifies fields to include or exclude in results.
  select = (fields) => {
    if (fields) {
      this.query = this.query.select(fields);
    }
    return this;
  };

  // Populates references with related data.
  populate = (populateOptions) => {
    if (populateOptions) {
      this.query = this.query.populate(populateOptions);
    }
    return this;
  };

  // Sorts results by specified fields.
  sort = (sort = '-updatedAt -_id') => {
    this.query = this.query.sort(sort);
    return this;
  };

  // Limits the number of documents returned.
  limit = (limit = 250) => {
    this.query = this.query.limit(limit);
    return this;
  };

  // Skips a specified number of documents.
  skip = (skip = 0) => {
    this.query = this.query.skip(skip);
    return this;
  };

  // Paginates the results by calculating skip and limit.
  paginate = (page, page_size) => {
    if (!page || !page_size) {
      return this;
    }
    const skip = (page - 1) * page_size;
    this.query = this.query.skip(skip).limit(page_size);
    return this;
  };

  // Executes the query and returns a promise.
  exec = () => {
    return this.query.exec();
  };
}

export default QueryBuilder;

