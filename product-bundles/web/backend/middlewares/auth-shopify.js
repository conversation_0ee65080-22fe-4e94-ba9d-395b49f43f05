import { ShopService } from '../services/_index.js';
import shopify, { extraShopify } from '../../shopify.js';

export const getShopify = async (req, providedDomain) => {
  const myshopifyDomain = providedDomain || req.query?.shop;

  try {
    const shop = await ShopService.getByMyshopifyDomain(myshopifyDomain);

    if (shop?.app_data?.use_extra_scopes) {
      console.log('USE EXTRA SHOPIFY', extraShopify?.api?.config?.scopes);
      return extraShopify;
    }
    //
  } catch (error) {
    console.error('Error in getShopify: ', error);
    return shopify;
  }

  return shopify;
};

export const shopifyAuth = async (req, res, next) => {
  const shopifyToUse = await getShopify(req);
  return shopifyToUse.auth.begin()(req, res, next);
};

export const shopifyAuthCallback = async (req, res, next) => {
  const shopifyToUse = await getShopify(req);
  if (shopifyToUse.config && !shopifyToUse.config.useOnlineTokens) {
    shopifyToUse.auth.callback()(req, res, next);
  } else {
    next();
  }
};

export const customValidateAuthenticatedSession = async (req, res, next) => {
  const referer = req.headers['referer'];

  try {
    const url = new URL(referer);
    const shop = url.searchParams.get('shop');
    req.query.shop = shop;
    //
  } catch (error) {
    console.error('Error parsing referer URL: ', error);
    return res.status(400).json({ message: 'Invalid referer URL' });
  }

  const { shop } = req.query;
  const shopifyToUse = await getShopify(req, shop);
  return shopifyToUse.validateAuthenticatedSession()(req, res, next);
};

