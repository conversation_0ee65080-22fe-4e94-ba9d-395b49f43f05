import { ApiKeyService } from '../services/_index.js';

export const requirePermissions = (...permissions) => {
  return async (req, res, next) => {
    try {
      const apiKey = req.headers['gtf-api-key'];

      if (!apiKey) {
        return res.status(401).json({ message: 'Unauthorized: API key is missing' });
      }

      const existedKey = await ApiKeyService.getByKey(apiKey);

      if (!existedKey) {
        return res.status(401).json({ message: 'Unauthorized: Invalid API key' });
      }

      if (permissions.some((permission) => !existedKey.permissions.includes(permission))) {
        return res.status(403).json({
          message: `Forbidden: Your API key must have one of the following permissions: ${permissions.join(', ')}`,
        });
      }

      req.session = { _id: existedKey.shop };
      next();
      //
    } catch (error) {
      console.error('Verify request failed: ', error);
      return res.status(500).json({ message: 'Internal Server Error' });
    }
  };
};

