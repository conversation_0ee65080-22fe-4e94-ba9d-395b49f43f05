import { AuthService } from '../services/_index.js';

export const requireRoles = (...roles) => {
  return async (req, res, next) => {
    try {
      const token = req.headers['auth-token'];

      if (!token) {
        return res.status(401).json({ message: 'Unauthorized: Token is missing' });
      }

      const session = await AuthService.getSession({ token });

      if (!session) {
        return res.status(401).json({ message: 'Unauthorized: Invalid token or session' });
      }

      if (!roles.includes(session.role)) {
        return res.status(403).json({
          message: `Forbidden: Your token must have one of the following roles: ${roles.join(', ')}`,
        });
      }

      req.session = session;
      next();
      //
    } catch (error) {
      console.error('Verify request failed: ', error);
      return res.status(500).json({ message: 'Internal Server Error' });
    }
  };
};

