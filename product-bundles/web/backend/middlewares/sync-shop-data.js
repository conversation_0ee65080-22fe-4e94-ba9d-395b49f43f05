import moment from 'moment';
import { config } from '../configs/_index.js';
import { updateZoho } from '../utils/update-zoho.js';
import {
  ActionLogService,
  ApiKeyService,
  MerchantConfigService,
  PricingPlanService,
  RestShopifyService,
  ShopService,
} from '../services/_index.js';

import _ from 'lodash';
import fetch from 'node-fetch';
import { DEFAULT_PLAN_LIMITATION, DEFAULT_PLAN_LIST } from '../constants/pricing-plans.js';

const { IS_TESTING } = config.env;

export const syncShopData = async (req, res, next) => {
  try {
    const session = res.locals.shopify.session;
    const { accessToken, scope: accessScope, shop: myshopifyDomain } = session;

    console.log('SYNC DATA FOR SHOP: ' + myshopifyDomain);

    // Init shopify services
    const ShopifyService = {
      Rest: new RestShopifyService(myshopifyDomain, accessToken),
    };

    // get shop info from database
    let [spfShop, dbShop] = await Promise.all([
      ShopifyService.Rest.getShop(),
      ShopService.getByMyshopifyDomain(myshopifyDomain),
    ]);

    const currentDate = new Date();
    const currentTime = currentDate.getTime();
    const shopId = String(spfShop.id);
    req.sessionId = shopId;

    const shopToSave = {
      _id: shopId,
      shop_status: 1,
      access_token: accessToken,
      access_scope: accessScope,
      gtf_access_token: '',
      update_unix_time: currentTime,

      shopify_data: spfShop,
      app_data: {
        charge_id: '',
        charge_name: '',
        charge_status: '',
        plan_name: 'free_feb25',
        plan_display_name: 'Free',
        plan_amount: '0',
        plan_list: DEFAULT_PLAN_LIST,
        freeze_limitation: false,
        feature_limitation: DEFAULT_PLAN_LIMITATION,
        recurring_interval: '',
        payment_date: '',
        password: '',
        app_touched: false,
      },
    };

    // init survey for first time install
    if (!dbShop) {
      shopToSave.app_data.survey_status = 'pending';
      shopToSave.app_data.survey_response = {};
      shopToSave.app_data.survey_result = {};
    }

    if (!dbShop || dbShop.shop_status === 2) {
      shopToSave.installed_at = currentDate;
      shopToSave.install_unix_time = currentTime;
      shopToSave.uninstall_at = null;
      shopToSave.uninstall_unix_time = null;

      await ActionLogService.create({
        shop: shopId,
        type: 'other',
        message: 'Install app',
        detail: {},
      });

      console.log('INSTALLER', shopToSave);
    }

    if (dbShop) {
      shopToSave.app_data = { ...shopToSave.app_data, ...dbShop.app_data };

      if (!shopToSave.app_data.plan_list?.length) shopToSave.app_data.plan_list = DEFAULT_PLAN_LIST;
      if (!shopToSave.app_data.feature_limitation) shopToSave.app_data.feature_limitation = DEFAULT_PLAN_LIMITATION;

      if (!shopToSave.app_data.freeze_limitation) {
        const shopPlan = await PricingPlanService.getActivePlanByShop(shopId);
        if (shopPlan) shopToSave.app_data.feature_limitation = JSON.stringify(shopPlan.default_limitation);
      }

      // manage api key base on limitation
      const shopLimitation = JSON.parse(shopToSave.app_data.feature_limitation || '{}');

      if (shopLimitation.allowed_request_api_key) {
        const apiKey = await ApiKeyService.createByShop(shopId);
        shopToSave.gtf_access_token = apiKey.raw_key;
        //
      } else {
        await ApiKeyService.deleteByShop(shopId);
        shopToSave.gtf_access_token = '';
      }

      dbShop = await ShopService.updateById(shopId, shopToSave);
    }
    //
    else {
      dbShop = await ShopService.create(shopToSave);

      if (!IS_TESTING) {
        fetch('https://platform.shoffi.app/v1/newMerchant', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            api_key: 'pk_QS8McPU9',
            shopName: myshopifyDomain,
            appId: '164884840449',
            XFF: req.headers['x-forwarded-for'],
          }),
        }).catch((err) => console.log('Shoffi error', err));
      }
    }

    // move to next middleware
    next();

    // Register webhooks
    await ShopService.registerWebhooks(shopId);

    // sync config to theme app
    await MerchantConfigService.syncToThemeApp(shopId);
    await ShopService.syncToThemeApp(shopId);

    // sync data to zoho
    const zohoCreateTime = moment(dbShop.install_unix_time).utcOffset(7).format();

    const zohoData = {
      shopId: spfShop.id,
      accountData: {
        Account_Name: spfShop.name,
        Domain: spfShop.domain,
        Store_Creation_Time: spfShop.created_at,

        Shopify_Storeid: dbShop._id,
        Shopify_Email: spfShop.email,
        Shopify_URL: spfShop.myshopify_domain,
        Shopify_Plan_Name: spfShop.plan_name,
        Shopify_Plan_Displayname: spfShop.plan_display_name,
        Shopify_Password_Enabled: spfShop.password_enabled ? 'Yes' : 'No',
        Store_Password: dbShop?.app_data?.password || '',

        Grouptify_App_Status: 'installed',
        Grouptify_Plan: dbShop?.app_data?.plan_name,
        Grouptify_Charge_ID: dbShop?.app_data?.charge_id,
        Grouptify_Payment_Date: dbShop?.app_data?.payment_date
          ? moment(new Date(dbShop.app_data.payment_date)).utcOffset(7).format()
          : undefined,
        Grouptify_Plan_Amount: dbShop?.app_data?.plan_amount,
        Grouptify_Recurring_Interval: dbShop?.app_data?.recurring_interval,
        Grouptify_Feature_Limitation: dbShop?.app_data?.feature_limitation,
        Grouptify_App_Touched: dbShop?.app_data?.app_touched ? 'Yes' : 'No',
        Grouptify_Created_Time: zohoCreateTime,
      },

      contactData: {
        Account_Name: spfShop.name,
        First_Name: _.truncate(spfShop.shop_owner || 'Unknown', { length: 40 }),
        Last_Name: _.truncate(spfShop.shop_owner || 'MTM', { length: 40 }),
        Email: spfShop.email,
        Grouptify_Unique_ID: `${spfShop.id}.${spfShop.email}`,
        Grouptify_App_Status: 'installed',
        Grouptify_Plan: dbShop?.app_data?.plan_name,
        Grouptify_Created_Time: zohoCreateTime,
      },
    };

    if (spfShop.email !== spfShop.customer_email)
      zohoData.contactData2 = {
        Account_Name: spfShop.name,
        First_Name: _.truncate(spfShop.shop_owner || 'Unknown', { length: 40 }),
        Last_Name: _.truncate(spfShop.shop_owner || 'MTM', { length: 40 }),
        Email: spfShop.customer_email,
        Grouptify_Unique_ID: `${spfShop.id}.${spfShop.customer_email}`,
        Grouptify_App_Status: 'installed',
        Grouptify_Plan: dbShop?.app_data?.plan_name,
        Grouptify_Created_Time: zohoCreateTime,
      };

    await updateZoho(zohoData);
    //
  } catch (error) {
    console.error('Failed to sync shop data: ', error);
    return res.status(500).json({ message: error.message });
  }
};

