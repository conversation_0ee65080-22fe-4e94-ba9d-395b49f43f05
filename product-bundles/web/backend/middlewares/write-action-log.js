import { ActionLogService } from '../services/_index.js';

export const writeActionLog = (message, options = {}) => {
  return async (req, _res, next) => {
    try {
      const { params, query, originalUrl, method, body } = req;

      const shopId = req?.session?._id;
      const role = req?.session?.role;

      const isShop = shopId && role === 'shop';
      if (!isShop) return next();

      const logData = {
        shop: shopId,
        type: 'api_call',
        message,
        detail: { endpoint: originalUrl, method },
      };

      if (params) logData.detail.params = params;
      if (query) logData.detail.query = query;
      if (body && !options.skipBody) logData.detail.body = body;

      await ActionLogService.create(logData);
      console.log(`ACTION LOG - SHOP ${shopId}: ${message}`);

      next();
      //
    } catch (error) {
      console.error('Write action log failed: ', error);
      next(error);
    }
  };
};

