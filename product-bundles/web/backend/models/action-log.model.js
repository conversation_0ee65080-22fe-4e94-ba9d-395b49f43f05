import mongoose from 'mongoose';

const actionLogSchema = new mongoose.Schema(
  {
    shop: { type: String, required: true, index: true },
    type: { type: String, enum: ['api_call', 'cron_task', 'other'], default: 'api_call' },
    message: { type: String, required: false },
    detail: { type: mongoose.Schema.Types.Mixed, required: false },
  },
  { timestamps: true }
);

// TTL index to delete log after 30 days
actionLogSchema.index({ createdAt: 1 }, { expireAfterSeconds: 30 * 24 * 60 * 60 });

// Create index to optimize query by shop and time
actionLogSchema.index({ shop: 1, createdAt: -1 });

// Static method to add log and limit 200 logs
actionLogSchema.statics.addLogWithLimit = async function (logData) {
  const { shop } = logData;

  await this.create(logData);

  const logsToKeep = await this.find({ shop }).sort({ createdAt: -1 }).limit(200).select('_id');

  if (logsToKeep.length >= 200) {
    const idsToKeep = logsToKeep.map((log) => log._id);
    await this.deleteMany({ shop, _id: { $nin: idsToKeep } });
  }
};

const ActionLogModel = mongoose.model('ActionLog', actionLogSchema);

export default ActionLogModel;
