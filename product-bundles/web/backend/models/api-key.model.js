import mongoose from 'mongoose';

const apiKeySchema = new mongoose.Schema({
  shop: { type: String, required: true },
  raw_key: { type: String, required: true },
  permissions: [{ type: String, required: true }], // read_product_group, write_product_group
  created_at: { type: Date, default: Date.now },
  expires_at: { type: Date, required: true },
});

const ApiKeyModel = mongoose.model('ApiKey', apiKeySchema);

export default ApiKeyModel;
