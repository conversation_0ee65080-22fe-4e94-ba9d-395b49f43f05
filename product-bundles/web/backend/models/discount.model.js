import mongoose from 'mongoose';

const discountSchema = new mongoose.Schema({
  is_active: { type: Boolean, default: false },
  name: { type: String, required: true },
  display_name: { type: String, required: false },
  discount_code: { type: String, required: true },
  discount_percent: { type: Number, required: true },
  discount_interval: { type: Number, required: true },
  targeted_shop_ids: { type: String, required: false },
  targeted_tapita_plans: { type: String, required: false },
  targeted_shopify_plans: { type: String, required: false },
  used_count: { type: Number, default: 0 },
  expired_at: { type: Number, required: true },
});

const DiscountModel = mongoose.model('Discount', discountSchema);

export default DiscountModel;

