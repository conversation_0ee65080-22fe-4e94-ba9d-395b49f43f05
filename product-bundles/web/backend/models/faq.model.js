import mongoose from 'mongoose';

const faqSchema = new mongoose.Schema(
  {
    order: { type: Number, required: true, unique: true },
    question: { type: String, required: true },
    answer: { type: String, required: true },
    category: { type: String, required: false },
    tags: [{ type: String, required: true }],
  },
  { timestamps: true }
);

const FaqModel = mongoose.model('Faq', faqSchema);

export default FaqModel;
