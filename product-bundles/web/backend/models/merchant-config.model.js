import mongoose from 'mongoose';

const merchantConfigSchema = new mongoose.Schema({
  shop: { type: String, required: true },
  password: { type: String, required: false },
  custom_prod_js: { type: String, required: false },
  custom_card_js: { type: String, required: false },
  custom_prod_css: { type: String, required: false },
  custom_card_css: { type: String, required: false },
  storefront_access_token: { type: String, required: false },
  // show on product card
  show_on_home: { type: Boolean, required: true },
  show_on_blog: { type: Boolean, required: true },
  show_on_page: { type: Boolean, required: true },
  show_on_cart: { type: Boolean, required: true },
  show_on_search: { type: Boolean, required: true },
  show_on_article: { type: Boolean, required: true },
  show_on_product: { type: Boolean, required: true },
  show_on_collection: { type: Boolean, required: true },
  // email for contact
  contact_email: { type: String, required: false },
  // time wait for render
  timeout_home: { type: Number, required: true },
  timeout_blog: { type: Number, required: true },
  timeout_page: { type: Number, required: true },
  timeout_cart: { type: Number, required: true },
  timeout_search: { type: Number, required: true },
  timeout_article: { type: Number, required: true },
  timeout_product: { type: Number, required: true },
  timeout_collection: { type: Number, required: true },
  // timeout location change
  timeout_location_basf: { type: Number, required: true },
  timeout_location_prod: { type: Number, required: true },
  timeout_location_card: { type: Number, required: true },
  // timeout override
  timeout_override_xhr: { type: Number, required: true },
  timeout_override_fetch: { type: Number, required: true },
  timeout_override_push_state: { type: Number, required: true },
  timeout_override_replace_state: { type: Number, required: true },
  // turn theme app on or off
  app_enabled_1: { type: Boolean, default: true },
  app_enabled_2: { type: Boolean, default: true },
  swatch_enabled: { type: Boolean, default: false },
  browser_select_box_enabled: { type: Boolean, default: false },
  override_grouped_product_option: { type: Boolean, default: true },
  // product in group limit
  draft_product_in_group_allowed: { type: Boolean, default: false },
  hidden_product_in_group_allowed: { type: Boolean, default: false },
  archived_product_in_group_allowed: { type: Boolean, default: false },
  // resync setting
  resync_group_mode: { type: String, default: 'disable' },
  resync_group_interval: { type: String, default: '1d' },
  resync_group_time: { type: String, default: '00:00' },
  resync_group_times_per_day: { type: String, default: '1' },
  auto_update_stock_instantly: { type: Boolean, default: false },
  auto_update_automation_group: { type: Boolean, default: false },
  // ignore keywords for flying pages
  fp_ignore_keywords: [{ type: String, required: true }],
  // special case: boost ai search & filter app
  use_basf_app: { type: Boolean, required: true },
  timeout_basf_app: { type: Number, required: true },
  // hide option label on product card
  hide_card_option_label: { type: Boolean, default: false },
  // behavior when changing selected variant on product card
  change_variant_on_card_behavior: { type: String, default: 'change_content', enum: ['change_content', 'redirect'] },
  // rerender after request this url
  observe_urls: [
    {
      url: { type: String, required: false },
      page: {
        type: String,
        required: false,
        enum: ['article', 'blog', 'collection', 'cart', 'index', 'page', 'product', 'search'],
      },
      timeout: { type: Number, required: false },
    },
  ],
  // global style config
  prod_style: {
    margin_top: { type: Number, required: false },
    margin_bottom: { type: Number, required: false },
    option_spacing: { type: Number, required: false }, // spacing between 2 options
    label_spacing: { type: Number, required: false }, // spacing between label and option values

    show_label: { type: Boolean, default: true },
    label_color: { type: String, required: false },
    label_font_size: { type: Number, required: false },
    label_font_weight: { type: Number, required: false },

    show_label_value: { type: Boolean, default: true },
    label_value_color: { type: String, required: false },
    label_value_font_size: { type: Number, required: false },
    label_value_font_weight: { type: Number, required: false },
    label_value_connector: { type: String, required: false },

    value_limit_display_position: { type: String, required: false },
    value_limit_max: { type: Number, required: false },
    value_limit_text_color: { type: String, required: false },
    value_limit_font_size: { type: Number, required: false },
    value_limit_font_weight: { type: Number, required: false },
  },
  card_style: {
    margin_top: { type: Number, required: false },
    margin_bottom: { type: Number, required: false },
    option_spacing: { type: Number, required: false }, // spacing between 2 options
    label_spacing: { type: Number, required: false }, // spacing between label and option values

    show_label: { type: Boolean, default: true },
    label_color: { type: String, required: false },
    label_font_size: { type: Number, required: false },
    label_font_weight: { type: Number, required: false },

    show_label_value: { type: Boolean, default: true },
    label_value_color: { type: String, required: false },
    label_value_font_size: { type: Number, required: false },
    label_value_font_weight: { type: Number, required: false },
    label_value_connector: { type: String, required: false },

    value_limit_display_position: { type: String, required: false },
    value_limit_max: { type: Number, required: false },
    value_limit_text_color: { type: String, required: false },
    value_limit_font_size: { type: Number, required: false },
    value_limit_font_weight: { type: Number, required: false },
  },
});

const MerchantConfigModel = mongoose.model('MerchantConfig', merchantConfigSchema);

export default MerchantConfigModel;

