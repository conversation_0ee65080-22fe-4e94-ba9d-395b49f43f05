import mongoose from 'mongoose';

// border style
const borderSchema = new mongoose.Schema({
  color: { type: String, default: 'rgba(0,0,0,0.1)' },
  radius: { type: Number, default: 0 },
  style: {
    type: String,
    enum: ['none', 'solid', 'dashed', 'dotted', 'double', 'groove', 'ridge', 'inset', 'outset'],
    default: 'none',
  },
  width: { type: Number, default: 1 },
});

// background style
const backgroundSchema = new mongoose.Schema({
  type: { type: String, enum: ['none', 'color', 'gradient', 'pattern'], default: 'none' },
  color1: { type: String, default: 'rgba(0,0,0,0.1)' },
  color2: { type: String, default: 'rgba(0,0,0,0.1)' },
  angle: { type: Number, default: 0 },
  pattern: { type: String, required: false },
});

// text style
const textStyleSchema = new mongoose.Schema({
  font_size: { type: Number, default: 16 },
  font_family: { type: String, default: 'inherit' },
  is_bold: { type: Boolean, default: false },
  is_italic: { type: Boolean, default: false },
  is_underline: { type: Boolean, default: false },
  is_strikethrough: { type: Boolean, default: false },
});

// tier
const tierSchema = new mongoose.Schema({
  title: String,
  subtitle: String,
  label_text: String,
  badge_text: String,
  label_style: { type: String, enum: ['style_1', 'style_2', 'style_3'], default: 'style_1' },

  quantity_min: Number,
  quantity_max: Number,
  selected_by_default: { type: Boolean, default: false },

  discount_type: {
    type: String,
    enum: ['no_discount', 'fixed_discount', 'percentage_discount', 'fixed_product_price'],
    default: 'no_discount',
  },
  discount_value: Number,

  image_url: String,
  image_size: { type: Number, default: 100 },
  image_border: borderSchema,

  extra_offers: [
    {
      title: String,

      type: { type: String, enum: ['free_gift', 'free_shipping', 'upsell'], default: 'free_gift' },
      quantity: Number,
      always_visible: { type: Boolean, default: false },

      image_url: String,
      image_size: { type: Number, default: 100 },
      image_border: borderSchema,

      // for upsell
      selected_by_default: { type: Boolean, default: false },
      discount_type: {
        type: String,
        enum: ['no_discount', 'fixed_discount', 'percentage_discount', 'fixed_product_price'],
        default: 'no_discount',
      },
      discount_value: Number,

      // for free_gift
      variant_id: String,
    },
  ],
});

// Translation Schema
const translationSchema = new mongoose.Schema({
  language: String,
  texts: {
    title: String,
    subtitle: String,
    footer_text: String,
    atc_button_text: String,
    out_of_stock_text: String,
    tiers: [
      {
        title: String,
        subtitle: String,
        label_text: String,
        badge_text: String,
        extra_offers: [{ title: String }],
      },
    ],
  },
});

// Main Offer Schema
const offerSchema = new mongoose.Schema(
  {
    uuid: String,
    name: String,
    shop: String,
    type: { type: String, enum: ['quantity_break', 'volume_discount', 'quantity_discount'] },
    is_active: { type: Boolean, default: true },

    general: {
      title: String,
      subtitle: String,
      footer_text: String,
      atc_button_text: String,
      out_of_stock_text: String,

      apply_to_products: {
        type: String,
        enum: ['all_products', 'specific_products', 'custom_segment'],
        default: 'all_products',
      },
      applied_products: [{ type: String }],

      apply_to_customers: {
        type: String,
        enum: ['all_customers', 'custom_segment'],
        default: 'all_customers',
      },

      variant_selector_type: {
        type: String,
        enum: ['hidden', 'combined_select', 'separated_select'],
        default: 'combined_select',
      },
      default_selected_variant: {
        type: String,
        enum: [
          'default',
          'first_variant',
          'last_variant',
          'first_available',
          'last_available',
          'cheapest_available',
          'most_expensive_available',
        ],
        default: 'default',
      },

      atc_button_type: {
        type: String,
        enum: ['custom_button', 'theme_button'],
        default: 'custom_button',
      },

      atc_button_behavior: {
        type: String,
        enum: ['add_and_toast', 'add_and_go_to_cart_page', 'add_and_go_to_checkout_page', 'match_theme_behavior'],
        default: 'add_and_toast',
      },
      atc_message: { type: String, required: false },

      quantity_selector_position: {
        type: String,
        enum: ['hidden', 'above_title', 'above_tiers', 'above_footer'],
        default: 'hidden',
      },

      widget_position: {
        type: String,
        enum: ['above_form_actions', 'below_form_actions', 'custom_position'],
        default: 'above_form_actions',
      },
      widget_custom_positions: [
        {
          selector: String,
          position: {
            type: String,
            enum: ['beforebegin', 'afterbegin', 'beforeend', 'afterend'],
            default: 'beforebegin',
          },
        },
      ],
    },

    tiers: [tierSchema],

    translations: [translationSchema],

    styles: {
      layout: { type: String, enum: ['horizontal', 'vertical'], default: 'horizontal' },
      color_theme: { type: String, default: 'rgba(0,0,0,1)' },
      radio_button_type: { type: String, enum: ['hidden', 'radio', 'variant_image', 'custom_image'], default: 'radio' },

      widget_padding: { type: Number, default: 0 },
      widget_spacing: { type: Number, default: 0 },
      widget_border: borderSchema,
      widget_background: backgroundSchema,

      widget_title_color: { type: String, default: 'rgba(0,0,0,0.8)' },
      widget_title_text: textStyleSchema,
      widget_subtitle_color: { type: String, default: 'rgba(0,0,0,0.6)' },
      widget_subtitle_text: textStyleSchema,
      widget_footer_text_color: { type: String, default: 'rgba(0,0,0,0.6)' },
      widget_footer_text: textStyleSchema,
      widget_atc_button_color: { type: String, default: 'rgba(0,0,0,0.8)' },
      widget_atc_button_text: textStyleSchema,
      widget_out_stock_text_color: { type: String, default: 'rgba(0,0,0,0.6)' },
      widget_out_stock_text: textStyleSchema,

      tier_padding: { type: Number, default: 0 },
      tier_spacing: { type: Number, default: 0 },

      // normal tier
      tier_border: borderSchema,
      tier_background: backgroundSchema,

      tier_title_color: { type: String, default: 'rgba(0,0,0,0.8)' },
      tier_subtitle_color: { type: String, default: 'rgba(0,0,0,0.6)' },
      tier_sale_price_color: { type: String, default: 'rgba(255,0,0,1)' },
      tier_regular_price_color: { type: String, default: 'rgba(0,0,0,0.6)' },
      tier_compare_at_price_color: { type: String, default: 'rgba(0,0,0,0.4)' },
      tier_radio_button_color: { type: String, default: 'rgba(0,0,0,0.8)' },
      tier_label_text_color: { type: String, default: 'rgba(0,0,0,1)' },
      tier_label_background_color: { type: String, default: 'rgba(0,0,0,0.1)' },
      tier_badge_text_color: { type: String, default: 'rgba(0,0,0,1)' },
      tier_badge_background_color: { type: String, default: 'rgba(0,0,0,0.1)' },

      // selected block
      selected_tier_border: borderSchema,
      selected_tier_background: backgroundSchema,

      selected_tier_title_color: { type: String, default: 'rgba(0,0,0,0.8)' },
      selected_tier_subtitle_color: { type: String, default: 'rgba(0,0,0,0.6)' },
      selected_tier_sale_price_color: { type: String, default: 'rgba(255,0,0,1)' },
      selected_tier_regular_price_color: { type: String, default: 'rgba(0,0,0,0.6)' },
      selected_tier_compare_at_price_color: { type: String, default: 'rgba(0,0,0,0.4)' },
      selected_tier_radio_button_color: { type: String, default: 'rgba(0,0,0,0.8)' },
      selected_tier_label_text_color: { type: String, default: 'rgba(0,0,0,1)' },
      selected_tier_label_background_color: { type: String, default: 'rgba(0,0,0,0.1)' },
      selected_tier_badge_text_color: { type: String, default: 'rgba(0,0,0,1)' },
      selected_tier_badge_background_color: { type: String, default: 'rgba(0,0,0,0.1)' },

      // typography
      tier_title_text: textStyleSchema,
      tier_subtitle_text: textStyleSchema,
      tier_sale_price_text: textStyleSchema,
      tier_regular_price_text: textStyleSchema,
      tier_compare_at_price_text: textStyleSchema,
      tier_label_text: textStyleSchema,
      tier_badge_text: textStyleSchema,
    },

    settings: {
      show_price_per_unit: { type: Boolean, default: false },
      compare_at_price_type: {
        type: String,
        enum: ['hidden', 'show_if_available', 'show_custom_price'],
        default: 'hidden',
      },
      custom_compare_at_price: Number,

      price_rounding: { type: String, enum: ['none', '.99', '.95', '.90', '.x9', '.x0', '.00'], default: 'none' },

      apply_discount_to_exact_quantity: { type: Boolean, default: false },
      limit_one_use_per_customer: { type: Boolean, default: false },
      limit_total_uses_of_offer: { type: Boolean, default: false },
      total_uses: { type: Number, default: 0 },

      combined_with_order_discount: { type: Boolean, default: false },
      combined_with_product_discount: { type: Boolean, default: false },
      combined_with_shipping_discount: { type: Boolean, default: false },

      start_date: Date,
      end_date: Date,
      use_end_date: { type: Boolean, default: false },
    },

    product_conditions: [
      {
        type: {
          type: String,
          enum: ['products_by_tag', 'products_by_type', 'products_by_vendor', 'products_by_collection'],
          default: 'products_by_tag',
        },
        operator: {
          type: String,
          enum: ['include_all', 'include_any', 'exclude_all', 'exclude_any'],
          default: 'include_all',
        },
        products_tags: [String],
        products_types: [String],
        products_vendors: [String],
        products_collections: [String],
      },
    ],

    customer_conditions: [
      {
        type: {
          type: String,
          enum: [
            'customers_by_login_status',
            'customers_by_tag',
            'customers_by_country',
            'customers_by_amount_spent',
            'customers_by_order_count',
          ],
          default: 'customers_by_login_status',
        },
        operator: {
          type: String,
          enum: ['include_all', 'include_any', 'exclude_all', 'exclude_any', 'is', 'is_not'],
          default: 'include_all',
        },
        login_status: {
          type: String,
          enum: ['logged_in', 'logged_out'],
          default: 'logged_in',
        },
        customer_tags: [String],
        customer_countries: [String],
        customer_min_amount_spent: { type: Number, default: 0 },
        customer_max_amount_spent: { type: Number, default: 0 },
        customer_min_order_count: { type: Number, default: 0 },
        customer_max_order_count: { type: Number, default: 0 },
      },
    ],
  },
  { timestamps: true }
);

const OfferModel = mongoose.model('Offer', offerSchema);

export default OfferModel;

