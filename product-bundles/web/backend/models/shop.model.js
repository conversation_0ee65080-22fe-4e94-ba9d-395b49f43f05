import mongoose, { Schema } from 'mongoose';

const shopSchema = new Schema(
  {
    _id: { type: String, required: true },
    shop_status: { type: Number, required: false }, // 1 - installed  2 - uninstalled, 3 - closed store,  4 - api_404 , other: enabled
    access_token: { type: String, required: true },
    access_scope: { type: String, required: false },
    gtf_access_token: { type: String, required: false },

    installed_at: { type: Date, required: false },
    uninstalled_at: { type: Date, required: false },

    install_unix_time: { type: Number, required: false },
    uninstall_unix_time: { type: Number, required: false },
    update_unix_time: { type: Number, required: false },

    shopify_data: {
      name: { type: String, required: false },
      email: { type: String, required: false },
      domain: { type: String, required: false },
      province: { type: String, required: false },
      country: { type: String, required: false },
      address1: { type: String, required: false },
      zip: { type: String, required: false },
      city: { type: String, required: false },
      source: { type: String, required: false },
      phone: { type: String, required: false },
      latitude: { type: Number, required: false },
      longitude: { type: Number, required: false },
      primary_locale: { type: String, required: false },
      address2: { type: String, required: false },
      created_at: { type: Date, required: false },
      updated_at: { type: Date, required: false },
      country_code: { type: String, required: false },
      country_name: { type: String, required: false },
      currency: { type: String, required: false },
      customer_email: { type: String, required: false },
      timezone: { type: String, required: false },
      iana_timezone: { type: String, required: false },
      shop_owner: { type: String, required: false },
      money_format: { type: String, required: false },
      money_with_currency_format: { type: String, required: false },
      plan_name: { type: String, required: false },
      plan_display_name: { type: String, required: false },
      myshopify_domain: { type: String, required: false },
      money_in_emails_format: { type: String, required: false },
      money_with_currency_in_emails_format: { type: String, required: false },
      password_enabled: { type: Boolean, required: false },
    },

    app_data: {
      charge_id: { type: String, required: false },
      charge_name: { type: String, required: false },
      charge_status: { type: String, required: false },
      plan_name: { type: String, required: false },
      plan_display_name: { type: String, required: false },
      plan_amount: { type: String, required: false },
      plan_list: [{ type: String, required: false }],
      freeze_limitation: { type: Boolean, default: false },
      feature_limitation: { type: String, required: false },
      recurring_interval: { type: String, required: false },
      payment_date: { type: String, required: false },
      trial_status: { type: Number, default: 0 }, // 0 - trial not started; 1 - trial started; 2 - trial ended; 3 - paid after trial
      trial_end: { type: String, required: false },

      rating_point: { type: String, required: false },
      theme_name: { type: String, required: false },
      theme_supported: { type: Boolean, default: false },

      app_enabled: { type: Boolean, default: false },
      app_touched: { type: Boolean, default: false },
      swatch_on_pdp_enabled: { type: Boolean, default: false }, // product detail page
      swatch_on_cdp_enabled: { type: Boolean, default: false }, // collection page

      group_count: { type: Number, default: 0 },
      group_count_manual_single: { type: Number, default: 0 },
      group_count_manual_multiple: { type: Number, default: 0 },
      group_count_automation_single: { type: Number, default: 0 },
      group_count_automation_multiple: { type: Number, default: 0 },
      product_count: { type: Number, default: 0 },
      active_group_count: { type: Number, default: 0 },
      active_product_count: { type: Number, default: 0 },
      password: { type: String, required: false },

      use_extra_scopes: { type: Boolean, default: false },
      installed_competitors: { type: String, required: false },

      survey_status: { type: String, enum: ['pending', 'completed', 'skipped', 'aborted'], required: false },
      survey_response: { type: Schema.Types.Mixed, required: false },
      survey_result: { type: Schema.Types.Mixed, required: false },

      tags: [{ type: String, required: false }],
    },

    custom_data: { type: Schema.Types.Mixed, required: false },
  },
  { timestamps: true }
);

const ShopModel = mongoose.model('Shop', shopSchema);

export default ShopModel;
