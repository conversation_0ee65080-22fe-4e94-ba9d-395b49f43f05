import mongoose from 'mongoose';

const taskSchema = new mongoose.Schema(
  {
    shop: { type: String, required: true },
    type: { type: String, required: true }, // resync_group
    data: { type: mongoose.Schema.Types.Mixed, required: false },
    status: { type: String, required: true }, // date_scheduled / time_scheduled / completed / failed / disabled
    auto_schedule: { type: Boolean, default: true },
    executed_date: { type: String, default: '2001-01-01' }, // date format as yyyy-mm-dd
    executed_time: { type: String, default: '00:00' }, // time format as hh:mm
  },
  { timestamps: true }
);

const TaskModel = mongoose.model('Task', taskSchema);

export default TaskModel;
