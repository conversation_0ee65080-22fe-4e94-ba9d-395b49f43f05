import _ from 'lodash';
import { ActionLogService, ShopService } from '../services/_index.js';
import { updateZoho } from '../utils/update-zoho.js';

export const changeTheme = async (req, res) => {
  try {
    res.status(200).send('OK');

    console.log('CHANGE THEME WEBHOOK PROCESSING . . .');

    const myshopifyDomain = req.headers['x-shopify-shop-domain'];
    if (!myshopifyDomain) throw new Error('Shop domain is missing in headers');

    console.log('STORE: ', myshopifyDomain);

    const foundShop = await ShopService.getByMyshopifyDomain(myshopifyDomain);
    if (!foundShop) throw new Error('Shop not found');

    // write action log
    await ActionLogService.create({
      shop: foundShop._id,
      type: 'other',
      message: 'Change theme',
      detail: {},
    });

    // const themeInfo = await ThemeSettingService.syncToThemeApp(foundShop._id);
    const themeInfo = {};

    // update zoho
    const zohoData = {
      shopId: foundShop._id,
      accountData: {
        Grouptify_Theme_Schema_Name: themeInfo.theme_name,
        Grouptify_Theme_Name: themeInfo.theme_display_name,
      },
      contactData: {
        First_Name: _.truncate(foundShop?.shopify_data?.shop_owner || 'Unknown', { length: 40 }),
        Last_Name: _.truncate(foundShop?.shopify_data?.shop_owner || 'MTM', { length: 40 }),
        Email: foundShop?.shopify_data?.email,
        Grouptify_Theme_Schema_Name: themeInfo.theme_name,
        Grouptify_Theme_Name: themeInfo.theme_display_name,
      },
    };

    if (foundShop?.shopify_data?.email !== foundShop?.shopify_data?.customer_email)
      zohoData.contactData2 = {
        First_Name: _.truncate(foundShop?.shopify_data?.shop_owner || 'Unknown', { length: 40 }),
        Last_Name: _.truncate(foundShop?.shopify_data?.shop_owner || 'MTM', { length: 40 }),
        Email: foundShop?.shopify_data?.customer_email,
        Grouptify_Theme_Schema_Name: themeInfo.theme_name,
        Grouptify_Theme_Name: themeInfo.theme_display_name,
      };

    await updateZoho(zohoData);

    console.log('CHANGE THEME WEBHOOK SUCCEEDED');
    //
  } catch (error) {
    console.error('CHANGE THEME WEBHOOK FAILED: ', error);
  }
};

