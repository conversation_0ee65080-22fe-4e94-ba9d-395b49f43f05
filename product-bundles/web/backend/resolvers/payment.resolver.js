import _ from 'lodash';
import { ActionLogService, DiscountService, PricingPlanService, ShopService } from '../services/_index.js';
import { initRestShopifyService } from '../services/shopify/rest-shopify.service.js';
import { updateZoho } from '../utils/update-zoho.js';
import { config } from '../configs/_index.js';
import moment from 'moment';

export const payment = async (req, res) => {
  try {
    const shopId = req.query.shop_id;
    const planId = req.query.plan_id;
    const chargeId = req.query.charge_id;
    const discountId = req.query.discount_id;

    const [shop, plan, RestShopifyService] = await Promise.all([
      ShopService.getById(shopId),
      PricingPlanService.getPlanByShopAndId(shopId, planId),
      initRestShopifyService(shopId),
    ]);

    // verify charge
    const chargeData = await RestShopifyService.getRecurringApplicationChargeById(chargeId);

    if (!shop || !chargeData?.status) {
      return res.status(400).json({
        message: 'Invalid charge',
      });
    }

    if (chargeData?.status !== 'active') {
      return res.status(400).json({
        message: 'Impossible!',
      });
    }

    console.log(`LIMITATION CHANGE - ${shopId} - payment`);

    // update plan
    await ShopService.updateById(shopId, {
      $set: {
        update_unix_time: new Date().getTime(),
        'app_data.charge_id': chargeData?.id,
        'app_data.charge_name': chargeData?.name,
        'app_data.payment_date': new Date().toISOString(),
        'app_data.charge_status': chargeData?.status,
        'app_data.plan_name': plan?.plan_name,
        'app_data.plan_amount': chargeData?.price,
        'app_data.plan_display_name': plan?.plan_display_name,
        'app_data.recurring_interval': plan?.recurring_interval,
        'app_data.freeze_limitation': false,
        'app_data.feature_limitation': JSON.stringify(plan?.default_limitation),
        'app_data.trial_status': 3,
      },
    });

    // write action log
    await ActionLogService.create({
      shop: shopId,
      type: 'other',
      message: `Subscribe to ${plan?.plan_name} plan ($${chargeData?.price})`,
      detail: {
        plan_name: plan?.plan_name,
        plan_amount: chargeData?.price,
        charge_id: chargeData?.id,
        charge_name: chargeData?.name,
        charge_status: chargeData?.status,
        charge_price: chargeData?.price,
        charge_interval: chargeData?.interval,
      },
    });

    await ShopService.syncToThemeApp(shopId);
    if (discountId) await DiscountService.updateById(discountId, { $inc: { used_count: 1 } });

    // redirect to app
    res.redirect(`https://${shop?.shopify_data?.myshopify_domain}/admin/apps/${config.env.CLIENT_ID}`);

    // update zoho
    const zohoData = {
      shopId: shop._id,
      accountData: {
        Grouptify_Plan: plan?.plan_name,
        Grouptify_Charge_ID: chargeData?.id,
        Grouptify_Payment_Date: moment(new Date()).utcOffset(7).format(),
        Grouptify_Plan_Amount: chargeData?.price,
        Grouptify_Recurring_Interval: plan?.recurring_interval,
        Grouptify_Feature_Limitation: JSON.stringify(plan?.default_limitation),
      },
      contactData: {
        First_Name: _.truncate(shop?.shopify_data?.shop_owner || 'Unknown', { length: 40 }),
        Last_Name: _.truncate(shop?.shopify_data?.shop_owner || 'MTM', { length: 40 }),
        Email: shop?.shopify_data?.email,
        Grouptify_Plan: plan?.plan_name,
      },
    };

    if (shop?.shopify_data?.email !== shop?.shopify_data?.customer_email)
      zohoData.contactData2 = {
        First_Name: _.truncate(shop?.shopify_data?.shop_owner || 'Unknown', { length: 40 }),
        Last_Name: _.truncate(shop?.shopify_data?.shop_owner || 'MTM', { length: 40 }),
        Email: shop?.shopify_data?.customer_email,
        Grouptify_Plan: plan?.plan_name,
      };

    await updateZoho(zohoData);
    //
  } catch (error) {
    console.error(`Payment failed (${req.query.shop_id})`, error);
  }
};

