import _ from 'lodash';
import { updateZoho } from '../utils/update-zoho.js';
import { ActionLogService, ShopService } from '../services/_index.js';
import { DEFAULT_PLAN_LIMITATION, DEFAULT_PLAN_LIST } from '../constants/pricing-plans.js';
import { ApiKeyModel, MerchantConfigModel, TaskModel } from '../models/_index.js';

export const uninstall = async (req, res) => {
  try {
    res.status(200).send('OK');

    console.log('UNINSTALL WEBHOOK PROCESSING . . .');

    const myshopifyDomain = req.headers['x-shopify-shop-domain'];
    if (!myshopifyDomain) throw new Error('Shop domain is missing in headers');

    console.log('STORE: ', myshopifyDomain);

    const foundShop = await ShopService.getByMyshopifyDomain(myshopifyDomain);
    if (!foundShop) throw new Error('Shop not found');

    const shopId = foundShop._id;
    console.log(`LIMITATION CHANGE - ${shopId} - uninstall`);

    // update shop
    await ShopService.updateById(foundShop._id, {
      $set: {
        shop_status: 2,
        uninstalled_at: new Date(),
        uninstall_unix_time: new Date().getTime(),
        update_unix_time: new Date().getTime(),
        'app_data.charge_id': '',
        'app_data.charge_name': '',
        'app_data.charge_status': '',
        'app_data.plan_name': 'free_feb25',
        'app_data.plan_display_name': 'Free',
        'app_data.plan_amount': '',
        'app_data.plan_list': DEFAULT_PLAN_LIST,
        'app_data.freeze_limitation': false,
        'app_data.feature_limitation': DEFAULT_PLAN_LIMITATION,
        'app_data.recurring_interval': '',
        'app_data.payment_date': '',
        'app_data.trial_status': 0,
      },
    });

    // write action log
    await ActionLogService.create({
      shop: shopId,
      type: 'other',
      message: 'Uninstall app',
      detail: {},
    });

    // update zoho
    const zohoData = {
      shopId: foundShop._id,
      accountData: {
        Grouptify_App_Status: 'uninstalled',
        Grouptify_Plan: 'free_feb25',
        Grouptify_Charge_ID: '',
        Grouptify_Payment_Date: '',
        Grouptify_Plan_Amount: '',
        Grouptify_Recurring_Interval: '',
        Grouptify_Feature_Limitation: DEFAULT_PLAN_LIMITATION,
      },
      contactData: {
        First_Name: _.truncate(foundShop?.shopify_data?.shop_owner || 'Unknown', { length: 40 }),
        Last_Name: _.truncate(foundShop?.shopify_data?.shop_owner || 'MTM', { length: 40 }),
        Email: foundShop?.shopify_data?.email,
        Grouptify_App_Status: 'uninstalled',
        Grouptify_Plan: 'free_feb25',
      },
    };

    if (foundShop?.shopify_data?.email !== foundShop?.shopify_data?.customer_email)
      zohoData.contactData2 = {
        First_Name: _.truncate(foundShop?.shopify_data?.shop_owner || 'Unknown', { length: 40 }),
        Last_Name: _.truncate(foundShop?.shopify_data?.shop_owner || 'MTM', { length: 40 }),
        Email: foundShop?.shopify_data?.customer_email,
        Grouptify_App_Status: 'uninstalled',
        Grouptify_Plan: 'free_feb25',
      };

    await updateZoho(zohoData);

    // clean data
    const cleanDataPromises = [
      ApiKeyModel.deleteMany({ shop: shopId }),
      MerchantConfigModel.deleteMany({ shop: shopId }),
      TaskModel.deleteMany({ shop: shopId }),
    ];

    await Promise.all(cleanDataPromises);

    console.log('UNINSTALL WEBHOOK SUCCEEDED');
    //
  } catch (error) {
    console.error('UNINSTALL WEBHOOK FAILED: ', error);
  }
};

