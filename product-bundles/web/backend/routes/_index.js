export { default as faqRoutes } from './faq.routes.js';
export { default as adminRoutes } from './admin.routes.js';
export { default as appDataRoutes } from './app-data.routes.js';
export { default as appConfigRoutes } from './app-config.routes.js';
export { default as discountRoutes } from './discount.routes.js';
export { default as actionLogRoutes } from './action-log.routes.js';
export { default as releaseNoteRoutes } from './release-note.routes.js';
export { default as pricingPlanRoutes } from './pricing-plan.routes.js';
export { default as merchantConfigRoutes } from './merchant-config.routes.js';
export { default as recommendedAppRoutes } from './recommended-app.routes.js';

export { default as fileRoutes } from './file.routes.js';
export { default as shopRoutes } from './shop.routes.js';
export { default as offerRoutes } from './offer.routes.js';
export { default as reportRoutes } from './report.routes.js';
export { default as webhookRoutes } from './webhook.routes.js';

