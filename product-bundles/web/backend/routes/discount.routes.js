import { Router } from 'express';
import { DiscountController } from '../controllers/_index.js';
import { requireRoles, writeActionLog } from '../middlewares/_index.js';

const router = new Router();

router.post('/validate', requireRoles('shop'), writeActionLog('Validate a coupon code'), DiscountController.validate);

router.use(requireRoles('admin'));
router.get('/', DiscountController.get);
router.post('/', DiscountController.create);
router.get('/:id', DiscountController.getById);
router.put('/:id', DiscountController.updateById);
router.delete('/:id', DiscountController.deleteById);
router.post('/:id/duplicate', DiscountController.duplicateById);

export default router;

