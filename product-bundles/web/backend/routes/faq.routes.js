import { Router } from 'express';
import { FaqController } from '../controllers/_index.js';
import { requireRoles } from '../middlewares/_index.js';

const router = new Router();

router.use(requireRoles('admin', 'shop'));
router.post('/', FaqController.create);
router.get('/:id', FaqController.getById);
router.put('/:id', FaqController.updateById);
router.delete('/:id', FaqController.deleteById);
router.post('/:id/duplicate', FaqController.duplicateById);

router.use(requireRoles('admin', 'shop'));
router.get('/', FaqController.get);

export default router;

