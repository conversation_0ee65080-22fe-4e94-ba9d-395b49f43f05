import { Router } from 'express';
import { MerchantConfigController } from '../controllers/_index.js';
import { requireRoles, writeActionLog } from '../middlewares/_index.js';

const router = new Router();

router.use(requireRoles('shop'));
router.get('/', MerchantConfigController.get);
router.put('/', writeActionLog('Update the merchant config', { skipBody: true }), MerchantConfigController.update);

export default router;

