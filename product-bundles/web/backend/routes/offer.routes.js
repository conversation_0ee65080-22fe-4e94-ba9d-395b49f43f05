import { Router } from 'express';
import { OfferController } from '../controllers/_index.js';
import { requireRoles } from '../middlewares/_index.js';

const router = new Router();

router.use(requireRoles('admin', 'shop'));
router.get('/', OfferController.get);
router.post('/', OfferController.create);
router.get('/:id', OfferController.getById);
router.put('/:id', OfferController.updateById);
router.delete('/:id', OfferController.deleteById);
router.post('/:id/duplicate', OfferController.duplicateById);

export default router;

