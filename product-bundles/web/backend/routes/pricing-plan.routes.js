import { Router } from 'express';
import { PricingPlanController } from '../controllers/_index.js';
import { requireRoles, writeActionLog } from '../middlewares/_index.js';

const router = new Router();

router.get('/', requireRoles('admin'), PricingPlanController.getAllPlans);
router.get('/shop', requireRoles('shop'), PricingPlanController.getShopPlans);
router.get('/shop/active', requireRoles('shop'), PricingPlanController.getShopActivePlan);
router.post('/shop/trial', requireRoles('shop'), PricingPlanController.startTrial);
router.post(
  '/:id/charges',
  requireRoles('shop'),
  writeActionLog('Try to switch plan'),
  PricingPlanController.createChargeByPlan
);

export default router;

