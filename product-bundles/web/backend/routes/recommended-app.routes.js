import { Router } from 'express';
import { RecommendedAppController } from '../controllers/_index.js';
import { requireRoles } from '../middlewares/_index.js';

const router = new Router();

router.use(requireRoles('admin'));
router.post('/', RecommendedAppController.create);
router.get('/:id', RecommendedAppController.getById);
router.put('/:id', RecommendedAppController.updateById);
router.delete('/:id', RecommendedAppController.deleteById);
router.post('/:id/duplicate', RecommendedAppController.duplicateById);

router.use(requireRoles('admin', 'shop'));
router.get('/', RecommendedAppController.get);

export default router;

