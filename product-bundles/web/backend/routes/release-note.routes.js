import { Router } from 'express';
import { ReleaseNoteController } from '../controllers/_index.js';
import { requireRoles } from '../middlewares/_index.js';

const router = new Router();

router.use(requireRoles('admin'));
router.post('/', ReleaseNoteController.create);
router.get('/:id', ReleaseNoteController.getById);
router.put('/:id', ReleaseNoteController.updateById);
router.delete('/:id', ReleaseNoteController.deleteById);
router.post('/:id/duplicate', ReleaseNoteController.duplicateById);

router.use(requireRoles('admin', 'shop'));
router.get('/', ReleaseNoteController.get);

export default router;

