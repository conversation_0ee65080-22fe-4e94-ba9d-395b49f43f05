import { Router } from 'express';
import { ShopController } from '../controllers/_index.js';
import { requireRoles, writeActionLog } from '../middlewares/_index.js';

const router = new Router();

router.get('/', requireRoles('shop'), ShopController.get);
router.post(
  '/regenerate-api-key',
  requireRoles('shop'),
  writeActionLog('Regenerate the API key'),
  ShopController.regenerateApiKey
);
router.post('/save-survey-result', requireRoles('shop'), ShopController.saveSurveyResult);

router.use(requireRoles('admin'));
router.post('/sync', ShopController.sync);
router.get('/search', ShopController.search);
router.get('/:id', ShopController.getById);
router.put('/:id', ShopController.updateById);
router.get('/:id/competitors', ShopController.getCompetitors);

export default router;

