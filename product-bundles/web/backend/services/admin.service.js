import { AdminModel } from '../models/_index.js';
import { generateHash } from '../utils/generate-hash.js';
import AuthService from './auth.service.js';
import { config } from '../configs/_index.js';

class AdminService {
  constructor() {
    this.model = AdminModel;
    this.authService = AuthService;
  }

  create = async (data) => {
    return this.model.create(data);
  };

  get = async () => {
    try {
      const admins = await this.model.find({});

      if (admins.length) return admins;

      await this.create({
        username: config.env.ADMIN_USERNAME,
        password: generateHash(config.env.ADMIN_PASSWORD),
        role: 'admin',
      });

      return this.model.find({});
      //
    } catch (error) {
      console.error('Failed to get admins: ', error);
    }
  };

  getById = (id) => {
    return this.model.findById(id);
  };
}

export default new AdminService();

