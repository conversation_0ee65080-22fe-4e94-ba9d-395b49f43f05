import crypto from 'crypto';
import { ApiKeyModel } from '../models/_index.js';

class ApiKeyService {
  constructor() {
    this.model = ApiKeyModel;
  }

  getByKey = async (apiKey) => {
    return this.model.findOne({ raw_key: apiKey });
  };

  getByShop = async (shopId) => {
    return this.model.findOne({ shop: shopId });
  };

  createByShop = async (shopId) => {
    try {
      const existedKey = await this.model.findOne({ shop: shopId });
      if (existedKey) return existedKey;

      const apiKey = 'gtf_' + crypto.randomBytes(32).toString('hex').slice(0, 44);
      const expiresDate = new Date();
      expiresDate.setFullYear(expiresDate.getFullYear() + 100); // expires after 100 years

      const keyData = {
        shop: shopId,
        raw_key: apiKey,
        permissions: ['read_product_group', 'write_product_group'],
        expires_at: expiresDate,
      };

      const createdKey = await this.model.create(keyData);
      return createdKey;
      //
    } catch (error) {
      console.log(`Failed to create API key for shop ${shopId}`, error);
    }
  };

  refreshByShop = async (shopId) => {
    try {
      const apiKey = 'gtf_' + crypto.randomBytes(32).toString('hex').slice(0, 44);

      const existedKey = await this.model.findOne({ shop: shopId });
      if (!existedKey) return this.createByShop(shopId);

      existedKey.raw_key = apiKey;
      await existedKey.save();
      return existedKey;
      //
    } catch (error) {
      console.log(`Failed to refresh API key for shop ${shopId}`, error);
    }
  };

  deleteByShop = async (shopId) => {
    return this.model.deleteMany({ shop: shopId });
  };

  create = async (data) => {
    return this.model.create(data);
  };

  get = async () => {
    return this.model.find({});
  };

  getById = (id) => {
    return this.model.findById(id);
  };
}

export default new ApiKeyService();

