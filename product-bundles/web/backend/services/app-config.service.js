import { AppConfigModel } from '../models/_index.js';

class AppConfigService {
  constructor() {
    this.model = AppConfigModel;
  }

  get = async () => {
    try {
      const appConfig = await this.model.findOne();
      if (appConfig) return appConfig;

      return await this.upsert({ support_enabled: true });
      //
    } catch (error) {
      console.error('Failed to get app config: ', error);
    }
  };

  upsert = (data) => {
    return this.model.findOneAndUpdate({}, data, { new: true, upsert: true, setDefaultsOnInsert: true });
  };
}

export default new AppConfigService();

