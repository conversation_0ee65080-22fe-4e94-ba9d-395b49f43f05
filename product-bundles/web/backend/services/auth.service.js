import { AuthModel } from '../models/_index.js';
import { randomString } from '../utils/random.js';

class AuthService {
  constructor() {
    this.model = AuthModel;
  }

  getSession = async ({ id, role, token }) => {
    const currentTime = Date.now();
    let session = await this.model.findOne({ $or: [{ token }, { $and: [{ _id: id }, { role }] }] }).exec();

    let tokenExpired = false;

    if (session && role === 'shop') {
      tokenExpired = Math.abs(currentTime - session.created_at) > 86400 * 1000 * 30;
    }

    if ((!session || tokenExpired) && id) {
      session = await this.model.findOneAndUpdate(
        { _id: id },
        { role, token: randomString(32), created_at: currentTime },
        { new: true, upsert: true }
      );
    }

    return session;
  };
}

export default new AuthService();

