import QueryBuilder from '../../database/query-builder.js';

// BaseService for MongoDB interactions with common CRUD operations.
class BaseService {
  // Initializes the service with a Mongoose model and optional query builder.
  constructor(model) {
    this.model = model;
  }

  // Creates a new instance of the query builder with this service's model.
  createQuery = () => {
    return new QueryBuilder(this.model);
  };

  // Retrieves documents based on specified options.
  get = async (options = {}) => {
    const results = await this.createQuery()
      .where(options.query)
      .select(options.select)
      .populate(options.populate)
      .sort(options.sort)
      .paginate(options.page, options.page_size)
      .exec();

    const currentPage = Number(options.page);
    const resultsPerPage = Number(options.page_size);
    const totalResults = await this.model.countDocuments(options.query);
    const totalPages = Math.ceil(totalResults / resultsPerPage);

    return {
      results,
      pageInfo: {
        resultsPerPage,
        totalResults,
        currentPage,
        totalPages,
        hasNext: currentPage < totalPages,
        hasPrev: currentPage > 1,
      },
    };
  };

  // Retrieves a single document by its ID with optional query customization.
  getById = async (id) => {
    return this.model.findById(id).exec();
  };

  // Creates a new document in the database.
  create = async (data) => {
    return this.model.create(data);
  };

  // Updates a document by its ID with the provided data.
  updateById = async (id, data) => {
    return this.model.findByIdAndUpdate(id, data, { new: true }).exec();
  };

  // Deletes a document by its ID.
  deleteById = async (id) => {
    return this.model.findByIdAndDelete(id).exec();
  };

  // Count documents by query
  count = (query = {}) => {
    return this.model.countDocuments(query);
  };
}

export default BaseService;

