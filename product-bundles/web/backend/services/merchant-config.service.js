import { MerchantConfigModel } from '../models/_index.js';
import { initGraphqlShopifyService } from './shopify/graphql-shopify.service.js';
import { initRestShopifyService } from './shopify/rest-shopify.service.js';

class MerchantConfigService {
  constructor() {
    this.model = MerchantConfigModel;
  }

  get = async (shop) => {
    try {
      let merchantConfig = await this.model.findOne({ shop });

      // if storefront_access_token existed, return
      if (merchantConfig?.storefront_access_token) return merchantConfig;

      // if not, create access token
      const GraphqlShopifyService = await initGraphqlShopifyService(shop);
      const storefrontAccessToken = await GraphqlShopifyService.createStorefrontAccessToken();

      // if merchant config does not exist, create one
      if (!merchantConfig) {
        return await this.upsert(shop, {
          shop,
          password: '',
          custom_prod_js: '',
          custom_card_js: '',
          custom_card_css: '',
          custom_prod_css: '',
          storefront_access_token: storefrontAccessToken?.accessToken,

          show_on_article: true,
          show_on_blog: true,
          show_on_cart: true,
          show_on_collection: true,
          show_on_home: true,
          show_on_page: true,
          show_on_product: true,
          show_on_search: true,

          timeout_article: 0,
          timeout_blog: 0,
          timeout_cart: 0,
          timeout_collection: 0,
          timeout_home: 0,
          timeout_page: 0,
          timeout_product: 0,
          timeout_search: 0,

          timeout_location_basf: 1000,
          timeout_location_card: 1000,
          timeout_location_prod: 0,

          timeout_override_fetch: 0,
          timeout_override_push_state: 0,
          timeout_override_replace_state: 0,
          timeout_override_xhr: 0,

          app_enabled_1: true,
          app_enabled_2: true,
          swatch_enabled: false,
          browser_select_box_enabled: false,
          override_grouped_product_option: false,

          draft_product_in_group_allowed: false,
          hidden_product_in_group_allowed: false,
          archived_product_in_group_allowed: false,

          resync_group_mode: 'disable',
          resync_group_interval: '1d',
          resync_group_time: '00:00',
          resync_group_times_per_day: '1',
          auto_update_stock_instantly: false,
          auto_update_automation_group: false,

          fp_ignore_keywords: ['/cart', '/account/login', '/account/logout', '/account', '/checkout'],

          use_basf_app: false,
          timeout_basf_app: 1500,

          hide_card_option_label: false,
          change_variant_on_card_behavior: 'change_content',

          observe_urls: [
            { timeout: 1000, page: 'product', url: '/recommendations/products' },
            { timeout: 1000, page: 'product', url: '/search' },
            { timeout: 1500, page: 'collection', url: '/search' },
          ],
        });
      }

      // if merchant config exist but not have storefront_access_token, add token
      if (!merchantConfig.storefront_access_token) {
        merchantConfig.storefront_access_token = storefrontAccessToken?.accessToken;
        console.log('STOREFRONT_ACCESS_TOKEN', storefrontAccessToken?.accessToken);
        await merchantConfig.save();
      }

      return merchantConfig;
    } catch (error) {
      console.error('Failed to get merchant config: ', error);
    }
  };

  upsert = (shop, data) => {
    return this.model.findOneAndUpdate({ shop }, data, { new: true, upsert: true, setDefaultsOnInsert: true });
  };

  syncToThemeApp = async (shop) => {
    try {
      const merchantConfig = await this.get(shop);
      const RestShopifyService = await initRestShopifyService(shop);
      await RestShopifyService.upsertMetafield({
        key: 'merchant_config',
        value: JSON.stringify(merchantConfig),
        type: 'json',
      });

      //
    } catch (error) {
      console.error('Failed to sync merchant config to theme app:', error);
    }
  };
}

export default new MerchantConfigService();

