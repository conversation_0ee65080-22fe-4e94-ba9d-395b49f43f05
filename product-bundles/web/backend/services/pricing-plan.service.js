import { ShopModel } from '../models/_index.js';

class PricingPlanService {
  constructor() {
    this.shopModel = ShopModel;
  }

  getAllPlans = () => {
    const plansMap = getAllPlans();
    const allPlans = Object.entries(plansMap).map(([_planId, planInfo]) => planInfo);
    return allPlans;
  };

  getPlanById = (id) => {
    const plansMap = getAllPlans();
    return plansMap?.[id];
  };

  getActivePlanByShop = async (shopId) => {
    const shop = await this.shopModel.findById(shopId);

    const allPlans = getAllPlans(shop);

    const activePlan = allPlans[shop?.app_data?.plan_name] || allPlans.free;

    return activePlan;
  };

  getAllPlansByShop = async (shopId) => {
    const shop = await this.shopModel.findById(shopId);

    const allPlans = getAllPlans(shop);

    const planNames = shop?.app_data?.plan_list || [];
    const shopPlans = {};

    planNames.forEach((name) => {
      const matchPlan = allPlans[name];
      if (matchPlan) shopPlans[name] = matchPlan;
    });

    return shopPlans;
  };

  getPlanByIdAndShop = async (id, shopId) => {
    const shop = await this.shopModel.findById(shopId);
    const plansMap = getAllPlans(shop);
    return plansMap?.[id];
  };

  getPlanByShopAndId = async (shopId, planId) => {
    const shop = await this.shopModel.findById(shopId);

    const allPlans = getAllPlans(shop);

    return allPlans?.[planId] || allPlans?.free;
  };
}

function roundByDigits(num, digits = 2) {
  return Math.round(num * 10 ** digits) / 10 ** digits;
}

function getAllPlans(shop) {
  const shopifyPlan = shop?.shopify_data?.plan_name || 'basic';

  // level 0 plans (basic, partner_test, ...) will have extra price = 0
  let extraPrice = 0;

  const level1Plans = ['professional'];
  const level2Plans = ['unlimited'];
  const level3Plans = ['shopify_plus'];

  const level1ExtraPrice = 5;
  const level2ExtraPrice = 15;
  const level3ExtraPrice = 30;

  if (level1Plans.includes(shopifyPlan)) extraPrice = level1ExtraPrice;
  if (level2Plans.includes(shopifyPlan)) extraPrice = level2ExtraPrice;
  if (level3Plans.includes(shopifyPlan)) extraPrice = level3ExtraPrice;

  const allPlans = {};

  // product limit for store in 2024
  let groupLimit24 = shop?.custom_data?.group_limit_24 || 5;
  let productLimit24 = shop?.custom_data?.product_limit_24 || 100;
  groupLimit24 = groupLimit24 > 5 ? groupLimit24 : 5;
  productLimit24 = productLimit24 > 100 ? productLimit24 : 100;

  const customLimitation = JSON.parse(shop?.app_data?.feature_limitation || '{}');
  delete customLimitation.max_group_count;
  delete customLimitation.max_product_count;

  // Free Plan for old customer
  allPlans.free = {
    plan_name: 'free',
    plan_display_name: 'Free (2024)',
    plan_amount: 0,
    plan_amount_base: 0,
    plan_amount_compare: 0,
    plan_level: 0,
    featured_text: '',
    additional_text: '',
    recurring_interval: 'monthly',
    benefits: [
      `${groupLimit24} product groups`,
      `Limit ${productLimit24} products for grouping`,
      'Unlimited products for swatch feature',
      'Single option product groups',
      'Multiple options product groups',
      'Manually sync product information',
      'Display variants in collection page',
      'Grouptify appearance editor',
      'Re-design shopify option (color swatch)',
      'Upload custom image for swatch',
      'Support multi-language',
    ],
    default_limitation: {
      max_sync_count: 0,
      max_group_count: groupLimit24,
      max_product_count: productLimit24,

      removed_trademark: false,
      unlocked_auto_group: false,
      unlocked_auto_group_by_tag: false,
      allowed_request_api_key: false,
      unlocked_import_export_groups: false,
      unlocked_import_export_swatches: false,
      unlocked_auto_resync: false,

      ...customLimitation,

      unlocked_swatch: true,
      allowed_upload_image: true,
      allowed_show_option_on_card: true,
      allowed_update_stock_instantly: false,
      allowed_update_automation_group: false,
    },
  };

  // Free Plan
  allPlans.free_feb25 = {
    plan_name: 'free_feb25',
    plan_display_name: 'Free',
    plan_amount: 0,
    plan_amount_base: 0,
    plan_amount_compare: 0,
    plan_level: 0,
    featured_text: '',
    additional_text: '',
    recurring_interval: 'monthly',
    benefits: [
      '5 product groups',
      'Limit 50 products for grouping',
      'Unlimited products for swatch feature',
      'Single option product groups',
      'Multiple options product groups',
      'Manually sync product information',
      'Grouptify appearance editor',
      'Re-design shopify option (color swatch)',
      'Support multi-language',
    ],
    default_limitation: {
      max_sync_count: 0,
      max_group_count: 5,
      max_product_count: 50,

      unlocked_swatch: true,
      removed_trademark: false,
      unlocked_auto_group: false,
      unlocked_auto_group_by_tag: false,
      allowed_upload_image: false,
      allowed_request_api_key: false,
      allowed_show_option_on_card: false,
      allowed_update_stock_instantly: false,
      allowed_update_automation_group: false,
      unlocked_import_export_groups: false,
      unlocked_import_export_swatches: false,
      unlocked_auto_resync: false,
    },
  };

  // Basic Plan
  allPlans.basic_feb25 = {
    plan_name: 'basic_feb25',
    plan_display_name: 'Basic',
    plan_amount: 9.99,
    plan_amount_base: 9.99,
    plan_amount_compare: 0,
    plan_level: 1,
    featured_text: '',
    additional_text: '',
    recurring_interval: 'monthly',
    benefits: [
      'All features in FREE plan',
      '100 product groups',
      'Unlimited products for grouping',
      'Group product automation by title',
      'Auto-sync product information (1 per day)',
      'Import & export product groups',
      'Upload custom image for swatch',
      'Import & export color swatches',
      'Display variants in collection page',
      'Remove trademark',
    ],
    default_limitation: {
      max_sync_count: 1,
      max_group_count: 100,
      max_product_count: 100000,

      unlocked_swatch: true,
      removed_trademark: true,
      unlocked_auto_group: true,
      unlocked_auto_group_by_tag: false,
      allowed_upload_image: true,
      allowed_request_api_key: false,
      allowed_show_option_on_card: true,
      allowed_update_stock_instantly: false,
      allowed_update_automation_group: false,
      unlocked_import_export_groups: true,
      unlocked_import_export_swatches: true,
      unlocked_auto_resync: true,
    },
  };

  // Standard Plan
  allPlans.standard_feb25 = {
    plan_name: 'standard_feb25',
    plan_display_name: 'Standard',
    plan_amount: 29,
    plan_amount_base: 29,
    plan_amount_compare: 0,
    plan_level: 2,
    featured_text: 'Most Popular',
    additional_text: '',
    recurring_interval: 'monthly',
    benefits: ['All features in BASIC plan', '500 product groups', 'Support style customization'],
    default_limitation: {
      max_sync_count: 1,
      max_group_count: 500,
      max_product_count: 100000,

      unlocked_swatch: true,
      removed_trademark: true,
      unlocked_auto_group: true,
      unlocked_auto_group_by_tag: false,
      allowed_upload_image: true,
      allowed_request_api_key: false,
      allowed_show_option_on_card: true,
      allowed_update_stock_instantly: false,
      allowed_update_automation_group: false,
      unlocked_import_export_groups: true,
      unlocked_import_export_swatches: true,
      unlocked_auto_resync: true,
    },
  };

  // Advanced Plan
  allPlans.advanced_feb25 = {
    plan_name: 'advanced_feb25',
    plan_display_name: 'Advanced',
    plan_amount: 49,
    plan_amount_base: 49,
    plan_amount_compare: 0,
    plan_level: 3,
    featured_text: '',
    additional_text: '',
    recurring_interval: 'monthly',
    benefits: [
      'All features in STANDARD plan',
      '5000 product groups',
      'Group product automation by tag',
      'Real-time inventory updates',
      'Auto-sync product information (4 per day)',
      'Manage product groups through API',
      'VIP support',
    ],
    default_limitation: {
      max_sync_count: 4,
      max_group_count: 5000,
      max_product_count: 100000,

      unlocked_swatch: true,
      removed_trademark: true,
      unlocked_auto_group: true,
      unlocked_auto_group_by_tag: true,
      allowed_upload_image: true,
      allowed_request_api_key: true,
      allowed_show_option_on_card: true,
      allowed_update_stock_instantly: true,
      allowed_update_automation_group: true,
      unlocked_import_export_groups: true,
      unlocked_import_export_swatches: true,
      unlocked_auto_resync: true,
    },
  };

  // Apr 25 -  Basic Plan
  allPlans.basic_apr25 = {
    plan_name: 'basic_apr25',
    plan_display_name: 'Basic',
    plan_amount: roundByDigits(9.99 + extraPrice),
    // plan_amount_base: 9.99,
    plan_amount_base: roundByDigits(9.99 + extraPrice),
    plan_amount_compare: 0,
    plan_level: 1,
    featured_text: '',
    // additional_text: '+ $5 Grow plan, + $15 Advanced plan, + $30 Shopify Plus',
    additional_text: '',
    recurring_interval: 'monthly',
    benefits: [
      'All features in FREE plan',
      '100 product groups',
      'Unlimited products for grouping',
      'Group product automation by title',
      'Auto-sync product information (1 per day)',
      'Import & export product groups',
      'Upload custom image for swatch',
      'Import & export color swatches',
      'Display variants in collection page',
      'Remove trademark',
    ],
    default_limitation: {
      max_sync_count: 1,
      max_group_count: 100,
      max_product_count: 100000,

      unlocked_swatch: true,
      removed_trademark: true,
      unlocked_auto_group: true,
      unlocked_auto_group_by_tag: false,
      allowed_upload_image: true,
      allowed_request_api_key: false,
      allowed_show_option_on_card: true,
      allowed_update_stock_instantly: false,
      allowed_update_automation_group: false,
      unlocked_import_export_groups: true,
      unlocked_import_export_swatches: true,
      unlocked_auto_resync: true,
    },
  };

  // Apr 25 - Standard Plan
  allPlans.standard_apr25 = {
    plan_name: 'standard_apr25',
    plan_display_name: 'Standard',
    plan_amount: roundByDigits(29 + extraPrice),
    // plan_amount_base: 29,
    plan_amount_base: roundByDigits(29 + extraPrice),
    plan_amount_compare: 0,
    plan_level: 2,
    featured_text: '',
    // additional_text: '+ $5 Grow plan, + $15 Advanced plan, + $30 Shopify Plus',
    additional_text: '',
    recurring_interval: 'monthly',
    benefits: ['All features in BASIC plan', '500 product groups', 'Support style customization'],
    default_limitation: {
      max_sync_count: 1,
      max_group_count: 500,
      max_product_count: 100000,

      unlocked_swatch: true,
      removed_trademark: true,
      unlocked_auto_group: true,
      unlocked_auto_group_by_tag: true,
      allowed_upload_image: true,
      allowed_request_api_key: false,
      allowed_show_option_on_card: true,
      allowed_update_stock_instantly: false,
      allowed_update_automation_group: false,
      unlocked_import_export_groups: true,
      unlocked_import_export_swatches: true,
      unlocked_auto_resync: true,
    },
  };

  // Apr 25 - Advanced Plan
  allPlans.advanced_apr25 = {
    plan_name: 'advanced_apr25',
    plan_display_name: 'Advanced',
    plan_amount: roundByDigits(49 + extraPrice),
    // plan_amount_base: 29.99,
    plan_amount_base: roundByDigits(49 + extraPrice),
    plan_amount_compare: 0,
    plan_level: 3,
    featured_text: 'Most Popular',
    // additional_text: '+ $5 Grow plan, + $15 Advanced plan, + $30 Shopify Plus',
    additional_text: '',
    recurring_interval: 'monthly',
    benefits: [
      'All features in STANDARD plan',
      '5000 product groups',
      'Group product automation by tag',
      'Real-time inventory updates',
      'Auto-sync product information (4 per day)',
      'Manage product groups through API',
      'VIP support',
    ],
    default_limitation: {
      max_sync_count: 4,
      max_group_count: 5000,
      max_product_count: 100000,

      unlocked_swatch: true,
      removed_trademark: true,
      unlocked_auto_group: true,
      unlocked_auto_group_by_tag: true,
      allowed_upload_image: true,
      allowed_request_api_key: true,
      allowed_show_option_on_card: true,
      allowed_update_stock_instantly: true,
      allowed_update_automation_group: true,
      unlocked_import_export_groups: true,
      unlocked_import_export_swatches: true,
      unlocked_auto_resync: true,
    },
  };

  return allPlans;
}

export default new PricingPlanService();

