import BaseService from './base/base.service.js';
import { config } from '../configs/_index.js';
import { ShopModel } from '../models/_index.js';
import { initRestShopifyService } from './shopify/rest-shopify.service.js';
import { initGraphqlShopifyService } from './shopify/graphql-shopify.service.js';

const { APP_URL } = config.env;
const { WEBHOOK_PATH } = config.shopify;

class ShopService extends BaseService {
  constructor() {
    super(ShopModel);
  }

  getByMyshopifyDomain = (myshopifyDomain) => {
    return this.model.findOne({ 'shopify_data.myshopify_domain': myshopifyDomain }).exec();
  };

  registerWebhooks = async (
    shopId,
    options = {
      skipUninstallAppWebhook: false,
      skipChangeThemeWebhook: false,
    }
  ) => {
    try {
      const GraphqlShopifyService = await initGraphqlShopifyService(shopId);

      const webhooks = {
        uninstallApp: {
          callbackUrl: `${APP_URL}/${WEBHOOK_PATH}/uninstall`.replace(/(?<!:)\/{2,}/g, '/'),
          topic: 'APP_UNINSTALLED',
          shouldRegister: true,
          skip: options.skipUninstallAppWebhook,
        },
        changeTheme: {
          callbackUrl: `${APP_URL}/${WEBHOOK_PATH}/change-theme`.replace(/(?<!:)\/{2,}/g, '/'),
          topic: 'THEMES_PUBLISH',
          shouldRegister: true,
          skip: options.skipChangeThemeWebhook,
        },
      };

      // Process webhooks that aren't skipped
      for (const [_key, webhook] of Object.entries(webhooks)) {
        // Skip this webhook if the skip option is true
        if (webhook.skip) {
          continue;
        }

        const existedWebhook = await GraphqlShopifyService.getWebhookByCallbackUrl(webhook.callbackUrl);

        if (webhook.shouldRegister) {
          // Create or update webhook
          const webhookData = { callbackUrl: webhook.callbackUrl, format: 'JSON' };

          if (existedWebhook?.id) {
            await GraphqlShopifyService.updateWebhook(existedWebhook.id, webhookData);
          } else {
            await GraphqlShopifyService.createWebhook(webhook.topic, webhookData);
          }
        } else if (existedWebhook?.id) {
          // Delete webhook if it exists but shouldn't be registered
          await GraphqlShopifyService.deleteWebhook(existedWebhook.id);
        }
      }

      return true;
      //
    } catch (error) {
      console.error('Failed to register app webhook', error);
      return false;
    }
  };

  syncToThemeApp = async (shopId) => {
    try {
      const shop = await this.getById(shopId);
      const RestShopifyService = await initRestShopifyService(shopId);
      await RestShopifyService.upsertMetafield({
        key: 'feature_limitation',
        value: shop?.app_data?.feature_limitation || '{}',
        type: 'json',
      });

      //
    } catch (error) {
      console.error('Failed to sync shop to theme app:', error);
    }
  };
}

export default new ShopService();

