export const graphqlQuery = {
  GET_SHOP_LITE: () => `
    query getShop {
      shop {
        name
      }
    }
  `,

  GET_PRODUCTS_COUNT: () => `
    query getProductsCount {
      productsCount {
        count
      }
    }
  `,

  GET_ALL_PRODUCT_OPTIONS: (limit = 250, afterCursor) => {
    const params = [`first: ${limit}`];
    if (afterCursor) params.push(`after: "${afterCursor}"`);
    const paramsStr = params.join(', ');

    return `
      query getAllProductOptions {
        products(${paramsStr}) {
          pageInfo {
            endCursor
            hasNextPage
          }  
          nodes {
            options(first: 5) {
              name
              optionValues {
                name
              }
            }
          }
        }
      }
    `;
  },

  GET_ALL_PRODUCTS: (limit = 250, afterCursor) => {
    const params = [`first: ${limit}`];
    if (afterCursor) params.push(`after: "${afterCursor}"`);
    const paramsStr = params.join(', ');

    return `
      query getAllProducts {
        products(${paramsStr}) {
          pageInfo {
            endCursor
            hasNextPage
          }
          nodes {
            id
            tags
            title
            handle
            status
            images(first: 3) {
              nodes {
                url(transform: {maxWidth: 480})
              }
            }
            hasOnlyDefaultVariant
            tracksInventory
            totalInventory
            totalVariants
            publishedAt
            options {
              name
              values
            }
            variants(first: 250) {
              nodes {
                price
                compareAtPrice
                availableForSale
              }
            }
          }
        }
      }
    `;
  },

  GET_ALL_PRODUCT_TRANSLATIONS: (locale = 'en', limit = 250, afterCursor) => {
    const params = [`first: ${limit}`];
    if (afterCursor) params.push(`after: "${afterCursor}"`);
    const paramsStr = params.join(', ');

    return `
      query getAllProductTranslations {
        products(${paramsStr}) {
          pageInfo {
            endCursor
            hasNextPage
          }
          nodes {
            title
            handle
            translations(locale: "${locale}") {
              key
              value
            }
          }
        }
      }
    `;
  },

  GET_ALL_SALE_CHANNELS: () => `
    query getAllSaleChannels {
      channels(first: 250) {
        nodes {
          id
          name
          handle
          overviewPath
        }
      }
    }
  `,

  GET_PRODUCTS_IN_SALE_CHANNEL: (id, limit = 250, afterCursor) => {
    const params = [`first: ${limit}`];
    if (afterCursor) params.push(`after: "${afterCursor}"`);
    const paramsStr = params.join(', ');

    return `
      query getProductsInSaleChannel {
        channel(id: "${id}") {
          id
          name
          handle
          overviewPath
          products(${paramsStr}) {
            pageInfo {
              endCursor
              hasNextPage
            }
            nodes {
              id
              title
              handle
              status
              images(first: 3) {
                nodes {
                  url(transform: {maxWidth: 480})
                }
              }
              hasOnlyDefaultVariant
              hasOutOfStockVariants
              totalVariants
              totalInventory
              tracksInventory
              options {
                name
                values
              }
              variants(first: 250) {
                nodes {
                  price
                  compareAtPrice
                  inventoryPolicy
                  availableForSale
                }
              }
            }
          }
        }
      }
    `;
  },

  GET_PRODUCTS_BY_IDS: (ids = []) => {
    const idsStr = `[${ids.map((id) => `"gid://shopify/Product/${id}"`).join(', ')}]`;

    return `
      query getProductsByIds {
        nodes(ids: ${idsStr}) {
          ... on Product {
            id
            title
            handle
            status
            images(first: 3) {
              nodes {
                url(transform: {maxWidth: 480})
              }
            }
            hasOnlyDefaultVariant
            tracksInventory
            totalInventory
            totalVariants
            publishedAt
            variants(first: 250) {
              nodes {
                price
                compareAtPrice
                availableForSale
              }
            }
          }
        }
      }
    `;
  },

  GET_PRODUCT_WITH_VARIANTS_BY_ID: (id, limit = 250) => {
    return `
      query getProductWithVariantsById {
        product(id: "${id}") {
          id
          title
          handle
          status
          images(first: 3) {
            nodes {
              url(transform: {maxWidth: 480})
            }
          }
          hasOnlyDefaultVariant
          hasOutOfStockVariants
          totalVariants
          totalInventory
          tracksInventory
          variants(first: ${limit}) {
            nodes {
              price
              compareAtPrice
              inventoryPolicy
              availableForSale
            }
          }
        }
      }
    `;
  },

  GET_SHOP_LOCALES: () => `
    query getShopLocales {
      shopLocales {
        name
        locale
        primary
        published
      }
    }
  `,

  GET_METAOBJECTS: (type, limit = 250, afterCursor) => {
    const params = [`type: "${type}"`, `first: ${limit}`];
    if (afterCursor) params.push(`after: "${afterCursor}"`);
    const paramsStr = params.join(', ');

    return `
      query getMetaobjects {
        metaobjects(${paramsStr}) {
          pageInfo {
            hasNextPage
            endCursor
          }
          nodes {
            fields {
              key
              value
              reference {
                ... on MediaImage {
                  image {
                    src
                  }
                }
              }
            }
          }
        }
      }
    `;
  },

  SET_METAFIELDS: () => `
    mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields {
          key
          namespace
          value
          createdAt
          updatedAt
        }
        userErrors {
          field
          message
          code
        }
      }
    }
  `,

  CREATE_STAGED_UPLOAD: () => `
    mutation stagedUploadsCreate($input: [StagedUploadInput!]!) {
      stagedUploadsCreate(input: $input) {
        stagedTargets {
          resourceUrl
          url
          parameters {
            name
            value
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `,

  CREATE_FILE: () => `
    mutation fileCreate($files: [FileCreateInput!]!) {
      fileCreate(files: $files) {
        files {
          id
        }
        userErrors {
          field
          message
        }
      }
    }
  `,

  GET_IMAGE_BY_ID: (id) => `
    query getFile {
      node(id: "${id}") {
        id
        ... on MediaImage {
          image {
            url(transform: {maxWidth: 480})
          }
        }
      }
    }
  `,

  CREATE_STOREFRONT_ACCESS_TOKEN: (title) => `
    mutation CreateStorefrontAccessToken {
      storefrontAccessTokenCreate(input: {title: "${title}"}) {
        storefrontAccessToken {
          title
          accessToken
        }
        userErrors {
          field
          message
        }
      }
    }
  `,

  CREATE_RECURRING_CHARGE: () => `
    mutation CreateRecurringCharge($test: Boolean!, $name: String!, $lineItems: [AppSubscriptionLineItemInput!]!, $returnUrl: URL!) {
      appSubscriptionCreate(test: $test, name: $name, returnUrl: $returnUrl, lineItems: $lineItems) {
        userErrors {
          field
          message
        }
        appSubscription {
          id
        }
        confirmationUrl
      }
    }
  `,

  CANCEL_CHARGE_BY_ID: (id) => {
    if (!id.includes('gid://shopify/AppSubscription/')) id = `gid://shopify/AppSubscription/${id}`;

    return `
      mutation CancelChargeById {
        appSubscriptionCancel(id: "${id}") {
          appSubscription {
            id
            name
            status
            returnUrl
            createdAt
            currentPeriodEnd
          }
          userErrors {
            field
            message
          }
        }
      }
    `;
  },

  GET_WEBHOOK_BY_CALLBACK_URL: (url) => `
    query getWebhookByCallbackUrl {
      webhookSubscriptions(
        first: 100
        callbackUrl: "${url}"
      ) {
        nodes {
          id
        }
      }
    }
  `,

  CREATE_WEBHOOK: () => `
    mutation webhookSubscriptionCreate($topic: WebhookSubscriptionTopic!, $webhookSubscription: WebhookSubscriptionInput!) {
      webhookSubscriptionCreate(topic: $topic, webhookSubscription: $webhookSubscription) {
        webhookSubscription {
          id
          topic
          filter
          format
          endpoint {
            __typename
            ... on WebhookHttpEndpoint {
              callbackUrl
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `,

  UPDATE_WEBHOOK: () => `
    mutation WebhookSubscriptionUpdate($id: ID!, $webhookSubscription: WebhookSubscriptionInput!) {
      webhookSubscriptionUpdate(id: $id, webhookSubscription: $webhookSubscription) {
        userErrors {
          field
          message
        }
        webhookSubscription {
          id
          topic
          filter
          format
          endpoint {
            __typename
            ... on WebhookHttpEndpoint {
              callbackUrl
            }
          }
        }
      }
    }
  `,

  DELETE_WEBHOOK: () => `
    mutation webhookSubscriptionDelete($id: ID!) {
      webhookSubscriptionDelete(id: $id) {
        userErrors {
          field
          message
        }
        deletedWebhookSubscriptionId
      }
    }
  `,
};

