import axios from 'axios';
import { graphqlQuery } from './graphql-query.const.js';
import { config } from '../../configs/_index.js';
import ShopService from '../shop.service.js';
import { sleepWithQueryCost } from '../../utils/_index.js';
import _ from 'lodash';

const { API_VERSION } = config.shopify;

class GraphqlShopifyService {
  // Initializes the GraphqlShopifyService with the Shopify domain and access token.
  constructor(myshopifyDomain, accessToken, disabled) {
    this.baseUrl = `https://${myshopifyDomain}/admin/api/${API_VERSION}/graphql.json`;
    this.accessToken = accessToken;
    this.disabled = disabled;
  }

  // Sends a GraphQL request to the Shopify API with the specified query and variables.
  request = async (query, variables = {}, headers = {}) => {
    const requestBody = { query, variables };

    const requestHeaders = {
      'appx-Type': 'application/json',
      'X-Shopify-Access-Token': this.accessToken,
      ...headers,
    };

    const maxRetries = 5;
    const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await axios.post(this.baseUrl, requestBody, { headers: requestHeaders });
        return response.data;
        //
      } catch (error) {
        console.error(`GRAPHQL API REQUEST FAILED - ATTEMPT ${attempt}`, error?.response?.data?.errors);
        console.error('URL: ', error?.config?.url);
        console.error('BODY: ', error?.config?.data);

        if (attempt === maxRetries) {
          console.error('All retry attempts failed.');
        }

        console.log(`Retrying in ${attempt * 500}ms...`);
        await delay(attempt * 500);
      }
    }
  };

  getShopLite = async () => {
    if (this.disabled) return {};

    try {
      const query = graphqlQuery.GET_SHOP_LITE();
      const response = await this.request(query);
      return response?.data?.shop;
      //
    } catch (error) {
      console.error('Failed to get shop (lite)', error);
    }
  };

  /**
   * PRODUCT SERVICE
   */
  getProductsCount = async () => {
    if (this.disabled) return 0;

    try {
      const query = graphqlQuery.GET_PRODUCTS_COUNT();
      const response = await this.request(query);
      return response?.data?.productsCount?.count;
      //
    } catch (error) {
      console.error('Failed to get products count', error);
    }
  };

  getAllProductOptions = async () => {
    if (this.disabled)
      return {
        optionSet: [],
        valueSet: [],
      };

    try {
      let endCursor = undefined;
      let hasNextPage = true;
      const optionCountMap = new Map();
      const valueCountMap = new Map();
      const optionSingleMap = new Map();

      while (true) {
        const response = await this.request(graphqlQuery.GET_ALL_PRODUCT_OPTIONS(250, endCursor));
        const data = response.data.products;
        const cost = response.extensions.cost;

        data.nodes.forEach((p) => {
          p.options.forEach((o) => {
            const optionName = o.name.trim();
            if (!optionCountMap.has(optionName)) optionCountMap.set(optionName, 1);
            else optionCountMap.set(optionName, optionCountMap.get(optionName) + 1);

            const isSingle = o.optionValues.length === 1;
            if (!optionSingleMap.has(optionName)) optionSingleMap.set(optionName, isSingle);
            else optionSingleMap.set(optionName, optionSingleMap.get(optionName) || isSingle);

            o.optionValues.forEach((v) => {
              const valueName = v.name.trim();
              const valueKey = optionName + 'matitmui' + valueName;
              if (!valueCountMap.has(valueKey)) valueCountMap.set(valueKey, 1);
              else valueCountMap.set(valueKey, valueCountMap.get(valueKey) + 1);
            });
          });
        });

        endCursor = data.pageInfo ? data.pageInfo.endCursor : undefined;
        hasNextPage = data.pageInfo ? data.pageInfo.hasNextPage : false;

        if (!hasNextPage) break;

        await sleepWithQueryCost(cost);
      }

      return {
        optionSet: Array.from(optionCountMap).map(([name, count]) => ({
          name,
          count,
          single: optionSingleMap.get(name),
        })),
        valueSet: Array.from(valueCountMap).map(([key, count]) => ({
          type: key.split('matitmui')?.[0],
          name: key.split('matitmui')?.[1],
          count,
        })),
      };
    } catch (error) {
      console.error('Failed to get all product options: ', error);
    }
  };

  getProductsByIds = async (ids = []) => {
    if (this.disabled) return [];

    try {
      const products = [];
      const chunkedIds = _.chunk(ids, 250); // no more than 250 items per call

      for (const ids of chunkedIds) {
        const response = await this.request(graphqlQuery.GET_PRODUCTS_BY_IDS(ids));
        products.push(...(response?.data?.nodes || []));
      }

      return products.filter((p) => p);
      //
    } catch (error) {
      console.error('Failed to get products by ids: ', error);
    }
  };

  getAllProducts = async () => {
    if (this.disabled) return [];

    try {
      let endCursor = undefined;
      let hasNextPage = true;
      const allProducts = [];

      while (true) {
        const response = await this.request(graphqlQuery.GET_ALL_PRODUCTS(250, endCursor));
        const data = response.data.products;
        const cost = response.extensions.cost;

        allProducts.push(...data.nodes);
        endCursor = data.pageInfo ? data.pageInfo.endCursor : undefined;
        hasNextPage = data.pageInfo ? data.pageInfo.hasNextPage : false;

        if (!hasNextPage) break;

        await sleepWithQueryCost(cost);
      }

      return allProducts;
      //
    } catch (error) {
      console.error('Failed to get all products: ', error);
    }
  };

  getAllProductTranslations = async (locale) => {
    if (this.disabled) return [];

    try {
      let endCursor = undefined;
      let hasNextPage = true;
      const allTrans = [];

      while (true) {
        const response = await this.request(graphqlQuery.GET_ALL_PRODUCT_TRANSLATIONS(locale, 250, endCursor));
        const data = response.data.products;
        const cost = response.extensions.cost;

        allTrans.push(...data.nodes);
        endCursor = data.pageInfo ? data.pageInfo.endCursor : undefined;
        hasNextPage = data.pageInfo ? data.pageInfo.hasNextPage : false;

        if (!hasNextPage) break;

        await sleepWithQueryCost(cost);
      }

      return allTrans;
      //
    } catch (error) {
      console.error('Failed to get all product translations: ', error);
    }
  };

  /**
   * SALE CHANNEL SERVICE
   */
  getAllSaleChannels = async () => {
    if (this.disabled) return [];

    try {
      const query = graphqlQuery.GET_ALL_SALE_CHANNELS();
      const response = await this.request(query);
      return response?.data?.channels?.nodes;
      //
    } catch (error) {
      console.error('Failed to get all sale channels', error);
    }
  };

  getProductsInSaleChannel = async (channelId) => {
    if (this.disabled) return [];

    try {
      let endCursor = undefined;
      let hasNextPage = true;
      const products = [];

      while (true) {
        const response = await this.request(graphqlQuery.GET_PRODUCTS_IN_SALE_CHANNEL(channelId, 250, endCursor));

        const data = response.data.channel.products;
        const cost = response.extensions.cost;

        products.push(...data.nodes);
        endCursor = data.pageInfo ? data.pageInfo.endCursor : undefined;
        hasNextPage = data.pageInfo ? data.pageInfo.hasNextPage : false;

        if (!hasNextPage) break;

        await sleepWithQueryCost(cost);
      }

      return products;
      //
    } catch (error) {
      console.error('Failed to get products in channel:', error);
    }
  };

  getProductAndVariantsById = async (productId) => {
    if (this.disabled) return {};

    try {
      const query = graphqlQuery.GET_PRODUCT_WITH_VARIANTS_BY_ID(productId);
      const response = await this.request(query);
      return response?.data?.product;
      //
    } catch (error) {
      console.error('Failed to get product and variants by id', error);
    }
  };

  /**
   * METAOBJECT SERVICE
   */
  getMetaobjects = async (type) => {
    if (this.disabled) return [];

    try {
      let endCursor = undefined;
      let hasNextPage = true;
      const metaobjects = [];

      while (true) {
        const response = await this.request(graphqlQuery.GET_METAOBJECTS(type, 250, endCursor));
        const data = response.data.metaobjects;
        const cost = response.extensions.cost;

        metaobjects.push(...data.nodes);
        endCursor = data.pageInfo ? data.pageInfo.endCursor : undefined;
        hasNextPage = data.pageInfo ? data.pageInfo.hasNextPage : false;

        if (!hasNextPage) break;

        await sleepWithQueryCost(cost);
      }

      return metaobjects;
      //
    } catch (error) {
      console.error('Failed to get metaobjects: ', error);
    }
  };

  setMetafields = async (metafields) => {
    if (this.disabled) return {};

    try {
      const query = graphqlQuery.SET_METAFIELDS();
      const variables = { metafields };
      const response = await this.request(query, variables);
      return response?.data?.metafieldsSet;
      //
    } catch (error) {
      console.error('Failed to set metafields', error);
    }
  };

  /**
   * SHOP SERVICE
   */
  getShopLocales = async () => {
    if (this.disabled) return [];

    try {
      const query = graphqlQuery.GET_SHOP_LOCALES();
      const response = await this.request(query);
      return response?.data?.shopLocales;
      //
    } catch (error) {
      console.error('Failed to get shop locales', error);
    }
  };

  /**
   * FILE SERVICE
   */
  createStagedUpload = async (fileData) => {
    if (this.disabled) return {};

    try {
      const query = graphqlQuery.CREATE_STAGED_UPLOAD();
      const variables = { input: [fileData] };
      const response = await this.request(query, variables);
      return response?.data?.stagedUploadsCreate;
      //
    } catch (error) {
      console.error('Failed to create staged upload', error);
    }
  };

  createFile = async (fileData) => {
    if (this.disabled) return {};

    try {
      const query = graphqlQuery.CREATE_FILE();
      const variables = { files: fileData };
      const response = await this.request(query, variables);
      return response?.data?.fileCreate;
      //
    } catch (error) {
      console.error('Failed to create file', error);
    }
  };

  getImageById = async (id) => {
    if (this.disabled) return {};

    try {
      const query = graphqlQuery.GET_IMAGE_BY_ID(id);
      const response = await this.request(query);
      return response?.data?.node?.image?.url;
      //
    } catch (error) {
      console.error('Failed to get image file', error);
    }
  };

  createStorefrontAccessToken = async (title = 'grouptify') => {
    if (this.disabled) return {};

    try {
      const query = graphqlQuery.CREATE_STOREFRONT_ACCESS_TOKEN(title);
      const response = await this.request(query);

      return response?.data?.storefrontAccessTokenCreate?.storefrontAccessToken;
      //
    } catch (error) {
      console.error('Failed to create file', error);
    }
  };

  /**
   * CHARGE SERVICE
   */
  createRecurringCharge = async (chargeData = {}) => {
    if (this.disabled) return {};

    try {
      const query = graphqlQuery.CREATE_RECURRING_CHARGE();
      const variables = chargeData;
      const response = await this.request(query, variables);
      return response?.data?.appSubscriptionCreate;
      //
    } catch (error) {
      console.error('Failed to create recurring charge', error);
    }
  };

  cancelChargeById = async (id) => {
    if (this.disabled) return {};

    try {
      const query = graphqlQuery.CANCEL_CHARGE_BY_ID(id);
      const response = await this.request(query);
      return response?.data?.appSubscriptionCancel;
      //
    } catch (error) {
      console.error('Failed to cancel charge', error);
    }
  };

  /**
   * WEBHOOK SERVICE
   */
  getWebhookByCallbackUrl = async (url) => {
    if (this.disabled) return {};

    try {
      const query = graphqlQuery.GET_WEBHOOK_BY_CALLBACK_URL(url);
      const response = await this.request(query);
      return response?.data?.webhookSubscriptions?.nodes?.[0];
      //
    } catch (error) {
      this.csErr('Failed to get webhook by callback url', error);
    }
  };

  createWebhook = async (topic, webhookSubscription) => {
    if (this.disabled) return {};

    try {
      const query = graphqlQuery.CREATE_WEBHOOK();
      const variables = { topic, webhookSubscription };
      const response = await this.request(query, variables);

      console.log('CREATE WEBHOOK', JSON.stringify(response?.data, null, 2));

      return response?.data?.webhookSubscriptionCreate?.webhookSubscription;
      //
    } catch (error) {
      this.csErr(`Failed to create webhook ${topic}`, error);
    }
  };

  updateWebhook = async (id, webhookSubscription) => {
    if (this.disabled) return {};

    try {
      const query = graphqlQuery.UPDATE_WEBHOOK();
      const variables = { id, webhookSubscription };
      const response = await this.request(query, variables);

      console.log('UPDATE WEBHOOK', JSON.stringify(response?.data, null, 2));

      return response?.data?.webhookSubscriptionUpdate?.webhookSubscription;
      //
    } catch (error) {
      this.csErr(`Failed to update webhook ${id}`, error);
    }
  };

  deleteWebhook = async (id) => {
    if (this.disabled) return '';

    try {
      const query = graphqlQuery.DELETE_WEBHOOK();
      const variables = { id };
      const response = await this.request(query, variables);

      console.log('DELETE WEBHOOK', JSON.stringify(response?.data, null, 2));

      return response?.data?.webhookSubscriptionDelete?.deletedWebhookSubscriptionId;
      //
    } catch (error) {
      this.csErr(`Failed to delete webhook ${id}`, error);
    }
  };
}

export const initGraphqlShopifyService = async (shopId) => {
  try {
    const shop = await ShopService.getById(shopId);
    const accessToken = shop.access_token;
    const myshopifyDomain = shop.shopify_data.myshopify_domain;
    const disabled = shop.shop_status === 2;
    return new GraphqlShopifyService(myshopifyDomain, accessToken, disabled);
    //
  } catch (error) {
    console.error('Failed to init GraphqlShopifyService: ', error);
  }
};

export default GraphqlShopifyService;

