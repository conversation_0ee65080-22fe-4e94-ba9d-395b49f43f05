import axios from 'axios';
import { config } from '../../configs/_index.js';
import ShopService from '../shop.service.js';

const { API_VERSION } = config.shopify;

class RestShopifyService {
  // Initializes RestShopifyService with the Shopify domain and access token.
  constructor(myshopifyDomain, accessToken) {
    this.baseUrl = `https://${myshopifyDomain}/admin/api/${API_VERSION}`;
    this.accessToken = accessToken;
  }

  // Makes an HTTP request to the Shopify API with the specified endpoint, method, and data.
  request = async (endpoint, method = 'GET', data = null, headers = {}) => {
    const url = `${this.baseUrl}${endpoint}`;
    const reqHeaders = {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': this.accessToken,
      ...headers,
    };
    const options = { url, method, headers: reqHeaders };
    if (method !== 'GET' && data !== null) options.data = data;

    try {
      const response = await axios(options);
      return response?.data;
      //
    } catch (error) {
      if (error?.response?.data?.errors?.address?.[0] !== 'for this topic has already been taken') {
        console.error('REST API REQUEST FAILED', error?.response?.data?.errors);
        console.error('URL: ', error?.config?.url);
        console.error('BODY: ', error?.config?.data);
      }
    }
  };

  /**
   * SHOP SERVICE
   */
  getShop = async () => {
    const response = await this.request('/shop.json', 'GET');
    return response?.shop;
  };

  /**
   * PRODUCT SERVICE
   */
  getProduct = async (productId, fields = 'id,title') => {
    const response = await this.request(`/products/${productId}.json?fields=${fields}`, 'GET');
    return response?.product;
  };

  getProducts = async (query = {}) => {
    const queryStr = new URLSearchParams(query).toString();

    const response = await this.request(`/products.json?${queryStr}`, 'GET');
    return response?.products;
  };

  /**
   * METAFIELD SERVICE
   */
  upsertMetafield = async (data = {}, resources = '', resourceId = '') => {
    // if resource is not provided, it means this is shop metafield
    let endpoint = '/metafields.json';
    if (resources && resourceId) {
      endpoint = `/${resources}/${resourceId}/metafields.json`;
    }

    const metafieldData = {
      metafield: {
        namespace: 'mtm_product_group',
        key: data.key,
        type: data.type || 'multi_line_text_field',
        value: data.value || '{}',
      },
    };

    const response = await this.request(endpoint, 'POST', metafieldData);
    return response?.metafield;
  };

  /**
   * WEBHOOK SERVICE
   */
  getRegisteredWebhooks = async () => {
    const response = await this.request('/webhooks.json', 'GET');
    return response?.webhooks || [];
  };

  registerWebhook = async (webhook) => {
    await this.request('/webhooks.json', 'POST', { webhook });
    console.log('WEBHOOK REGISTERED', webhook);
  };

  registerWebhooks = async (webhooks) => {
    // const registeredWebhooks = await this.getRegisteredWebhooks();

    // const unregisteredWebhooks = webhooks.filter(
    //   (wh) => !registeredWebhooks.some((rwh) => rwh.topic === wh.topic && rwh.address === wh.address)
    // );

    // for (const webhook of unregisteredWebhooks) {
    for (const webhook of webhooks) {
      await this.registerWebhook(webhook);
    }

    console.log('All webhooks registered');
  };

  /**
   * THEME SERVICE
   */
  getThemes = async () => {
    const response = await this.request('/themes.json', 'GET');
    return response?.themes || [];
  };

  /**
   * ASSET SERVICE
   */
  getAsset = async (themeId, assetKey) => {
    const response = await this.request(`/themes/${themeId}/assets.json?asset[key]=${assetKey}`, 'GET');
    return response?.asset;
  };

  /**
   * BILLING SERVICE
   */
  getRecurringApplicationChargeById = async (chargeId) => {
    const response = await this.request(`/recurring_application_charges/${chargeId}.json`, 'GET');
    return response?.recurring_application_charge;
  };
}

export const initRestShopifyService = async (shopId) => {
  try {
    const shop = await ShopService.getById(shopId);
    const accessToken = shop.access_token;
    const myshopifyDomain = shop.shopify_data.myshopify_domain;
    return new RestShopifyService(myshopifyDomain, accessToken);
    //
  } catch (error) {
    console.error('Failed to init RestShopifyService: ', error);
  }
};

export default RestShopifyService;

