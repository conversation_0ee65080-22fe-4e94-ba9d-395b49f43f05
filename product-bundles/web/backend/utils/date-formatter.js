export const DateFormatter = {
  toYYYYMMDD: (date = new Date()) => {
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');

    return `${yyyy}-${mm}-${dd}`;
  },

  toHHMMSSDDMMYYYY: (date = new Date(), timezone = 'Asia/Ho_Chi_Minh') => {
    date = new Date(date);

    const dateInTimezone = new Date(date.toLocaleString('en-US', { timeZone: timezone }));

    const hh = String(dateInTimezone.getHours()).padStart(2, '0');
    const mm = String(dateInTimezone.getMinutes()).padStart(2, '0');
    const ss = String(dateInTimezone.getSeconds()).padStart(2, '0');

    const DD = String(dateInTimezone.getDate()).padStart(2, '0');
    const MM = String(dateInTimezone.getMonth() + 1).padStart(2, '0');
    const YYYY = dateInTimezone.getFullYear();

    return `${hh}:${mm}:${ss} ${DD}/${MM}/${YYYY}`;
  },
};

