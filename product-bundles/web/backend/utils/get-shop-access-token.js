import axios from 'axios';
import { config } from '../configs/_index.js';

const { CLIENT_ID, CLIENT_SECRET } = config.env;

export const getShopAccessToken = async (myshopifyDomain, code) => {
  const headers = { 'Accept-Encoding': 'gzip,deflate,compress' };
  const endpoint = `https://${myshopifyDomain}/admin/oauth/access_token?client_id=${CLIENT_ID}&client_secret=${CLIENT_SECRET}&code=${code}`;

  return axios
    .post(endpoint, {}, { headers })
    .then((response) => response.data)
    .catch((error) => {
      console.error('Get shop access token failed: ', error);
    });
};

