import { randomId } from './random.js';

export const productToOptionValue = (product, swatchConfigs = []) => {
  const matchConfig = swatchConfigs.find((config) => config.value === product.valueName);

  return {
    id: randomId(),
    name: product.valueName,
    style:
      matchConfig?.style === 'variant_image' ? 'first_image' : matchConfig?.style ? matchConfig?.style : 'first_image',
    color1: matchConfig?.color1 || product.colors[1],
    color2: matchConfig?.color2 || product.colors[2],
    image: product.images[0] || 'https://placehold.co/100x100/EEE/31343C?font=oswald&text=NO%20IMAGE',
    image_custom: matchConfig?.image || '',
  };
};

export const spfToDbProduct = (spfProduct, options = []) => ({
  id: spfProduct.id.split('gid://shopify/Product/')?.[1],
  title: spfProduct.title,
  handle: spfProduct.handle,
  status: spfProduct.status,
  images: spfProduct.images.nodes.map((i) => i.url),
  options: options,
  regular_price: spfProduct.variants.nodes[0].compareAtPrice,
  sale_price: spfProduct.variants.nodes[0].price,
  available: spfProduct.totalInventory > 0 || spfProduct.variants.nodes.some((variant) => variant.availableForSale),
});

