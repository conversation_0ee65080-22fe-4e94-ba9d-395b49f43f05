// get product name and variant from product title by pattern
export const extractInfoFromProductTitle = (title = '', pattern = '<product_name> - <variant>') => {
  const regex = /(<product_name>|<variant>)/g;
  const tokens = pattern.split(regex).filter((token) => token !== '');

  let info = { productName: '', valueName: '', temp: '' };
  let fieldToSet = 'temp';
  let processTitle = title + '@mtm@';
  tokens.push('@mtm@');

  tokens.forEach((token) => {
    if (token === '<product_name>') {
      fieldToSet = 'productName';
    }
    //
    else if (token === '<variant>') {
      fieldToSet = 'valueName';
    }
    //
    else {
      const tokenPos = processTitle.indexOf(token);
      if (tokenPos !== -1) {
        const fieldContent = processTitle.substr(0, tokenPos).trim();
        const lenToCut = tokenPos + token.length;
        processTitle = processTitle.slice(lenToCut);
        info[fieldToSet] = fieldContent;
        fieldToSet = 'temp';
      }
    }

    if (info.productName && info.valueName) return {};
  });

  return info;
};

// get group id, group option, group option value from product tag
export const extractInfoFromProductTag = (tag = '', _pattern = 'Grouptify_[GroupID]_[OptionName]_[ValueName]') => {
  const regex = /Grouptify_\[([^\]]*)\]_\[([^\]]*)\]_\[([^\]]*)\]/;
  const match = tag.match(regex);
  return {
    groupID: match ? match[1] : '',
    optionName: match ? match[2] : '',
    valueName: match ? match[3] : '',
  };
};

