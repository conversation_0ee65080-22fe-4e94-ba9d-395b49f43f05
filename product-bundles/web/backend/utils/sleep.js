export const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const sleepWithQueryCost = async (queryCost) => {
  if (!queryCost) return;

  const maxQueryCost = queryCost.requestQueryCost;
  const pointLeft = queryCost.throttleStatus?.currentAvailable;
  const restorePerSec = queryCost.throttleStatus?.restoreRate || 1;

  if (maxQueryCost > pointLeft) {
    console.log('SLEEP');
    const pointToRestore = maxQueryCost - pointLeft;
    const sleepTimeMs = Math.ceil((pointToRestore / restorePerSec) * 1000);
    await sleep(sleepTimeMs);
  }
};

