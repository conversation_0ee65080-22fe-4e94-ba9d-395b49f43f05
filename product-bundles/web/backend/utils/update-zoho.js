import axios from 'axios';
import moment from 'moment';
import { config } from '../configs/_index.js';

export const updateZoho = async ({ shopId, accountData, contactData, contactData2, token }) => {
  console.log('ZOHO UPDATE DATA: ', accountData, contactData, contactData2);

  if (config.env.IS_TESTING) return;

  const zohoToken = token || (await getZohoToken());
  const zohoUpdateTime = moment(Date.now()).utcOffset(7).format();

  if (zohoToken) {
    const updateAccountUrl = 'https://www.zohoapis.com/crm/v4/Accounts/upsert';
    const updateContactUrl = 'https://www.zohoapis.com/crm/v4/Contacts/upsert';

    const config = { headers: { Authorization: `Zoho-oauthtoken ${zohoToken}`, 'Content-Type': 'application/json' } };

    try {
      // update account data
      if (accountData) {
        const updateAccountResponse = await axios.post(
          updateAccountUrl,
          JSON.stringify({
            data: [{ ...accountData, Grouptify_Updated_Time: zohoUpdateTime, Shopify_Storeid: `${shopId}` }],
            duplicate_check_fields: ['Shopify_Storeid'],
            trigger: ['workflow'],
          }),
          config
        );
        console.log('UPDATE ACCOUNT ZOHO SUCCESS: ', JSON.stringify(updateAccountResponse.data, null, 2));
      }

      // update contact data
      if (contactData) {
        const updateContactResponse = await axios.post(
          updateContactUrl,
          JSON.stringify({
            data: [{ ...contactData, Grouptify_Updated_Time: zohoUpdateTime }],
            duplicate_check_fields: ['Email', 'Shopify_Store_ID', 'Grouptify_Unique_ID'],
          }),
          config
        );
        console.log('UPDATE CONTACT ZOHO SUCCESS: ', JSON.stringify(updateContactResponse.data, null, 2));
      }

      // update contact data 2
      if (contactData2) {
        const updateContactResponse = await axios.post(
          updateContactUrl,
          JSON.stringify({
            data: [{ ...contactData2, Grouptify_Updated_Time: zohoUpdateTime }],
            duplicate_check_fields: ['Email', 'Shopify_Store_ID', 'Grouptify_Unique_ID'],
          }),
          config
        );
        console.log('UPDATE CONTACT2 ZOHO SUCCESS: ', JSON.stringify(updateContactResponse.data, null, 2));
      }
      //
    } catch (error) {
      if (!error?.response?.data?.data) console.error('Failed to update zoho (server):', error);
      else console.error('Failed to update zoho (API):', error.response.data.data);
    }
  }
};

let zohoTokenInterval;

export const getZohoToken = async () => {
  console.info('ZOHO TOKEN GETTING');

  try {
    if (global.foundZohoToken) {
      console.log('FOUND ZOHO TOKEN ', global.zoho_token);
      return global.zoho_token;
    }

    const endpoint = 'https://tapita.io/pb/zhtoken/';
    const config = { headers: { Authorization: 'HDWbxq1lRdzUruqUE1HKnSzVZOr6KCZu' } };

    const response = await axios.post(endpoint, {}, config);

    const generatedToken = response?.data?.token || '';

    if (generatedToken) {
      global.zoho_token = generatedToken;

      if (!zohoTokenInterval) {
        zohoTokenInterval = setInterval(() => {
          console.log('AUTO REVOKE ZOHO TOKEN EVERY 9 MINS');
          global.zoho_token = '';
          global.foundZohoToken = false;
        }, 540000); // 9 mins
      }
    }

    console.log('ZOHO TOKEN GENERATED:', generatedToken);

    return generatedToken;
    //
  } catch (error) {
    console.log('Failed to get zoho token', error?.response?.data || error);
  }
};

