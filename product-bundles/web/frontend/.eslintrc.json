{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "react-hooks"], "rules": {"react/prop-types": "off", "react/react-in-jsx-scope": "off", "no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}]}}