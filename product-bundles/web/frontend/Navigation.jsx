import { NavMenu } from '@shopify/app-bridge-react';
import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { NAV_ITEMS } from '@/constants/navigation';
import { nav } from '@/utils/mixed';

export default function Navigation() {
  const navigate = useNavigate();

  const navItems = useMemo(() => {
    return NAV_ITEMS;
  }, []);

  const navMarkup = useMemo(
    () =>
      navItems.map(({ label, path }) => (
        <a
          key={path}
          href={nav(path)}
          onClick={(e) => {
            e.preventDefault();
            navigate(nav(path));
          }}
        >
          {label}
        </a>
      )),
    [navItems, navigate]
  );

  return (
    <NavMenu>
      <a
        href="/"
        rel="home"
        onClick={(e) => {
          e.preventDefault();
          navigate('/');
        }}
      />
      {navMarkup}
    </NavMenu>
  );
}

