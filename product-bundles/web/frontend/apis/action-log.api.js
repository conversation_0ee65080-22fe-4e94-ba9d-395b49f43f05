import axiosInstance, { authConfigs } from './axios.instance';

class ActionLogApi {
  constructor() {
    this.instance = axiosInstance;
    this.resources = 'action-logs';
  }

  get = (query = {}, config = {}) => {
    const queryStr = new URLSearchParams(query).toString();
    return this.instance.get(`/${this.resources}?${queryStr}`, authConfigs(config));
  };

  search = (query = {}, config = {}) => {
    const queryStr = new URLSearchParams(query).toString();
    return this.instance.get(`/${this.resources}/search?${queryStr}`, authConfigs(config, true));
  };
}

export default new ActionLogApi();

