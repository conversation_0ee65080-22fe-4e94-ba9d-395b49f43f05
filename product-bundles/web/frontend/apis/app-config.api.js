import { isAdmin } from '../utils/auth';
import axiosInstance, { authConfigs } from './axios.instance';

class AppConfigApi {
  constructor() {
    this.instance = axiosInstance;
  }

  get = (config = {}) => {
    return this.instance.get('/app-configs', authConfigs(config, isAdmin()));
  };

  update = (data, config = {}) => {
    return this.instance.put('/app-configs', data, authConfigs(config, true));
  };
}

export default new AppConfigApi();
