import axiosInstance, { authConfigs } from './axios.instance';

class AppDataApi {
  constructor() {
    this.instance = axiosInstance;
  }

  getShop = (config = {}) => {
    return this.instance.get('/app-data/shop', authConfigs(config));
  };

  getLocales = (config = {}) => {
    return this.instance.get('/app-data/locales', authConfigs(config));
  };

  getProductOptions = (config = {}) => {
    return this.instance.get('/app-data/product-options', authConfigs(config));
  };

  getSwatchSuggestions = (config = {}) => {
    return this.instance.get('/app-data/swatch-suggestions', authConfigs(config));
  };

  getWarning = (config = {}) => {
    return this.instance.get('/app-data/warning', authConfigs(config));
  };
}

export default new AppDataApi();

