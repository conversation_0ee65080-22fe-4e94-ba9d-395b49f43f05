// api/axiosInstance.js
import axios from 'axios';
import config from '../configs/config';
import { getAdminToken, getShopToken } from '../utils/auth';

const axiosInstance = axios.create({
  baseURL: config.env.API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.data) {
      console.error('Axios error: ', error.response.data);
      return Promise.reject(error.response.data);
    }
    console.error('Axios error: ', error);
    return Promise.reject(error);
  }
);

export const authConfigs = (config, isAdmin = false) => ({
  ...config,
  headers: { ...config.headers, 'auth-token': isAdmin ? getAdminToken() : getShopToken() },
});

export default axiosInstance;
