import axiosInstance, { authConfigs } from './axios.instance';

class BaseApi {
  constructor(resources, isAdmin = false) {
    this.instance = axiosInstance;
    this.resources = resources;
    this.isAdmin = isAdmin;
  }

  get = (query = {}, config = {}) => {
    let queryStr = new URLSearchParams(query).toString();
    if (queryStr) queryStr = '?' + queryStr;
    return this.instance.get(`/${this.resources}${queryStr}`, authConfigs(config, this.isAdmin));
  };

  getById = (id, config = {}) => {
    return this.instance.get(`/${this.resources}/${id}`, authConfigs(config, this.isAdmin));
  };

  create = (data, config = {}) => {
    return this.instance.post(`/${this.resources}`, data, authConfigs(config, this.isAdmin));
  };

  updateById = (id, data, config = {}) => {
    return this.instance.put(`/${this.resources}/${id}`, data, authConfigs(config, this.isAdmin));
  };

  deleteById = (id, config = {}) => {
    return this.instance.delete(`/${this.resources}/${id}`, authConfigs(config, this.isAdmin));
  };

  duplicateById = (id, config = {}) => {
    return this.instance.post(`/${this.resources}/${id}/duplicate`, {}, authConfigs(config, this.isAdmin));
  };
}

export default BaseApi;
