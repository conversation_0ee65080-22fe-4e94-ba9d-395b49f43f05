import axiosInstance, { authConfigs } from './axios.instance';

class MerchantConfigApi {
  constructor() {
    this.instance = axiosInstance;
  }

  get = (config = {}) => {
    return this.instance.get(`/merchant-configs`, authConfigs(config));
  };

  update = (data, config = {}) => {
    return this.instance.put(`/merchant-configs`, data, authConfigs(config));
  };
}

export default new MerchantConfigApi();
