import axiosInstance, { authConfigs } from './axios.instance';

class PricingPlanApi {
  constructor() {
    this.instance = axiosInstance;
  }

  getAllPlans = (config = {}) => {
    return this.instance.get('/pricing-plans', authConfigs(config, true));
  };

  getShopPlans = (config = {}) => {
    return this.instance.get('/pricing-plans/shop', authConfigs(config));
  };

  getShopActivePlan = (config = {}) => {
    return this.instance.get('/pricing-plans/shop/active', authConfigs(config));
  };

  startTrial = (data = {}, config = {}) => {
    return this.instance.post('/pricing-plans/shop/trial', data, authConfigs(config));
  };

  createChargeByPlanId = (planId, data = {}, config = {}) => {
    return this.instance.post(`/pricing-plans/${planId}/charges`, data, authConfigs(config));
  };
}

export default new PricingPlanApi();

