import { authConfigs } from './axios.instance';
import BaseApi from './base.api';

class ReleaseNoteApi extends BaseApi {
  constructor() {
    super('release-notes', true);
  }

  getByShop = (query = {}, config = {}) => {
    let queryStr = new URLSearchParams(query).toString();
    if (queryStr) queryStr = '?' + queryStr;
    return this.instance.get(`/${this.resources}${queryStr}`, authConfigs(config, false));
  };
}

export default new ReleaseNoteApi();

