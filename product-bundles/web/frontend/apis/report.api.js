import axiosInstance, { authConfigs } from './axios.instance';

class ReportApi {
  constructor() {
    this.instance = axiosInstance;
    this.resources = 'reports';
  }

  getAppStatistics = (query = {}, config = {}) => {
    const queryStr = new URLSearchParams(query).toString();
    return this.instance.get(`/${this.resources}/app-statistics?${queryStr}`, authConfigs(config, true));
  };

  getMerchantActions = (query = {}, config = {}) => {
    const queryStr = new URLSearchParams(query).toString();
    return this.instance.get(`/${this.resources}/merchant-actions?${queryStr}`, authConfigs(config, true));
  };
}

export default new ReportApi();
