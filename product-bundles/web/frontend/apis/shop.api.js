import axiosInstance, { authConfigs } from './axios.instance';

class ShopApi {
  constructor() {
    this.instance = axiosInstance;
  }

  regenerateApiKey = (data, config = {}) => {
    return this.instance.post(`/shops/regenerate-api-key`, data, authConfigs(config));
  };

  saveSurveyResult = (data, config = {}) => {
    return this.instance.post(`/shops/save-survey-result`, data, authConfigs(config));
  };

  sync = (data, config = {}) => {
    return this.instance.post(`/shops/sync`, data, authConfigs(config, true));
  };

  search = (query, config = {}) => {
    return this.instance.get(`/shops/search?${new URLSearchParams(query).toString()}`, authConfigs(config, true));
  };

  getById = (id, config = {}) => {
    return this.instance.get(`/shops/${id}`, authConfigs(config, true));
  };

  getCompetitors = (id, config = {}) => {
    return this.instance.get(`/shops/${id}/competitors`, authConfigs(config, true));
  };

  updateById = (id, data, config = {}) => {
    return this.instance.put(`/shops/${id}`, data, authConfigs(config, true));
  };
}

export default new ShopApi();
