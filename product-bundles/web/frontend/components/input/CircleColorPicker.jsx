import { useCallback, useMemo, useState } from 'react';
import { CirclePicker } from 'react-color';
import { Box, Popover } from '@shopify/polaris';
import './CircleColorPicker.scss';

export const CircleColorPicker = ({ value, palette = [], onChange = () => {} }) => {
  const presetColors = useMemo(() => {
    const originalPalette = [
      '#FF6347',
      '#FFA500',
      '#FFD700',
      '#9ACD32',
      '#00FA9A',
      '#20B2AA',
      '#1E90FF',
      '#FFA07A',
      '#FFCBA4',
      '#FFECB3',
      '#D2E680',
      '#90EE90',
      '#AFEEEE',
      '#87CEFA',
      '#FF69B4',
      '#F08080',
      '#000000',
      '#696969',
      '#A9A9A9',
      '#D3D3D3',
      '#FFFFFF',
    ];
    return [...originalPalette.slice(0, originalPalette.length - palette.length), ...palette];
  }, [palette]);

  const [showPicker, setShowPicker] = useState(false);

  const togglePicker = useCallback(() => setShowPicker(!showPicker), [showPicker]);

  const pickerActivator = useMemo(
    () => <div className="circle-picker__activator" style={{ background: value }} onClick={togglePicker} />,
    [togglePicker, value]
  );

  return (
    <Popover active={showPicker} activator={pickerActivator} onClose={togglePicker}>
      <Box padding="200">
        <CirclePicker
          triangle="hide"
          color={value}
          colors={presetColors}
          circleSpacing={8}
          onChange={({ hex }) => onChange(hex)}
        />
      </Box>
    </Popover>
  );
};
