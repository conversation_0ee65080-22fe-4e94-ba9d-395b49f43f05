import { InlineStack, TextField } from '@shopify/polaris';
import { CircleColorPicker } from './CircleColorPicker';
import { useCallback } from 'react';

export const ColorInput = ({ value, palette = [], onChange = () => {} }) => {
  //
  const validateHexColor = useCallback((color) => {
    if (color === 'transparent') return true;
    const hexColorRegex = /^#([a-fA-F0-9]{3}|[a-fA-F0-9]{6})$/;
    return hexColorRegex.test(color);
  }, []);

  const handleBlur = useCallback(() => {
    if (validateHexColor(value)) return;
    onChange('#000000');
  }, [onChange, validateHexColor, value]);

  const handleChange = useCallback(
    (value) => {
      let newValue = value;
      if (!newValue.startsWith('#')) newValue = '#' + newValue;
      onChange(newValue);
    },
    [onChange]
  );

  return (
    <InlineStack gap="100">
      <CircleColorPicker value={value} palette={palette} onChange={onChange} />
      <TextField value={value} onChange={handleChange} onBlur={handleBlur} />
    </InlineStack>
  );
};
