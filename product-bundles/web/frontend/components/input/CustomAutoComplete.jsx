import { Autocomplete } from '@shopify/polaris';
import { useState, useCallback, useEffect } from 'react';

export const CustomAutoComplete = ({ id, label, value, name, options, disabled, onChange = () => {}, ...props }) => {
  const [inputValue, setInputValue] = useState(value);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [filteredOptions, setFilteredOptions] = useState([]);

  useEffect(() => {
    setFilteredOptions(options);
  }, [options]);

  useEffect(() => {
    setInputValue(value);
    setSelectedOptions([value]);
  }, [value]);

  const updateText = useCallback(
    (value) => {
      setInputValue(value);
      onChange(value);

      if (value === '') {
        setFilteredOptions(options);
        return;
      }

      const filterRegex = new RegExp(value, 'i');
      const resultOptions = options.filter((option) => {
        if (typeof option.label === 'string') return option.label.match(filterRegex);
        if (typeof option.value === 'string') return option.value.match(filterRegex);
        return true;
      });
      setFilteredOptions(resultOptions);
    },
    [options, onChange]
  );

  const updateSelection = useCallback(
    (selected) => {
      setSelectedOptions(selected);
      setInputValue(selected[0] || '');

      const selectedOption = options.find((o) => o.value === selected[0]);
      onChange(selected[0] || '', selectedOption);
    },
    [onChange, options]
  );

  const textField = (
    <Autocomplete.TextField
      id={id}
      name={name}
      label={label}
      value={inputValue}
      disabled={disabled}
      onChange={updateText}
      autoComplete="off"
      {...props}
    />
  );

  return (
    <Autocomplete
      options={filteredOptions}
      selected={selectedOptions}
      onSelect={updateSelection}
      textField={textField}
    />
  );
};
