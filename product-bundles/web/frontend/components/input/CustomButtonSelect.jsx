import { Button, ButtonGroup } from '@shopify/polaris';
import { useMemo } from 'react';

export const CustomButtonSelect = ({ value, options, fullWidth, onChange = () => {} }) => {
  const listMarkup = useMemo(
    () =>
      options?.map((o, i) => (
        <Button key={i} pressed={o.value === value} onClick={() => onChange(o.value)}>
          {o.label}
        </Button>
      )),
    [onChange, options, value]
  );

  return (
    <ButtonGroup gap="extraTight" variant="segmented" fullWidth={fullWidth}>
      {listMarkup}
    </ButtonGroup>
  );
};
