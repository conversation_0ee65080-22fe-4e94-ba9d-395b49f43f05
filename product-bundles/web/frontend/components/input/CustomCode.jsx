import { Button } from '@shopify/polaris';
import { toast } from 'sonner';
import copy from 'copy-to-clipboard';
import './CustomCode.scss';

export const CustomCode = ({ children, type = 'single-line', copyable }) => {
  const copyToClipboard = (text) => {
    copy(text);
    toast.success('Code copied to clipboard!', { duration: 1500 });
  };

  return (
    <span className="custom-code">
      <code className={`${type}`}>
        {children}{' '}
        {copyable && type === 'single-line' && (
          <Button onClick={() => copyToClipboard(children)} size="micro">
            Copy
          </Button>
        )}
      </code>
      {copyable && type !== 'single-line' && <Button onClick={() => copyToClipboard(children)}>Copy</Button>}
    </span>
  );
};
