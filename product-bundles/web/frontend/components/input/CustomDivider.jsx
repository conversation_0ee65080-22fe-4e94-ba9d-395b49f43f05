export const CustomDivider = ({ children, style = {} }) => {
  const dividerStyle = {
    display: 'flex',
    alignItems: 'center',
    textAlign: 'center',
    ...style,
  };

  const lineStyle = {
    flex: 1,
    height: '1px',
    backgroundColor: '#E3E3E3',
  };

  const textStyle = {
    margin: '0 10px',
    color: '#666',
    fontWeight: 'bold',
    fontSize: '14px',
  };

  return (
    <div style={dividerStyle}>
      <div style={lineStyle} />
      {children && <span style={textStyle}>{children}</span>}
      <div style={lineStyle} />
    </div>
  );
};
