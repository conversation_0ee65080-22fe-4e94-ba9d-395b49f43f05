import { Checkbox, ChoiceList, RangeSlider, Select, TextField } from '@shopify/polaris';
import { CustomJoditEditor } from './CustomJoditEditor';
import { CustomSelect } from './CustomSelect';
import { TwitterColorPicker } from './TwitterColorPicker';
import { CustomAutoComplete } from './CustomAutoComplete';
import { ImagePicker } from './ImagePicker';
import { CustomSwitch } from './CustomSwitch';
import { CustomSwitch2 } from './CustomSwitch2';
import { CustomButtonSelect } from './CustomButtonSelect';
import { CircleColorPicker } from './CircleColorPicker';
import { ColorInput } from './ColorInput';
import { PopoverSelect } from './PopoverSelect';
import './CustomInput.scss';

const CustomLabel = ({ id, label }) => {
  if (!label) return null;

  return (
    <div className="Polaris-Labelled__LabelWrapper">
      <div className="Polaris-Label">
        <label htmlFor={id} className="Polaris-Label__Text">
          <span className="Polaris-Text--root Polaris-Text--bodyMd">{label}</span>
        </label>
      </div>
    </div>
  );
};

const CustomAdditionalText = ({ helpText, error }) => {
  if (error)
    return (
      <div className="Polaris-Labelled__Error">
        <div className="Polaris-InlineError">
          <div className="Polaris-InlineError__Icon">
            <span className="Polaris-Icon">
              <svg viewBox="1 1 18 18" className="Polaris-Icon__Svg" focusable="false" aria-hidden="true">
                <path d="M10 6a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"></path>
                <path d="M11 13a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path>
                <path
                  fillRule="evenodd"
                  d="M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Zm-1.5 0a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0Z"
                ></path>
              </svg>
            </span>
          </div>
          <span className="Polaris-Text--root Polaris-Text--bodyMd">{error}</span>
        </div>
      </div>
    );

  if (helpText)
    return (
      <div className="Polaris-Labelled__HelpText">
        <span className="Polaris-Text--root Polaris-Text--bodyMd Polaris-Text--break Polaris-Text--subdued">
          {helpText}
        </span>
      </div>
    );
};

// MAIN COMPONENT
export const CustomInput = ({
  id,
  value,
  label,
  helpText,
  options,
  error,
  type = 'text',
  style = {},
  onChange = () => {},
  callbackHandler = () => {},
  ...props
}) => {
  if (type === 'label') {
    return (
      <div className="tpt-custom-input tpt-custom-input--label" style={style}>
        <CustomLabel label={label} />
      </div>
    );
  }

  if (type === 'switch')
    return (
      <div className="tpt-custom-input tpt-custom-input--switch" style={style}>
        <CustomLabel id={id} label={label} />
        <CustomSwitch
          checked={value}
          onChange={(checked) => {
            onChange(checked, id);
            callbackHandler(checked, id);
          }}
          {...props}
        />
        <CustomAdditionalText helpText={helpText} error={error} />
      </div>
    );

  if (type === 'switch2')
    return (
      <div className="tpt-custom-input tpt-custom-input--switch2" style={style}>
        <CustomLabel id={id} label={label} />
        <CustomSwitch2
          checked={value}
          onChange={(checked) => {
            onChange(checked, id);
            callbackHandler(checked, id);
          }}
          {...props}
        />
        <CustomAdditionalText helpText={helpText} error={error} />
      </div>
    );

  if (type === 'checkbox') {
    return (
      <div className="tpt-custom-input tpt-custom-input--check-box" style={style}>
        <Checkbox
          label={label}
          checked={value}
          onChange={(checked) => {
            onChange(checked, id);
            callbackHandler(checked, id);
          }}
          {...props}
        />
      </div>
    );
  }

  if (type === 'select')
    return (
      <div className="tpt-custom-input tpt-custom-input--select" style={style}>
        <CustomSelect
          id={id}
          label={label}
          value={value}
          options={options}
          helpText={helpText}
          onChange={(cValue) => {
            onChange(cValue, id);
            callbackHandler(cValue, id);
          }}
          {...props}
        />
      </div>
    );

  if (type === 'popover-select')
    return (
      <div className="tpt-custom-input tpt-custom-input--popover-select" style={style}>
        <CustomLabel id={id} label={label} />
        <PopoverSelect
          id={id}
          value={value}
          options={options}
          helpText={helpText}
          onChange={(cValue) => {
            onChange(cValue, id);
            callbackHandler(cValue, id);
          }}
          {...props}
        />
        <CustomAdditionalText helpText={helpText} error={error} />
      </div>
    );

  if (type === 'basic-select')
    return (
      <div className="tpt-custom-input tpt-custom-input--basic-select" style={style}>
        <Select
          id={id}
          label={label}
          value={value}
          options={options}
          helpText={helpText}
          onChange={(cValue, cId) => {
            onChange(cValue, cId);
            callbackHandler(cValue, cId);
          }}
          {...props}
        />
      </div>
    );

  if (type === 'button-select')
    return (
      <div className="tpt-custom-input tpt-custom-input--button-select" style={style}>
        <CustomLabel id={id} label={label} />
        <CustomButtonSelect
          value={value}
          options={options}
          onChange={(selected) => {
            onChange(selected, id);
            callbackHandler(selected, id);
          }}
          {...props}
        />
        <CustomAdditionalText helpText={helpText} error={error} />
      </div>
    );

  if (type === 'autocomplete')
    return (
      <div className="tpt-custom-input tpt-custom-input--autocomplete" style={style}>
        <CustomAutoComplete
          id={id}
          label={label}
          value={value}
          options={options}
          onChange={(cValue, selected) => {
            onChange(cValue, id, selected);
            callbackHandler(cValue, id, selected);
          }}
          {...props}
        />
        <CustomAdditionalText helpText={helpText} error={error} />
      </div>
    );

  if (type === 'richtext') {
    return (
      <div className="tpt-custom-input tpt-custom-input--rte" style={style}>
        <CustomLabel id={id} label={label} />
        <CustomJoditEditor
          mode="simple"
          value={value}
          onChange={(nValue) => {
            onChange(nValue, id);
            callbackHandler(nValue, id);
          }}
          {...props}
        />{' '}
        <CustomAdditionalText helpText={helpText} error={error} />
      </div>
    );
  }

  if (type === 'richtext-area') {
    return (
      <div className="tpt-custom-input tpt-custom-input--rte-area" style={style}>
        <CustomLabel id={id} label={label} />
        <CustomJoditEditor
          value={value}
          onChange={(nValue) => {
            onChange(nValue, id);
            callbackHandler(nValue, id);
          }}
          {...props}
        />
        <CustomAdditionalText helpText={helpText} error={error} />
      </div>
    );
  }

  if (type === 'color') {
    return (
      <div className="tpt-custom-input tpt-custom-input--color" style={style}>
        <CustomLabel id={id} label={label} />
        <ColorInput
          value={value}
          onChange={(nValue) => {
            onChange(nValue, id);
            callbackHandler(nValue, id);
          }}
          {...props}
        />
        <CustomAdditionalText helpText={helpText} error={error} />
      </div>
    );
  }

  if (type === 'twitter-color-picker') {
    return (
      <div className="tpt-custom-input tpt-custom-input--twitter-color-picker" style={style}>
        <TwitterColorPicker
          value={value}
          onChange={(cValue) => {
            onChange(cValue, id);
            callbackHandler(cValue, id);
          }}
          {...props}
        />
      </div>
    );
  }

  if (type === 'circle-color-picker') {
    return (
      <div className="tpt-custom-input tpt-custom-input--circle-color-picker" style={style}>
        <CircleColorPicker
          value={value}
          onChange={(cValue) => {
            onChange(cValue, id);
            callbackHandler(cValue, id);
          }}
          {...props}
        />
      </div>
    );
  }

  if (type === 'image-picker') {
    return (
      <div className="tpt-custom-input tpt-custom-input--image-picker" style={style}>
        <ImagePicker
          value={value}
          onChange={(cValue) => {
            onChange(cValue, id);
            callbackHandler(cValue, id);
          }}
        />
      </div>
    );
  }

  if (type === 'slider') {
    return (
      <div className="tpt-custom-input tpt-custom-input--slider" style={style}>
        <RangeSlider
          id={id}
          label={label}
          value={value}
          error={error}
          helpText={!error && helpText}
          suffix={props.suffix || (props.unit && `${value}${props.unit}`)}
          onChange={(cValue, cId) => {
            onChange(cValue, cId);
            callbackHandler(cValue, cId);
          }}
          {...props}
        />
      </div>
    );
  }

  if (type === 'choice-list') {
    return (
      <div className="tpt-custom-input tpt-custom-input--choice-list" style={style}>
        <ChoiceList
          name={id}
          title={label}
          choices={options}
          selected={value}
          error={error}
          helpText={!error && helpText}
          onChange={(cValue, cId) => {
            onChange(cValue, cId);
            callbackHandler(cValue, cId);
          }}
          {...props}
        />
      </div>
    );
  }

  if (type === 'time') {
    return (
      <div className="tpt-custom-input tpt-custom-input--time" style={style}>
        <TextField
          id={id}
          label={label}
          value={value}
          type="time"
          error={error}
          helpText={!error && helpText}
          onChange={(cValue, cId) => {
            onChange(cValue, cId);
            callbackHandler(cValue, cId);
          }}
          size="medium"
          onFocus={(e) => e.target.showPicker()}
          {...props}
        />
      </div>
    );
  }

  return (
    <div className="tpt-custom-input tpt-custom-input--text-field" style={style}>
      <TextField
        id={id}
        label={label}
        value={value}
        type={type}
        error={error}
        helpText={!error && helpText}
        onChange={(cValue, cId) => {
          onChange(cValue, cId);
          callbackHandler(cValue, cId);
        }}
        {...props}
      />
    </div>
  );
};

