.tpt-custom-input {
  legend.Polaris-Box {
    font-weight: 500;
  }

  input[type='date'] {
    display: inline-block;
    width: 100%;
  }

  .Polaris-Label > label > span {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.1rem;
  }

  .Polaris-InlineError > span,
  .Polaris-Labelled__HelpText > span {
    padding: 0;
    font-size: 0.75rem;
  }

  .jodit-container {
    border: 1px solid #8d8d8d !important;
    border-radius: 0.5rem !important;

    .jodit-toolbar__box {
      border-radius: 0.5rem 0.5rem 0 0;
      border-bottom: 1px solid #8d8d8d;
    }

    .jodit-workplace {
      border-radius: 0 0 0.5rem 0.5rem;
    }

    .jodit-add-new-line.jodit-add-new-line_after {
      display: none;
    }
  }
}

.tpt-custom-input--select {
  .Polaris-TextField {
    cursor: pointer;

    input {
      cursor: pointer;
      caret-color: transparent;
    }
  }
}

.tpt-custom-input--color {
  > .Polaris-InlineStack {
    > [class=''] {
      flex: 1;
    }
  }
}

.tpt-custom-input--time {
  input {
    padding: 5px;
    justify-content: center;
  }
}

.tpt-custom-input--appearance-picker {
  .<PERSON>is-Button {
    > .Polaris-Text--root {
      width: 100%;
    }
  }
}

.appearance-picker-item {
  .mpg-swatch__option-set {
    overflow: hidden;

    .mpg-swatch__nav {
      flex-wrap: nowrap;

      label {
        white-space: nowrap;
      }
    }
  }
}

[id^='modal-content']:has(.appearance-picker-item) {
  max-height: 540px;
}

.Polaris-OptionList-Option:has(.mpg-popover-select__option) {
  .Polaris-OptionList-Option__Icon {
    display: none;
  }
}
