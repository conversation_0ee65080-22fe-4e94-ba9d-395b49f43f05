import { useCallback, useMemo, useRef } from 'react';
import JoditEditor from 'jodit-react';
import _ from 'lodash';
import './CustomJoditEditor.scss';

export const CustomJoditEditor = ({ mode, onChange, value = '', placeholder, onClick = () => {}, ...props }) => {
  const editor = useRef('');

  const config = useMemo(() => {
    if (mode === 'simple')
      return {
        autofocus: true,
        cursorAfterAutofocus: 'end',
        useSearch: false,
        spellcheck: false,
        enter: 'P',
        defaultMode: '1',
        toolbarAdaptive: false,
        toolbarSticky: false,
        minHeight: 100,
        language: 'en',
        direction: 'ltr',
        placeholder: placeholder || 'Enter text here ...',
        buttons: 'bold,italic,underline,strikethrough,superscript,subscript,link',
        showCharsCounter: false,
        showWordsCounter: false,
        showXPathInStatusbar: false,
      };
    return {
      useSearch: false,
      spellcheck: false,
      enter: 'P',
      defaultMode: '1',
      toolbarAdaptive: false,
      toolbarSticky: false,
      minHeight: 240,
      language: 'en',
      direction: 'ltr',
      placeholder: placeholder || 'Enter text here ...',
      buttons:
        'bold,italic,underline,strikethrough,superscript,subscript,undo,redo,ul,ol,align,lineHeight,link,image,font,fontsize,brush,paragraph,hr,source',
      showCharsCounter: false,
      showWordsCounter: false,
      showXPathInStatusbar: false,
    };
  }, [mode, placeholder]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onChangeDebounce = useCallback(
    _.debounce((value) => onChange(value), 500),
    [onChange]
  );

  return (
    <div className="tpt-custom-jodit" onClick={onClick}>
      <JoditEditor
        ref={editor}
        value={value || ''}
        config={config}
        tabIndex={1}
        onChange={onChangeDebounce}
        onBlur={onChange}
        {...props}
      />
    </div>
  );
};

