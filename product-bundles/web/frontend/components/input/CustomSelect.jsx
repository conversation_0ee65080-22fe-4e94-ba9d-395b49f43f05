import { Autocomplete, Icon } from '@shopify/polaris';
import { SelectIcon } from '@shopify/polaris-icons';
import { useState, useCallback, useMemo } from 'react';

export const CustomSelect = ({ id, label, value, options, helpText, disabled, error, onChange = () => {} }) => {
  const [selectedOptions, setSelectedOptions] = useState([]);
  const valueLabel = useMemo(() => options?.find((o) => o?.value === value)?.label || '', [options, value]);

  const updateSelection = useCallback(
    (selected) => {
      setSelectedOptions(selected);
      onChange(selected[0]);
    },
    [onChange]
  );

  const triggerSelf = useCallback(() => {
    if (!id) return;
    const triggerEl = document.getElementById(id);
    if (triggerEl) triggerEl.click();
  }, [id]);

  const textField = (
    <Autocomplete.TextField
      id={id}
      label={label}
      value={valueLabel}
      helpText={helpText}
      error={error}
      disabled={disabled}
      suffix={
        <div onClick={triggerSelf}>
          <Icon source={SelectIcon} tone="base" />
        </div>
      }
    />
  );

  return <Autocomplete options={options} selected={selectedOptions} onSelect={updateSelection} textField={textField} />;
};

