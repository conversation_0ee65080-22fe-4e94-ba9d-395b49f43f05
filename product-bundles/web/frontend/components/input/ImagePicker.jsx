import './ImagePicker.scss';
import { BlockStack, Box, Button, DropZone, Popover, Text, TextField } from '@shopify/polaris';
import { useCallback, useContext, useMemo, useState } from 'react';
import { UploadIcon } from '@shopify/polaris-icons';
import { useToast } from '../../hooks';
import { useMutation } from 'react-query';
import { FileApi } from '../../apis';
import { AppDataContext } from '../../context/AppDataContext';

export const ImagePicker = ({ value, onChange = () => {} }) => {
  const { limitation } = useContext(AppDataContext);

  // Toast
  const { toast } = useToast();

  // Popover
  const [activePopover, setActivePopover] = useState(false);
  const togglePopover = useCallback(() => setActivePopover(!activePopover), [activePopover]);
  const activator = useMemo(
    () => (
      <div>
        {value && (
          <div
            className="image-picker__activator"
            onClick={togglePopover}
            style={{ backgroundImage: `url("${value}")` }}
          />
        )}
        {!value && <Button size="large" icon={UploadIcon} onClick={togglePopover} />}
      </div>
    ),
    [togglePopover, value]
  );

  // Handle image change
  const [imageError, setImageError] = useState('');

  const validateImageURL = useCallback((url) => {
    if (!url) return null;

    const urlRegex =
      /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:[/?#]\S*)?$/i;
    if (!urlRegex.test(url)) return 'Invalid image URL format';

    return null;
  }, []);

  const handleImageChange = useCallback(
    (url) => {
      const error = validateImageURL(url);
      setImageError(error);
      onChange(url);
      //
    },
    [onChange, validateImageURL]
  );

  // Files upload
  const [openFileDialog, setOpenFileDialog] = useState(false);
  const { isLoading: isFileUploading, mutateAsync: uploadFile } = useMutation((data) => FileApi.upload(data));

  const validImageTypes = useMemo(() => ['image/gif', 'image/jpeg', 'image/png', 'image/webp', 'image/avif'], []);

  const handleDropZoneDrop = useCallback(
    async (_dropFiles, acceptedFiles, _rejectedFiles) => {
      const selectedFile = acceptedFiles[0];
      const maxSizeInMB = 1;

      if (selectedFile && selectedFile.size > maxSizeInMB * 1024 * 1024) {
        toast.error(`File too large`);
        return;
      }

      if (!validImageTypes.includes(selectedFile.type)) {
        toast.error('Type not allowed');
        return;
      }

      const reader = new FileReader();
      reader.onload = async () => {
        const base64Content = reader.result.split(',')[1];

        try {
          const response = await uploadFile({
            content: base64Content,
            name: selectedFile.name,
            size: selectedFile.size,
            type: selectedFile.type,
          });

          const imageUrl = response?.data?.data || '';
          handleImageChange(imageUrl);
          //
        } catch (error) {
          console.error(error);
          toast.error('Upload failed');
        }
      };
      reader.readAsDataURL(selectedFile);
    },

    // eslint-disable-next-line react-hooks/exhaustive-deps
    [handleImageChange, uploadFile, validImageTypes]
  );

  const toggleOpenFileDialog = useCallback(() => setOpenFileDialog(!openFileDialog), [openFileDialog]);

  return (
    <Popover active={activePopover} activator={activator} onClose={togglePopover} sectioned>
      <Box width="210px" paddingBlock="100">
        <BlockStack gap="300">
          <TextField
            disabled={isFileUploading || !limitation?.allowed_upload_image}
            onChange={handleImageChange}
            value={value}
            label={
              <Text fontWeight="medium">
                Enter image URL or upload{' '}
                <Button
                  variant="plain"
                  onClick={toggleOpenFileDialog}
                  loading={isFileUploading}
                  disabled={!limitation?.allowed_upload_image}
                >
                  <Text fontWeight="medium">here</Text>
                </Button>
              </Text>
            }
            error={imageError}
            placeholder="Enter image URL . . ."
          />

          {!limitation?.allowed_upload_image && (
            <Text variant="bodySm" tone="caution">
              Upgrade your plan to unlock this.
            </Text>
          )}

          <div
            style={{
              width: '100%',
              border: '1px solid lightgray',
              aspectRatio: 1,
              borderRadius: '.25rem',
              backgroundSize: 'contain',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center',
              backgroundImage: `url("${
                isFileUploading
                  ? 'https://placehold.co/240x240/EEE/31343C?font=oswald&text=UPLOADING'
                  : value || 'https://placehold.co/240x240/EEE/31343C?font=oswald&text=NO%20IMAGE'
              }")`,
            }}
          />
          <div style={{ display: 'none' }}>
            <DropZone
              accept="image/*"
              type="image"
              allowMultiple={false}
              openFileDialog={openFileDialog}
              onDrop={handleDropZoneDrop}
              onFileDialogClose={toggleOpenFileDialog}
            />
          </div>
        </BlockStack>
      </Box>
    </Popover>
  );
};

