import { But<PERSON>, OptionList } from '@shopify/polaris';
import { useState, useMemo, useRef, useEffect } from 'react';
import { SkeletonTextLine } from '../ui';
import * as HoverCard from '@radix-ui/react-hover-card';
import './PopoverSelect.scss';

export const PopoverSelect = ({ value, options, disabled, onChange = () => {}, render = () => null }) => {
  const selectedRef = useRef(null);
  const [popoverActive, setPopoverActive] = useState(false);

  const renderedOptions = useMemo(
    () =>
      options.map((option) => ({
        value: option.value,
        label: (
          <div
            className={`mpg-popover-select__option ${option.value === value ? 'selected' : ''}`}
            ref={option.value === value ? selectedRef : null}
          >
            {render(option) || option.label}
          </div>
        ),
      })),
    [options, render, value]
  );

  const labelValue = useMemo(() => options?.find((o) => o.value === value)?.label, [options, value]);

  useEffect(() => {
    window.setTimeout(() => {
      if (!selectedRef?.current) return;
      selectedRef.current.scrollIntoView({ behavior: 'instant' });
    }, 50);
  }, [popoverActive]);

  return (
    <HoverCard.Root
      openDelay={0}
      onOpenChange={(open) => {
        if (!open) setPopoverActive(false);
      }}
    >
      {popoverActive ? (
        <HoverCard.Trigger asChild>
          <div>
            <Button onClick={() => {}} size="large" disabled={disabled} fullWidth disclosure>
              {labelValue || <SkeletonTextLine width="80px" height="10px" margin="0" />}
            </Button>
          </div>
        </HoverCard.Trigger>
      ) : (
        <div onClick={() => setPopoverActive(true)}>
          <Button onClick={() => {}} size="large" disabled={disabled} fullWidth disclosure>
            {labelValue || <SkeletonTextLine width="80px" height="10px" margin="0" />}
          </Button>
        </div>
      )}
      <HoverCard.Portal>
        <HoverCard.Content>
          <div
            style={{
              display: popoverActive ? 'block' : 'none',
              background: 'white',
              borderRadius: '14px',
              boxShadow: '0 0 8px rgba(0, 0, 0, 0.5)',
              maxHeight: '400px',
              overflowY: 'auto',
            }}
          >
            <OptionList
              selected={[value]}
              onChange={([newValue]) => {
                onChange(newValue);
                setPopoverActive(false);
              }}
              options={renderedOptions}
            />
          </div>
          <HoverCard.Arrow style={{ fill: 'white' }} />
        </HoverCard.Content>
      </HoverCard.Portal>
    </HoverCard.Root>
  );
};
