import { useMemo, useState } from 'react';
import { TwitterPicker } from 'react-color';
import * as HoverCard from '@radix-ui/react-hover-card';
import './TwitterColorPicker.scss';

export const TwitterColorPicker = ({ value, palette = [], onChange = () => {} }) => {
  const colorPalette = useMemo(() => {
    const originalPalette = [
      '#FF6347',
      '#FFA500',
      '#FFD700',
      '#9ACD32',
      '#00FA9A',
      '#20B2AA',
      '#1E90FF',
      '#FFA07A',
      '#FFCBA4',
      '#FFECB3',
      '#D2E680',
      '#90EE90',
      '#AFEEEE',
      '#87CEFA',
      '#BA55D3',
      '#9370DB',
      '#EE82EE',
      '#FF69B4',
      '#F08080',
      '#000000',
      '#FFFFFF',
      '#D3D3D3',
      '#A9A9A9',
      '#696969',
    ];
    return [...originalPalette.slice(0, originalPalette.length - palette.length), ...palette];
  }, [palette]);

  const [showTrigger, setShowTrigger] = useState(false);

  return (
    <HoverCard.Root
      openDelay={0}
      onOpenChange={(open) => {
        if (!open) setShowTrigger(false);
      }}
    >
      {showTrigger ? (
        <HoverCard.Trigger asChild>
          <div className="twitter-picker__activator" style={{ background: value }} />
        </HoverCard.Trigger>
      ) : (
        <div className="twitter-picker__activator" style={{ background: value }} onClick={() => setShowTrigger(true)} />
      )}
      <HoverCard.Portal>
        <HoverCard.Content>
          <TwitterPicker triangle="hide" color={value} onChange={({ hex }) => onChange(hex)} colors={colorPalette} />
          <HoverCard.Arrow style={{ fill: 'white' }} />
        </HoverCard.Content>
      </HoverCard.Portal>
    </HoverCard.Root>
  );
};
