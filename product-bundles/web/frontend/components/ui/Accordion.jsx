import { useState } from 'react';
import { Box, InlineStack, Text, Collapsible, Icon } from '@shopify/polaris';
import { PlusIcon, MinusIcon } from '@shopify/polaris-icons';
import './Accordion.scss';

export const Accordion = ({
  items,
  defaultExpanded,
  title,
  size = 'medium',
  iconReverse,
  borderReverse,
  moreIcon: MoreIcon = PlusIcon,
  lessIcon: LessIcon = MinusIcon,
}) => {
  const [expanded, setExpanded] = useState(defaultExpanded);

  return (
    <div className="accordion">
      {title && (
        <Box padding="400">
          <InlineStack align="space-between">
            <Text as="h3" variant="headingMd">
              {title}
            </Text>
          </InlineStack>
        </Box>
      )}
      {items.map(({ title, id, content }) => {
        const isExpanded = expanded === id;
        return (
          <Box
            background="bg-surface-secondary"
            borderColor="border"
            borderBlockStartWidth={borderReverse ? '0' : '025'}
            borderBlockEndWidth={borderReverse ? '025' : '0'}
            key={id}
          >
            <Box background={isExpanded ? 'bg-surface-active' : 'bg-surface'} paddingBlock="300" paddingInline="400">
              <div
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  return setExpanded((prev) => (id === prev ? null : id));
                }}
              >
                <div className={`accordion__trigger accordion__trigger--${size}`}>
                  <InlineStack
                    gap="200"
                    align={iconReverse ? 'space-between' : 'start'}
                    blockAlign="center"
                    wrap={false}
                    direction={iconReverse ? 'row-reverse' : 'row'}
                  >
                    <div>
                      {isExpanded ? (
                        <Icon source={LessIcon} tone="subdued" />
                      ) : (
                        <Icon source={MoreIcon} tone="subdued" />
                      )}
                    </div>
                    <Text variant="headingMd" as="p" fontWeight="medium">
                      {title}
                    </Text>
                  </InlineStack>
                </div>
              </div>
            </Box>
            <Collapsible open={isExpanded}>
              <Box padding="400" background="bg-surface">
                {content}
              </Box>
            </Collapsible>
          </Box>
        );
      })}
    </div>
  );
};

