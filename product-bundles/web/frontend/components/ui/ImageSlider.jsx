import React, { useState, useEffect, useCallback, useRef, Children, cloneElement } from 'react';
import './ImageSlider.scss';

// ImageSlider Item component
const ImageSliderItem = ({ src, alt, className = '', onClick, ...props }) => (
  <div className={`image-slider__item ${className}`} onClick={onClick} {...props}>
    <img src={src} alt={alt} draggable={false} />
  </div>
);

// Main ImageSlider component
export const ImageSlider = ({
  items = [],
  children,
  autoPlay = false,
  autoPlayInterval = 3000,
  showDots = true,
  showArrows = true,
  showCounter = false,
  infinite = true,
  aspectRatio = '2:1',
  className = '',
  onSlideChange,
  fixedIndex,
  ...props
}) => {
  const [currentIndex, setCurrentIndex] = useState(fixedIndex ?? 0);
  const autoPlayRef = useRef(null);

  // Update currentIndex when fixedIndex changes
  useEffect(() => {
    if (fixedIndex !== undefined) {
      setCurrentIndex(fixedIndex);
    }
  }, [fixedIndex]);

  // Determine slides from either items prop or children
  const slides =
    items.length > 0
      ? items.map((item, index) => (
          <ImageSliderItem
            key={item.id || index}
            src={item.src || item.image || item.url}
            alt={item.alt || item.title || `Slide ${index + 1}`}
            {...(item.props || {})}
          />
        ))
      : Children.toArray(children).filter(
          (child) => child.type === ImageSliderItem || child.type?.displayName === 'ImageSliderItem'
        );

  const totalSlides = slides.length;

  // Calculate aspect ratio padding
  const getAspectRatioPadding = () => {
    const [width, height] = aspectRatio.split(':').map(Number);
    return (height / width) * 100;
  };

  // Navigation functions
  const goToSlide = useCallback(
    (index) => {
      if (index < 0 || index >= totalSlides) return;
      setCurrentIndex(index);
      onSlideChange?.(index);
    },
    [onSlideChange, totalSlides]
  );

  const nextSlide = useCallback(() => {
    const newIndex = infinite ? (currentIndex + 1) % totalSlides : Math.min(currentIndex + 1, totalSlides - 1);
    goToSlide(newIndex);
  }, [currentIndex, totalSlides, infinite, goToSlide]);

  const prevSlide = useCallback(() => {
    const newIndex = infinite
      ? currentIndex === 0
        ? totalSlides - 1
        : currentIndex - 1
      : Math.max(currentIndex - 1, 0);
    goToSlide(newIndex);
  }, [currentIndex, totalSlides, infinite, goToSlide]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.key === 'ArrowLeft') {
        prevSlide();
      } else if (e.key === 'ArrowRight') {
        nextSlide();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [prevSlide, nextSlide]);

  // Auto play - only run if not controlled by fixedIndex
  useEffect(() => {
    if (autoPlay && totalSlides > 1 && fixedIndex === undefined) {
      autoPlayRef.current = setInterval(() => {
        setCurrentIndex((prev) => (infinite ? (prev + 1) % totalSlides : Math.min(prev + 1, totalSlides - 1)));
      }, autoPlayInterval);

      return () => clearInterval(autoPlayRef.current);
    }
  }, [autoPlay, autoPlayInterval, totalSlides, infinite, fixedIndex]);

  if (totalSlides === 0) {
    return <div className="image-slider image-slider--empty">No images to display</div>;
  }

  return (
    <div className={`image-slider ${className}`} {...props}>
      <div className="image-slider__container" style={{ paddingBottom: `${getAspectRatioPadding()}%` }}>
        <div className="image-slider__track" style={{ transform: `translateX(-${currentIndex * 100}%)` }}>
          {slides.map((slide, index) =>
            cloneElement(slide, {
              key: slide.key || index,
              className: `${slide.props.className || ''} ${index === currentIndex ? 'active' : ''}`.trim(),
            })
          )}
        </div>

        {/* Navigation Arrows */}
        {showArrows && totalSlides > 1 && (
          <>
            <button
              className="image-slider__arrow image-slider__arrow--prev"
              onClick={prevSlide}
              disabled={!infinite && currentIndex === 0}
              aria-label="Previous slide"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  d="M15 18L9 12L15 6"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
            <button
              className="image-slider__arrow image-slider__arrow--next"
              onClick={nextSlide}
              disabled={!infinite && currentIndex === totalSlides - 1}
              aria-label="Next slide"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path
                  d="M9 18L15 12L9 6"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </>
        )}
      </div>

      {/* Dots Indicator */}
      {showDots && totalSlides > 1 && (
        <div className="image-slider__dots">
          {Array.from({ length: totalSlides }, (_, index) => (
            <button
              key={index}
              className={`image-slider__dot ${index === currentIndex ? 'active' : ''}`}
              onClick={() => goToSlide(index)}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Slide Counter */}
      {showCounter && (
        <div className="image-slider__counter">
          {currentIndex + 1} / {totalSlides}
        </div>
      )}
    </div>
  );
};

// Attach Item component
ImageSlider.Item = ImageSliderItem;
ImageSliderItem.displayName = 'ImageSliderItem';

