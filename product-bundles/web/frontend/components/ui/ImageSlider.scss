.image-slider {
  position: relative;
  width: 100%;
  background: #f5f5f5;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  &--empty {
    padding: 2rem;
    text-align: center;
    color: #666;
    font-size: 1.1rem;
  }

  &__container {
    position: relative;
    width: 100%;
    height: 0;
    overflow: hidden;
  }

  &__track {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    transition: transform 0.4s ease;
  }

  &__item {
    flex: 0 0 100%;
    width: 100%;
    height: 100%;
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
      user-select: none;
      display: block;
    }
  }

  // Navigation Arrows
  &__arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    opacity: 0; // Hidden by default on desktop

    &:hover:not(:disabled) {
      background: rgba(0, 0, 0, 0.9);
      transform: translateY(-50%) scale(1.1);
    }

    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }

    svg {
      width: 20px;
      height: 20px;
      color: white;
    }

    &--prev {
      left: 15px;
    }

    &--next {
      right: 15px;
    }
  }

  // Show arrows on hover for desktop
  &:hover &__arrow {
    opacity: 1;
  }

  // Dots Indicator
  &__dots {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 20px;
  }

  &__dot {
    width: 8px;
    height: 8px;
    padding: 0;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.8);
      transform: scale(1.2);
    }

    &.active {
      width: 24px;
      border-radius: 4px;
      background: white;
    }
  }

  // Slide Counter
  &__counter {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 6px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
  }

  // Mobile responsive
  @media (max-width: 576px) {
    &__arrow {
      width: 36px;
      height: 36px;
      opacity: 1; // Always visible on mobile

      &--prev {
        left: 10px;
      }

      &--next {
        right: 10px;
      }

      svg {
        width: 18px;
        height: 18px;
      }
    }

    &__dots {
      bottom: 10px;
      gap: 6px;
      padding: 6px 10px;
    }

    &__dot {
      width: 6px;
      height: 6px;

      &.active {
        width: 20px;
      }
    }

    &__counter {
      top: 10px;
      right: 10px;
      font-size: 0.75rem;
      padding: 4px 8px;
    }
  }
}

