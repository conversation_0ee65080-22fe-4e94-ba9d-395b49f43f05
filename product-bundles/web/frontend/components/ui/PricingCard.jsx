import { BlockStack, Card, Text, InlineStack, Box, Button, Badge, ButtonGroup, Divider, Icon } from '@shopify/polaris';
import { SkeletonTextLine } from './SkeletonTextLine';
import { useMutation } from 'react-query';
import { DiscountApi, PricingPlanApi } from '../../apis';
import { useCallback, useContext, useState } from 'react';
import { AppDataContext } from '../../context/AppDataContext';
import { useModal, useRedirect, useToast } from '../../hooks';
import { CustomInput } from '../input';
import { CheckCircleIcon } from '@shopify/polaris-icons';

export const PricingCard = ({
  id,
  title,
  description,
  price,
  isUpgrade,
  features,
  featuredText,
  frequency,
  loading,
}) => {
  const redirect = useRedirect();
  const { shop } = useContext(AppDataContext);
  const { toast } = useToast();
  const {
    modalOpen: modalConfirmOpen,
    showModal: showModalConfirm,
    hideModal: hideModalConfirm,
    Modal: ModalConfirm,
  } = useModal({});

  // validate discount
  const { isLoading: isCouponValidating, mutateAsync: validateCoupon } = useMutation((discount) =>
    DiscountApi.validate(discount)
  );

  const [coupon, setCoupon] = useState('');
  const [couponError, setCouponError] = useState(null);
  const [couponValid, setCouponValid] = useState(null);
  const [discount, setDiscount] = useState(null);

  const handleValidateCoupon = useCallback(async () => {
    const validateResult = await validateCoupon({ discount_code: coupon });
    if (validateResult?.data?.valid) {
      setDiscount(validateResult?.data?.discount);
      setCouponValid(validateResult?.data?.message);
      setCouponError(null);
    } else {
      setDiscount(null);
      setCouponError(validateResult?.data?.message);
      setCouponValid(null);
    }
  }, [coupon, validateCoupon]);

  // create charge
  const [isChargeCreating, setIsChargeCreating] = useState(false);
  const { mutateAsync: createCharge } = useMutation((data) =>
    PricingPlanApi.createChargeByPlanId(data.planId, { discountId: data.discountId })
  );

  const handleCreateCharge = useCallback(async () => {
    setIsChargeCreating(true);
    const chargeData = await createCharge({ planId: id, discountId: discount?._id });
    const confirmationUrl = chargeData?.data?.confirmationUrl;
    if (confirmationUrl) redirect(confirmationUrl, true);
    else {
      setIsChargeCreating(false);
      toast.message('Your plan has been updated.');
      redirect('/');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createCharge, id, redirect]);

  if (loading) return <SkeletonPricingCard />;

  return (
    <div
      style={{
        width: '18.5rem',
        zIndex: '0',
        position: 'relative',
        boxShadow: featuredText ? '0px 0px 15px 4px #CDFEE1' : '',
        borderRadius: '.75rem',
      }}
    >
      {featuredText && (
        <div style={{ position: 'absolute', top: '-15px', right: '6px', zIndex: '100' }}>
          <Badge size="large" tone="success">
            {featuredText}
          </Badge>
        </div>
      )}
      <Card>
        <BlockStack gap="400">
          <BlockStack gap="200" align="start">
            <Text as="h3" variant="headingLg" tone="subdued">
              {title}
            </Text>
            {description && (
              <Text as="p" variant="bodySm" tone="subdued">
                {description}
              </Text>
            )}
          </BlockStack>

          {price ? (
            <BlockStack>
              <InlineStack blockAlign="end" gap="100" align="start">
                <Text as="h2" variant="heading2xl">
                  ${price}
                </Text>
                <Box paddingBlockEnd="200">
                  <Text variant="bodySm">/ {frequency?.replace('ly', '')}</Text>
                </Box>
              </InlineStack>
            </BlockStack>
          ) : (
            <Text as="h2" variant="heading2xl">
              Free
            </Text>
          )}

          <Box minHeight="20rem">
            <BlockStack gap="100">
              {id === 'free' && (
                <Box paddingBlockEnd="200">
                  <Text variant="bodySm" tone="critical">
                    This plan includes limited features and will remain available unless you switch to another plan.
                  </Text>
                </Box>
              )}
              {features?.map((feature, index) => (
                <Text tone="subdued" as="p" variant="bodyMd" key={index}>
                  {feature}
                </Text>
              ))}
            </BlockStack>
          </Box>

          <Box paddingBlockStart="200" paddingBlockEnd="200">
            <ButtonGroup fullWidth>
              {shop?.app_data?.plan_name === id ? (
                <Button variant="primary" disabled>
                  Current Plan
                </Button>
              ) : (
                <Button
                  variant="primary"
                  loading={isChargeCreating}
                  disabled={id === 'free'}
                  onClick={showModalConfirm}
                >
                  Switch Plan
                </Button>
              )}
            </ButtonGroup>
          </Box>
        </BlockStack>
      </Card>

      <ModalConfirm
        open={modalConfirmOpen}
        title={isUpgrade ? 'Upgrade Plan' : 'Downgrade Plan'}
        onClose={hideModalConfirm}
        size="small"
        primaryAction={{
          content: 'Continue',
          loading: isChargeCreating,
          onAction: handleCreateCharge,
        }}
        secondaryActions={[
          {
            content: 'Cancel',
            disabled: isChargeCreating,
            onAction: hideModalConfirm,
          },
        ]}
      >
        <BlockStack gap={200}>
          <Text>
            {isUpgrade
              ? `You are upgrading to the ${title?.toUpperCase()} plan. Enjoy enhanced features and greater flexibility. Proceed?`
              : `You are downgrading to the ${title?.toUpperCase()} plan. Some advanced features will be removed. Continue?`}
          </Text>
          {price > 0 && (
            <>
              <Divider />
              <InlineStack blockAlign={couponError || couponValid ? 'center' : 'end'} gap={200}>
                <div style={{ flex: 1 }}>
                  <BlockStack gap={100}>
                    <CustomInput
                      size="slim"
                      disabled={isCouponValidating || isChargeCreating}
                      label={
                        <Text variant="bodyMd" fontWeight="medium">
                          Enter a coupon code if you have one
                        </Text>
                      }
                      placeholder="Enter code"
                      value={coupon}
                      onChange={setCoupon}
                      error={couponError}
                    />
                    {couponValid && (
                      <InlineStack gap={150} blockAlign="center">
                        <span>
                          <Icon source={CheckCircleIcon} tone="success" />
                        </span>
                        <Text tone="success" variant="bodySm">
                          Coupon applied
                        </Text>
                      </InlineStack>
                    )}
                  </BlockStack>
                </div>
                <Button
                  size="micro"
                  loading={isCouponValidating}
                  disabled={!coupon || isChargeCreating}
                  onClick={handleValidateCoupon}
                >
                  Apply
                </Button>
              </InlineStack>
            </>
          )}
        </BlockStack>
      </ModalConfirm>
    </div>
  );
};

function SkeletonPricingCard() {
  return (
    <div
      style={{
        width: '19rem',
        borderRadius: '.75rem',
        position: 'relative',
        zIndex: '0',
      }}
    >
      <Card>
        <BlockStack gap="500">
          <SkeletonTextLine height="24px" width="50%" margin="0" />

          <SkeletonTextLine height="35px" margin="0" />

          <Box minHeight="18rem">
            <BlockStack gap="100">
              {[1, 2, 3, 4, 5, 6, 7, 8]?.map((id) => (
                <SkeletonTextLine key={id} margin="10px" />
              ))}
            </BlockStack>
          </Box>

          <Box paddingBlockStart="200" paddingBlockEnd="200">
            <ButtonGroup fullWidth>
              <SkeletonTextLine margin="0" height="28px" />
            </ButtonGroup>
          </Box>
        </BlockStack>
      </Card>
    </div>
  );
}

