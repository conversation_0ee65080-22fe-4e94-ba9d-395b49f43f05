import { BlockStack, ResourceList, Text, ResourceItem, Box, InlineStack } from '@shopify/polaris';
import { DragHandleIcon } from '@shopify/polaris-icons';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import { restrictToVerticalAxis, restrictToParentElement } from '@dnd-kit/modifiers';
import styles from './SortableList.module.css';

export const SortableList = ({
  items,
  setItems,
  title,
  sortable = true,
  resourceName = { singular: 'item', plural: 'items' },
  itemBlockAlign = 'center',
  idResolver = (item) => item.id,
  uuidResolver = (item) => item.id,
  renderItem = (item) => item.id,
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event) => {
    const { active, over } = event;
    const activeId = idResolver(active);
    const overId = idResolver(over);

    if (activeId !== overId) {
      // Updates items in state, add additional update handler logic here (e.g. API calls, toasts, etc.)
      const oldIndex = items.findIndex((item) => idResolver(item) === activeId);
      const newIndex = items.findIndex((item) => idResolver(item) === overId);
      const updatedItems = arrayMove(items, oldIndex, newIndex);

      setItems(updatedItems);
    }
  };

  return (
    <BlockStack gap="300">
      {title && (
        <Box paddingBlockStart="300" paddingInlineStart="300" zIndex="100">
          <Text as="h3" variant="headingSm">
            {title}
          </Text>
        </Box>
      )}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        modifiers={[restrictToVerticalAxis, restrictToParentElement]}
      >
        <SortableContext items={items} strategy={verticalListSortingStrategy}>
          <ResourceList
            resourceName={resourceName}
            items={items}
            renderItem={(item, id, index) => {
              return (
                <Item id={idResolver(item)} uuid={uuidResolver(item)} sortable={sortable} blockAlign={itemBlockAlign}>
                  {renderItem(item, id, index)}
                </Item>
              );
            }}
          />
        </SortableContext>
      </DndContext>
    </BlockStack>
  );
};

function Item({ id, uuid, sortable, blockAlign, children }) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id,
  });

  const style = {
    ...(transform
      ? {
          transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
          transition,
        }
      : {}),
    zIndex: isDragging ? 1000 : 0,
    position: 'relative',
  };

  return (
    <div className={styles.sortableItem} style={style} ref={setNodeRef}>
      <ResourceItem id={id} onClick={() => {}}>
        <InlineStack gap="400" blockAlign={blockAlign} wrap={false}>
          {sortable && (
            <div
              {...attributes}
              {...listeners}
              onClick={(e) => e.stopPropagation()}
              className={`${styles.itemAction} ${uuid}`}
              style={{ touchAction: 'none', cursor: 'grab' }} // Prevents page scrolling on mobile touch
            >
              <DragHandleIcon width="20" height="20" />
            </div>
          )}
          {children}
        </InlineStack>
      </ResourceItem>
    </div>
  );
}

