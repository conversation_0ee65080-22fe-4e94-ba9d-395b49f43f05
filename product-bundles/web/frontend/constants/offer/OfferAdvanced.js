export const PRICE_UNIT_OPTIONS = [
  { id: 'price_unit_total', label: 'Show total price', value: false },
  { id: 'price_unit_per_item', label: 'Show price per item', value: true },
];

export const PRICE_COMPARE_TYPE_OPTIONS = [
  { id: 'price_compare_show', label: 'Show (if available)', value: 'show_if_available' },
  { id: 'price_compare_hidden', label: 'Hidden', value: 'hidden' },
  { id: 'price_compare_custom', label: 'Custom compare price', value: 'show_custom_price' },
];

export const PRICE_ROUNDING_OPTIONS = [
  { label: 'None', value: 'none' },
  { label: 'Round to .99', value: '.99' },
  { label: 'Round to .95', value: '.95' },
  { label: 'Round to .90', value: '.90' },
  { label: 'Round to x.9', value: '.x9' },
  { label: 'Round to x.0', value: '.x0' },
  { label: 'Round to .00', value: '.00' },
];
