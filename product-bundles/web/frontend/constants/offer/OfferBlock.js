export const INITIAL_OFFER_VALUES = {
  uuid: 'offer_1234567890', // random ID
  name: 'Summer Sale Discount', // name at offer list
  // shop: 'example-store', // not required
  type: 'quantity-discount', // pick type at offer list
  is_active: true,

  general: {
    title: 'BUNDLE & SAVE',
    subtitle: 'Get the best deals on your favorite products',
    footer_text: 'Limited time offer',
    show_title: true,
    show_subtitle: true,
    show_footer_text: true,
    // atc_button_text: 'Add to Cart',
    // out_of_stock_text: 'Out of Stock',
    // atc_button_type: 'custom_button',
    // atc_button_behavior: 'add_and_toast',
    // atc_message: 'Item added to cart!',
    apply_to_products: 'all_products',
    applied_products: [
      {
        id: 'gid://shopify/Product/7765893710072',
        title: 'ELRVIKE 16 Million HD Video',
        image: {
          id: 'gid://shopify/ProductImage/37835477287160',
          originalSrc:
            'https://cdn.shopify.com/s/files/1/0615/2283/7752/products/ELRVIKE-16-Million-HD-Video-And-Photo-Integrated-Home-Camera-2-7-inch-HD-Digital-Camera.jpg?v=1659155152',
        },
        totalVariants: 4,
      },
      {
        id: 'gid://shopify/Product/7950757462264',
        title: 'Fujifilm X-H2 Mirrorless Digital',
        image: {
          id: 'gid://shopify/ProductImage/38962691703032',
          originalSrc: 'https://cdn.shopify.com/s/files/1/0615/2283/7752/products/xh2_4.jpg?v=1677821835',
        },
        totalVariants: 4,
      },
    ],
    apply_to_customers: 'all_customers',
    variant_selector_type: 'combined_select',
    default_selected_variant: 'first_available',
    quantity_selector_position: 'above_tiers',
    widget_position: 'above_form_actions',
    widget_custom_positions: [
      {
        selector: '.product-form',
        position: 'beforebegin',
      },
    ],
  },

  settings: {
    show_price_per_unit: true,
    compare_at_price_type: 'show_custom_price',
    custom_compare_at_price: 150.0,

    price_rounding: '.99',

    apply_discount_to_exact_quantity: true,
    limit_one_use_per_customer: true,
    limit_total_uses_of_offer: true,
    total_uses: 100,

    combined_with_order_discount: false,
    combined_with_product_discount: true,
    combined_with_shipping_discount: false,

    start_date: '2025-07-01T00:00:00Z',
    end_date: '2025-07-31T23:59:59Z',
    use_end_date: true,
  },

  tiers: [
    {
      title: 'Basic Tier',
      label_text: 'Best Value',
      badge_text: 'New',
      show_title: true,
      show_label: true,
      show_badge: true,
      label_style: 'style_1',
      quantity_min: 1,
      quantity_max: 2,
      selected_by_default: true,
      discount_type: 'percentage_discount',
      discount_value: 10,
      image_url: 'https://example.com/image1.jpg',
      image_size: 100,
      image_border: {
        color: { r: 200, g: 200, b: 200, a: 1 },
        radius: 4,
        style: 'solid',
        width: 1,
      },
      extra_offers: [
        {
          title: 'Free Gift',
          type: 'free_gift',
          quantity: 1,
          always_visible: true,
          image_url: 'https://example.com/gift1.jpg',
          image_size: 50,
          image_border: {
            color: { r: 150, g: 150, b: 150, a: 1 },
            radius: 2,
            style: 'solid',
            width: 1,
          },
          variant_id: 'variant_123',
        },
      ],
    },
  ],

  product_conditions: [
    {
      type: 'products_by_tag',
      operator: 'include_all',
      products_tags: ['summer', 'hot'],
      products_types: [],
      products_vendors: [],
      products_collections: [],
    },
  ],

  customer_conditions: [
    {
      type: 'customers_by_login_status',
      operator: '',
      login_status: 'logged_in',
      customer_tags: [],
      customer_countries: [],
      customer_min_amount_spent: 0,
      customer_max_amount_spent: 0,
      customer_min_order_count: 0,
      customer_max_order_count: 0,
    },
    {
      type: 'customers_by_country',
      operator: 'is',
      login_status: '',
      customer_tags: [],
      customer_countries: ['US', 'CA'],
      customer_min_amount_spent: 0,
      customer_max_amount_spent: 0,
      customer_min_order_count: 0,
      customer_max_order_count: 0,
    },
  ],
};

export const TYPE_TITLE_MAP = {
  'quantity-breaks': 'Create quantity breaks',
  'volume-discount': 'Create volume discounts',
  'quantity-discount': 'Create quantity discounts',
  'boosters-discount': 'Create boosters',
  'bundle-discount': 'Create bundle offer',
};

export const FIELDS_BLOCK_TITLE = [
  {
    name: 'general.title',
    showKey: 'show_title',
    placeholder: 'Enter block title',
    label: 'Title',
  },
  {
    name: 'general.subtitle',
    showKey: 'show_subtitle',
    placeholder: 'Enter block subtitle',
    label: 'Subtitle',
  },
];

export const TABS_OFFER_DETAIL = [
  {
    id: 'offer-block',
    content: 'Block',
    panelID: 'offer-content',
  },
  {
    id: 'general-settings',
    content: 'General',
    panelID: 'general-settings-content',
  },
  {
    id: 'design',
    content: 'Design',
    panelID: 'design-content',
  },
  {
    id: 'advanced-settings',
    content: 'Advanced',
    panelID: 'advanced-settings-content',
  },
];

export const DISCOUNT_VALUES = {
  NONE: 'no_discount',
  FIXED_DISCOUNT: 'fixed_discount',
  FIXED_PRICE: 'fixed_product_price',
  PERCENTAGE: 'percentage_discount',
};
export const DISCOUNT_OPTIONS = [
  DISCOUNT_VALUES.NONE,
  DISCOUNT_VALUES.FIXED_DISCOUNT,
  DISCOUNT_VALUES.PERCENTAGE,
  DISCOUNT_VALUES.FIXED_PRICE,
];

