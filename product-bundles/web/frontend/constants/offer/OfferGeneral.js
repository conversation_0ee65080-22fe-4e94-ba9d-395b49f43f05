import {
  ProductIcon,
  CollectionIcon,
  PersonIcon,
  HashtagDecimalIcon,
  PersonLockIcon,
  FlagIcon,
  OrderDraftIcon,
  CashDollarIcon,
} from '@shopify/polaris-icons';

export const ACTIVE_OPTIONS = [
  { label: 'Public', value: 'true' },
  { label: 'Draft', value: 'false' },
];

export const PRODUCT_OPTION_VALUE = {
  ALL: 'all_products',
  SPECIFIC: 'specific_products',
  CUSTOM: 'custom_segment',
};
export const PRODUCT_OPTIONS = [
  { id: 'products_all', label: 'All Product', value: PRODUCT_OPTION_VALUE.ALL },
  { id: 'products_specific_product', label: 'Specific Product', value: PRODUCT_OPTION_VALUE.SPECIFIC },
  { id: 'products_custom_segment', label: 'Custom Segment', value: PRODUCT_OPTION_VALUE.CUSTOM },
];

export const CUSTOMER_OPTION_VALUE = {
  ALL: 'all_customers',
  CUSTOM: 'custom_segment',
};
export const CUSTOMER_OPTIONS = [
  { id: 'customers_all', label: 'All Customer', value: CUSTOMER_OPTION_VALUE.ALL },
  { id: 'customers_custom_segment', label: 'Custom Segment', value: CUSTOMER_OPTION_VALUE.CUSTOM },
];

export const POSITION_OPTION_VALUE = {
  ABOVE: 'above_form_actions',
  BELOW: 'below_form_actions',
  CUSTOM: 'custom_position',
};
export const POSITION_OPTIONS = [
  { id: 'position_above_form_actions', label: 'Above Form Actions', value: POSITION_OPTION_VALUE.ABOVE },
  { id: 'position_below_form_actions', label: 'Below Form Actions', value: POSITION_OPTION_VALUE.BELOW },
  { id: 'position_custom_position', label: 'Custom Position', value: POSITION_OPTION_VALUE.CUSTOM },
];

export const CUSTOM_PRODUCT_VALUES = {
  TAG: {
    TYPE: 'products_by_tag',
    FIELD: 'products_tags',
  },
  TYPE: {
    TYPE: 'products_by_type',
    FIELD: 'products_types',
  },
  VENDOR: {
    TYPE: 'products_by_vendor',
    FIELD: 'products_vendors',
  },
  COLLECTION: {
    TYPE: 'products_by_collection',
    FIELD: 'products_collections',
  },
};
export const CUSTOM_PRODUCT_ITEMS = [
  {
    value: CUSTOM_PRODUCT_VALUES.TAG.TYPE,
    content: 'Product by tag',
    icon: ProductIcon,
    helpText: 'Select products based on specific tags (e.g., "sale", "new")',
  },
  {
    value: CUSTOM_PRODUCT_VALUES.TYPE.TYPE,
    content: 'Products by type',
    icon: HashtagDecimalIcon,
    helpText: 'Filter products according to their product type',
  },
  {
    value: CUSTOM_PRODUCT_VALUES.VENDOR.TYPE,
    content: 'Products by vendor',
    icon: PersonIcon,
    helpText: 'Choose products from specific vendors or brands',
  },
  {
    value: CUSTOM_PRODUCT_VALUES.COLLECTION.TYPE,
    content: 'Products by collection',
    icon: CollectionIcon,
    helpText: 'Target products that belong to specific collections',
  },
];

export const CUSTOM_CUSTOMER_VALUES = {
  LOGIN: {
    TYPE: 'customers_by_login_status',
    FIELD: 'login_status',
  },
  TAG: {
    TYPE: 'customers_by_tag',
    FIELD: 'customer_tags',
  },
  COUNTRY: {
    TYPE: 'customers_by_country',
    FIELD: 'customer_countries',
  },
  AMOUNT: {
    TYPE: 'customers_by_amount_spent',
    MIN: 'customer_min_amount_spent',
    MAX: 'customer_max_amount_spent',
  },
  ORDER_COUNT: {
    TYPE: 'customers_by_order_count',
    MIN: 'customer_min_order_count',
    MAX: 'customer_max_order_count',
  },
};
export const CUSTOM_CUSTOMER_ITEMS = [
  {
    value: CUSTOM_CUSTOMER_VALUES.LOGIN.TYPE,
    content: 'Login status',
    icon: PersonLockIcon,
    helpText: 'Target customers based on whether they are logged in or not',
  },
  {
    value: CUSTOM_CUSTOMER_VALUES.TAG.TYPE,
    content: 'Customer tags',
    icon: ProductIcon,
    helpText: 'Filter customers using custom tags (e.g., "VIP", "wholesale")',
  },
  {
    value: CUSTOM_CUSTOMER_VALUES.COUNTRY.TYPE,
    content: 'Country',
    icon: FlagIcon,
    helpText: 'Filter customers based on their country of residence',
  },
  {
    value: CUSTOM_CUSTOMER_VALUES.AMOUNT.TYPE,
    content: 'Total amount spent',
    icon: CashDollarIcon,
    helpText: 'Target customers who have spent a certain total amount',
  },
  {
    value: CUSTOM_CUSTOMER_VALUES.ORDER_COUNT.TYPE,
    content: 'Number of orders',
    icon: OrderDraftIcon,
    helpText: 'Target customers based on how many orders they’ve placed',
  },
];

export const OPERATOR_OPTIONS_TAGS = [
  { label: 'Include all', value: 'include_all' },
  { label: 'Include any', value: 'include_any' },
  { label: 'Exclude all', value: 'exclude_all' },
  { label: 'Exclude any', value: 'exclude_any' },
];

export const OPERATOR_OPTIONS_TYPE_VENDOR = [
  { label: 'Is', value: 'is' },
  { label: 'Is not', value: 'is_not' },
];

export const LOGIN_STATUS_OPTIONS = [
  { label: 'Logged in', value: 'logged_in' },
  { label: 'Logged out', value: 'logged_out' },
];

export const POSITION_SELECTOR_OPTIONS = [
  { label: 'Before begin', value: 'beforebegin' },
  { label: 'After begin', value: 'afterbegin' },
  { label: 'Before end', value: 'beforeend' },
  { label: 'After end', value: 'afterend' },
];

