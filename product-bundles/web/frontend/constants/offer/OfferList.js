import { DuplicateIcon, DeleteIcon, UploadIcon, ImportIcon } from '@shopify/polaris-icons';

export const ITEM_STRINGS = ['All', 'Product Page'];

export const MAX_PAGE_ITEM = 10;

export const RESOURCE_NAME = {
  singular: 'offer',
  plural: 'offers',
};

export const ACTION_LIST_PUBLISH = [
  { content: 'Publish', icon: UploadIcon },
  { content: 'Duplicate', icon: DuplicateIcon },
  {
    content: 'Delete',
    icon: DeleteIcon,
    destructive: true,
  },
];

export const ACTION_LIST_UNPUBLISH = [
  { content: 'Unpublish', icon: ImportIcon },
  { content: 'Duplicate', icon: DuplicateIcon },
  {
    content: 'Delete',
    icon: DeleteIcon,
    destructive: true,
  },
];

export const HEADING_TABLE_OFFER = [
  { title: 'Name' },
  { title: 'Impressions', alignment: 'end' },
  { title: 'Revenue', alignment: 'end' },
  { title: 'Status', alignment: 'end' },
  { title: '', alignment: 'center' },
];

export const OFFER_STATUS = {
  0: { key: 'DRAFT', title: 'Draft', tone: 'info' },
  1: { key: 'PUBLISH', title: 'Publish', tone: 'success' },
};

