import { createContext, useCallback, useEffect, useMemo, useState } from 'react';
import { AppDataApi, MerchantConfigApi } from '../apis';
import { getShopToken, isAdmin } from '../utils/auth';
import _ from 'lodash';

export const AppDataContext = createContext({
  isMerchant: false,
  isLoading: false,
  isTester: false,
  shop: {},
  locales: [],
  warning: {},
  limitation: {},
  merchantConfig: {},
  shared: {},
  setShared: () => {},
  refetchData: async () => {},
});

export const AppDataProvider = ({ children }) => {
  // SHARED DATA
  const [shop, setShop] = useState({});
  const [limitation, setLimitation] = useState({});
  const [warning, setWarning] = useState({});
  const [locales, setLocales] = useState([]);
  const [merchantConfig, setMerchantConfig] = useState({});

  const [isLoading, setIsLoading] = useState(false);
  const [shared, setShared] = useState({});

  const isMerchant = window.location.ancestorOrigins?.[0]?.includes('https://admin.shopify.com');

  const isTester = useMemo(() => {
    const email = shop?.shopify_data?.email;

    if (!email) return false;

    const testers = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];

    return email.includes('@simicart') || testers.includes(email);
    //
  }, [shop?.shopify_data?.email]);

  // REFETCH FUNCTIONS
  const refetchShop = async () => {
    return AppDataApi.getShop()
      .then((shop) => {
        setShop(shop?.data);
        setLimitation(JSON.parse(shop?.data?.app_data?.feature_limitation || '{}'));
        return shop?.data;
      })
      .catch((err) => console.error(err));
  };

  const refetchWarning = async () => {
    return AppDataApi.getWarning()
      .then((warning) => {
        setWarning(warning?.data);
        return warning?.data;
      })
      .catch((err) => console.error(err));
  };

  const refetchLocales = async () => {
    return AppDataApi.getLocales()
      .then((locales) => {
        setLocales(locales?.data);
        return locales?.data;
      })
      .catch((err) => console.error(err));
  };

  const refetchMerchantConfig = async () => {
    return MerchantConfigApi.get()
      .then((config) => {
        setMerchantConfig(config?.data);
        return config?.data;
      })
      .catch((err) => console.error(err));
  };

  const refetchData = useCallback(async ({ shop, warning, locales, merchantConfig }) => {
    if (!getShopToken()) return;

    setIsLoading(true);

    const result = {};
    const promises = [];
    if (shop) promises.push(refetchShop().then((data) => (result.shop = data)));
    if (warning) promises.push(refetchWarning().then((data) => (result.warning = data)));
    if (locales) promises.push(refetchLocales().then((data) => (result.locales = data)));
    if (merchantConfig) promises.push(refetchMerchantConfig().then((data) => (result.merchantConfig = data)));
    await Promise.all(promises);

    setIsLoading(false);

    return result;
  }, []);

  useEffect(() => {
    if (!isAdmin()) return;
    refetchData({
      shop: true,
      warning: true,
      locales: true,
      merchantConfig: true,
    });
  }, [refetchData]);

  return (
    <AppDataContext.Provider
      value={{
        isMerchant,
        isLoading,
        isTester,
        shop,
        warning,
        locales,
        limitation,
        merchantConfig,
        shared,
        setShared,
        refetchData,
      }}
    >
      {children}
    </AppDataContext.Provider>
  );
};

