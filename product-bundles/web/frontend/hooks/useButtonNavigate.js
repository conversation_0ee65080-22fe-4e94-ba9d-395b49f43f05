import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

export const useButtonNavigate = () => {
  const navigate = useNavigate();

  const onNavigate = useCallback(
    (url) => {
      // real navigate func
      return (event) => {
        if (event.ctrlKey || event.metaKey) {
          window.open(url, '_blank');
        } else {
          navigate(url);
        }
      };
    },
    [navigate]
  );

  return { navigate: onNavigate };
};
