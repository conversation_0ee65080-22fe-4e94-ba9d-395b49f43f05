import _ from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { createErrorSchema, formHasError } from '../utils/form';

/**
 * Custom hook to manage form data, validation, and error handling.
 *
 * @param {Object} data - Initial form data to be managed.
 * @param {Object} validationSchema - Object containing validation functions, with keys corresponding to form data fields.
 *
 * @returns {Object} formData - The current state of the form data.
 * @returns {Object} errors - The current state of form validation errors.
 * @returns {boolean} hasErro<PERSON> - <PERSON>olean indicating whether any validation errors exist in the form.
 * @returns {Function} runValidations - Function to validate the entire form against the validation schema.
 * @returns {Function} handleFieldChange - Function to handle updates to individual fields in the form and trigger validation for the updated field.
 */
export const useFormHandler = (data, validationSchema = {}) => {
  // FORM STATE
  const [formData, setFormData] = useState(null);
  const [cleanFormData, setCleanFormData] = useState(null);
  const [hasError, setHasError] = useState(false);
  const [errors, setErrors] = useState({});

  // INIT ERROR SCHEMA
  useEffect(() => {
    if (!data) return;
    console.log('data', data);
    setFormData(data);
    setCleanFormData(data);
    const errorSchema = createErrorSchema({}, data);
    setErrors(errorSchema);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(data)]);

  // FORM TOUCHED OR NOT
  const hasDirt = useMemo(() => JSON.stringify(formData) !== JSON.stringify(cleanFormData), [cleanFormData, formData]);

  /**
   * Function to validate the entire form object.
   * Iterates through each field in the form data, applying the corresponding validation function from the validation schema.
   * Updates the `errors` state with any validation errors and sets the `hasError` state if any errors are found.
   */
  const runValidations = useCallback(
    (data = formData) => {
      const newErrors = _.cloneDeep(errors);

      const validateFields = (currentData, currentPath = '', type = 'object') => {
        // Recursive loop through all fields of form data
        _.forEach(currentData, (value, key) => {
          let fullPath;
          if (type === 'array') fullPath = currentPath ? `${currentPath}[${key}]` : key;
          else fullPath = currentPath ? `${currentPath}.${key}` : key;

          // Dig array deeper
          if (_.isArray(value)) {
            validateFields(value, fullPath, 'array');
          }
          // Dig object deeper
          else if (_.isPlainObject(value)) {
            validateFields(value, fullPath);
          }
          // Validate leaf nodes
          else {
            const validate = _.get(validationSchema, fullPath.replace(/\[\d+\]/g, ''));
            if (validate) {
              const error = validate(value);
              if (error) _.set(newErrors, fullPath, error);
              else _.set(newErrors, fullPath, false);
            }
          }
        });
      };

      validateFields(data);

      const newHasError = formHasError(newErrors);

      setErrors(newErrors);
      setHasError(newHasError);

      return { newErrors, newHasError };
      //
    },
    [errors, formData, validationSchema]
  );

  const cleanupFormData = useCallback(() => {
    setFormData(cleanFormData);
    runValidations(cleanFormData);
  }, [cleanFormData, runValidations]);

  /**
   * Handles changes to individual fields in the form and updates the form data state accordingly.
   * This function updates the specified field in the form data and triggers validation for that field.
   * It also updates any related fields based on the `affected` parameter.
   * If the `path` parameter is not provided, the entire form data object is replaced with the new value.
   *
   * @param {any} value - The new value to set for the form field.
   * @param {string} [path] - The lodash-style path to the specific field in the form data to update.
   * @param {Array<{value: any, path: string}>} [affected] - Optional array of objects, each specifying a field (`path`)
   *    that should be updated with the same value (`value`) when the current field changes.
   */
  const handleFieldChange = useCallback(
    (value, path, affected = []) => {
      // Skip path if want to update the whole object
      if (!path) {
        setFormData(value);
        return;
      }

      // Fix true/false string values
      if (value === 'true') value = true;
      else if (value === 'false') value = false;

      // Validate the specific field
      const validate = validationSchema[path?.replace(/\[\d+\]/g, '')] || (() => false);
      const error = validate(value);
      const updatedErrors = _.cloneDeep(errors);
      const updatedFormData = _.cloneDeep(formData);
      _.set(updatedErrors, path, error);
      _.set(updatedFormData, path, value);

      if (Array.isArray(affected)) affected.forEach(({ value, path }) => _.set(updatedFormData, path, value));

      const newErrors = createErrorSchema(updatedErrors, updatedFormData);
      const newHasError = formHasError(newErrors);

      // Set new state
      setErrors(newErrors);
      setHasError(newHasError);
      setFormData(updatedFormData);
    },
    [errors, formData, validationSchema]
  );

  return { formData, errors, hasError, hasDirt, cleanupFormData, runValidations, handleFieldChange };
};

