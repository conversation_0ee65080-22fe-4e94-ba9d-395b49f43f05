import { useState } from 'react';

export const useHidden = (key) => {
  const storedHidden = localStorage.getItem(key) === 'true' || sessionStorage.getItem(key) === 'true';

  const [isHidden, setIsHidden] = useState(storedHidden);

  const hide = () => {
    setIsHidden(true);
    sessionStorage.setItem(key, true);
  };

  const hideForever = () => {
    setIsHidden(true);
    localStorage.setItem(key, true);
  };

  const unhide = () => {
    setIsHidden(false);
    localStorage.removeItem(key);
    sessionStorage.removeItem(key);
  };

  return { isHidden, hide, hideForever, unhide };
};

