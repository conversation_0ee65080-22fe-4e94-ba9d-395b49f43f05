import { Title<PERSON><PERSON>, Modal as ModalAppBridge } from '@shopify/app-bridge-react';
import { Box, Button, InlineStack, Modal as ModalPolaris } from '@shopify/polaris';
import { useCallback, useMemo, useState } from 'react';
import { isAdmin } from '../utils/auth';

export const useModal = ({ type = 'app-bridge' }) => {
  const [modalOpen, setModalOpen] = useState(false);

  const showModal = useCallback(() => setModalOpen(true), []);
  const hideModal = useCallback(() => setModalOpen(false), []);
  const Modal = useMemo(() => (type === 'polaris' || isAdmin() ? CustomModalPolaris : CustomModalAppBridge), [type]);

  return { modalOpen, showModal, hideModal, Modal };
};

function CustomModalPolaris({ open, title, size, primaryAction, secondaryActions, children, onClose = () => {} }) {
  return (
    <ModalPolaris
      open={open}
      title={title}
      size={size}
      primaryAction={primaryAction}
      secondaryActions={secondaryActions}
      onClose={onClose}
    >
      <ModalPolaris.Section>{children}</ModalPolaris.Section>
    </ModalPolaris>
  );
}

function CustomModalAppBridge({
  open,
  title,
  size,
  primaryAction,
  secondaryActions,
  children,
  buttonAlign = 'end',
  onClose = () => {},
}) {
  return (
    <ModalAppBridge open={open} variant={size} onHide={onClose}>
      {/* title */}
      <TitleBar title={title} />

      {/* content */}
      <Box padding="400">{children}</Box>

      {/* action */}
      <div style={{ position: 'sticky', bottom: 0, backgroundColor: 'white', zIndex: 100 }}>
        <Box padding="400" borderColor="border" borderBlockStartWidth="025">
          <InlineStack gap="200" align={buttonAlign} blockAlign="end">
            {secondaryActions?.length &&
              secondaryActions.map((action, key) => (
                <Button
                  key={key}
                  size="micro"
                  loading={action.loading}
                  disabled={action.disabled}
                  onClick={action.onAction || (() => {})}
                >
                  <div style={{ padding: '.125rem .25rem', fontSize: '.8rem' }}>{action.content}</div>
                </Button>
              ))}
            {primaryAction && (
              <Button
                size="micro"
                variant="primary"
                tone={primaryAction.destructive ? 'critical' : 'default'}
                loading={primaryAction.loading}
                disabled={primaryAction.disabled}
                onClick={primaryAction.onAction || (() => {})}
              >
                <div style={{ padding: '.125rem .25rem', fontSize: '.8rem', fontWeight: 650 }}>
                  {primaryAction.content}
                </div>
              </Button>
            )}
          </InlineStack>
        </Box>
      </div>
    </ModalAppBridge>
  );
}

