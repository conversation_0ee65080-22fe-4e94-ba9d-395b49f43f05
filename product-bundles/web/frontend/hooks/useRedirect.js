import { Redirect } from '@shopify/app-bridge/actions';
import { createApp } from '@shopify/app-bridge';
import { useMemo, useRef } from 'react';
import { isAdmin } from '../utils/auth';

export const useRedirect = () => {
  const host = useMemo(() => {
    return decodeURIComponent(
      new URLSearchParams(window.location.search).get('host') || window.__SHOPIFY_DEV_HOST || ''
    );
  }, []);

  const appRef = useRef(null);

  if (!appRef.current && !isAdmin()) {
    appRef.current = createApp({ host, apiKey: process.env.SHOPIFY_API_KEY });
  }

  const redirect = useMemo(() => {
    if (isAdmin()) return { dispatch: () => {} };
    return Redirect.create(appRef.current);
  }, []);

  return (url, remote = false) => {
    const target = remote ? Redirect.Action.REMOTE : Redirect.Action.APP;
    redirect.dispatch(target, url);
  };
};

