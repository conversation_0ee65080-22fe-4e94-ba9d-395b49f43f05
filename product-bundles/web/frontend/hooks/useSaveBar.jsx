/* eslint-disable react/no-unknown-property */
import { ContextualSaveBar as SaveBarPolaris, Frame } from '@shopify/polaris';
import { SaveBar as SaveBarAppBridge } from '@shopify/app-bridge-react';
import { isAdmin } from '../utils/auth';
import { useCallback, useMemo, useState } from 'react';

export const useSaveBar = () => {
  const [open, setOpen] = useState(false);

  const show = useCallback(() => setOpen(true), []);
  const hide = useCallback(() => setOpen(false), []);
  const SaveBar = useMemo(() => (isAdmin() ? CustomSaveBarPolaris : CustomSaveBarAppBridge), []);

  return { open, show, hide, SaveBar };
};

function CustomSaveBarPolaris({ open, saveAction = {}, discardAction = {} }) {
  if (!open) return null;

  return (
    <Frame>
      <SaveBarPolaris
        alignContentFlush
        message="Unsaved changes"
        saveAction={saveAction}
        discardAction={discardAction}
      />
    </Frame>
  );
}

function CustomSaveBarAppBridge({ open, saveAction = {}, discardAction = {} }) {
  return (
    <SaveBarAppBridge open={open}>
      <button
        variant="primary"
        loading={saveAction.loading ? '' : undefined}
        disabled={saveAction.disabled}
        onClick={saveAction.onAction}
      />
      <button
        loading={discardAction.loading ? '' : undefined}
        disabled={discardAction.disabled}
        onClick={discardAction.onAction}
      />
    </SaveBarAppBridge>
  );
}
