import { useCallback, useContext } from 'react';
import { AppDataContext } from '../context/AppDataContext';

export const useSupport = () => {
  const { shop } = useContext(AppDataContext);
  const name = shop?.shopify_data?.shop_owner;
  // const mail = shop?.shopify_data?.email;

  const startSupport = useCallback(
    (message) => {
      const chatButton = document.querySelector('.zsiq_floatmain.show');
      if (chatButton) chatButton.click();
      else window.open('https://tapita0.zohodesk.com/portal/en/newticket', '_blank');

      const zohoDocument = window.$ZSIQUtil?.getIframe()?.document;
      const inputName = zohoDocument?.getElementById('visname');
      // const inputMail = zohoDocument?.getElementById('visemail');
      const inputMess = zohoDocument?.getElementById('msgarea');

      if (inputName) inputName.value = name;
      // if (inputMail) inputMail.value = mail;
      if (inputMess) inputMess.value = message;
    },
    [name]
  );

  return { startSupport };
};
