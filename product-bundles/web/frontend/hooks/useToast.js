import { toast as sonnerToast } from 'sonner';
import { isAdmin } from '../utils/auth';
import { useMemo } from 'react';

export const useToast = () => {
  const toast = useMemo(
    () =>
      isAdmin()
        ? // admin toast
          {
            message: (content, config = {}) => sonnerToast(content, { duration: 3000, ...config }),
            error: (content, config = {}) => sonnerToast.error(content, { duration: 3000, ...config }),
          }
        : // shopify toast
          {
            message: (content, config = {}) => window.shopify.toast.show(content, { duration: 3000, ...config }),
            error: (content, config = {}) =>
              window.shopify.toast.show(content, { duration: 3000, isError: true, ...config }),
          },
    []
  );

  return { toast };
};
