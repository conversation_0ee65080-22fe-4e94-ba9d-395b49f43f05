{"name": "shopify-frontend-template-react", "version": "1.0.0", "private": true, "license": "UNLICENSED", "scripts": {"build": "vite build", "dev": "vite", "coverage": "vitest run --coverage", "lint": "eslint . --ext .js,.jsx"}, "type": "module", "engines": {"node": ">= 12.16"}, "stylelint": {"extends": "@shopify/stylelint-polaris"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@formatjs/intl-locale": "^3.3.2", "@formatjs/intl-localematcher": "^0.4.0", "@formatjs/intl-pluralrules": "^5.2.4", "@radix-ui/react-hover-card": "^1.1.1", "@shopify/app-bridge": "^3.7.10", "@shopify/app-bridge-react": "^4.1.5", "@shopify/i18next-shopify": "^0.2.9", "@shopify/polaris": "^13.9.0", "@vitejs/plugin-react": "4.2.1", "axios": "^1.7.4", "color.js": "^1.2.0", "copy-to-clipboard": "^3.3.3", "i18next": "^23.1.0", "i18next-resources-to-backend": "^1.1.4", "jodit-react": "^4.1.2", "lodash": "^4.17.21", "papaparse": "^5.4.1", "rc-switch": "^4.1.0", "react": "^18.2.0", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-hook-form": "^7.58.1", "react-i18next": "^13.0.0", "react-query": "^3.34.19", "react-router-dom": "^6.3.0", "sass": "^1.77.8", "short-unique-id": "^5.2.0", "sonner": "^1.5.0", "vite": "^4.3.9"}, "devDependencies": {"@babel/core": "^7.25.2", "@eslint/js": "^9.9.0", "eslint": "8", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "globals": "^15.9.0", "history": "^5.3.0", "jsdom": "^24.0.0", "prettier": "^3.3.3", "stylelint": "^15.6.1", "vi-fetch": "^0.6.1"}}