import React, { useContext } from 'react';
import { BlockStack, Box, Card, Divider, InlineStack, Page, Tabs } from '@shopify/polaris';
import { AppDataContext } from '../../context/AppDataContext';
import { useNavigate } from 'react-router-dom';
import { nav } from '../../utils/mixed';
import { Toaster } from 'sonner';
import './AdminWrapper.scss';

export const AdminWrapper = ({ children, isPortal = true }) => {
  return (
    <>
      {isPortal && <ShopNav />}
      {isPortal && <ShopInfo />}
      {children}
      <Toaster richColors position="bottom-center" />
    </>
  );
};

// navigation
function ShopNav() {
  const tabs = [
    { id: '/', content: 'Home' },
    { id: '/setting', content: 'Settings' },
    { id: '/pricing', content: 'Pricing' },
  ];

  const navigate = useNavigate();
  let path = window.location.pathname.replace('/admin/portal', '');
  if (!path) path += '/';
  const selected = tabs?.findIndex((t) => t.id === path || (t.id !== '/' && path.includes(t.id)));

  const handleSelect = (value) => {
    const path = tabs?.[value]?.id;
    navigate(nav(path));
  };

  return (
    <div className="shop-navigation">
      <Page>
        <Box background="bg-fill" borderRadius="full" shadow="400" borderColor="border" borderWidth="025">
          <Tabs tabs={tabs} selected={selected} onSelect={handleSelect} fitted />
        </Box>
      </Page>
    </div>
  );
}

// shop info
function ShopInfo() {
  const { shop } = useContext(AppDataContext);

  const SHOP_STATUS = {
    0: 'ENABLED',
    1: 'INSTALLED',
    2: 'UNINSTALLED',
    3: 'CLOSED STORE',
    4: 'API 404',
  };

  return (
    <Page>
      <Card>
        <BlockStack gap="300">
          <div style={{ fontWeight: 900, textAlign: 'center', fontSize: '1.2rem' }}>STORE INFO</div>

          <Divider />

          <InlineStack gap="200">
            <span>
              <b>Shop ID:</b> {shop?._id}
            </span>

            <span>|</span>

            <span>
              <b>Shop Name</b>: {shop?.shopify_data?.name}
            </span>

            <span>|</span>

            <span>
              <b>Status</b>: {SHOP_STATUS[shop?.shop_status | 0]}
            </span>

            <span>|</span>

            <span>
              <b>Owner:</b> {shop?.shopify_data?.shop_owner}
            </span>

            <span>|</span>

            <span>
              <b>Email:</b> {shop?.shopify_data?.email}
            </span>

            <span>|</span>

            <span>
              <b>Regular Domain:</b> {shop?.shopify_data?.domain}
            </span>

            <span>|</span>

            <span>
              <b>Myshopify Domain:</b> {shop?.shopify_data?.myshopify_domain}
            </span>

            <span>|</span>

            <span>
              <b>Shopify Plan:</b> {shop?.shopify_data?.plan_display_name?.toUpperCase()}
            </span>

            <span>|</span>

            <span>
              <b>Tapita Plan:</b> {shop?.app_data?.plan_display_name?.toUpperCase()}
            </span>

            <span>|</span>

            <span>
              <b>Theme Name:</b> {shop?.app_data?.theme_name?.toUpperCase()}
            </span>

            <span>|</span>

            <span>
              <b>Theme Supported:</b> {shop?.app_data?.theme_supported ? 'YES' : 'NO'}
            </span>

            <span>|</span>

            <span>
              <b>App Enabled:</b> {shop?.app_data?.app_enabled ? 'YES' : 'NO'}
            </span>

            <span>|</span>

            <span>
              <b>Password:</b> {shop?.app_data?.password}
            </span>
          </InlineStack>
        </BlockStack>
      </Card>
    </Page>
  );
}

