import { ActionList, BlockStack, Button, ButtonGroup, InlineCode, InlineStack, Popover, Text } from '@shopify/polaris';
import { ChevronDownIcon, ExportIcon } from '@shopify/polaris-icons';
import { useCallback, useState } from 'react';
import { ReportApi } from '../../../apis';
import { useModal, useToast } from '../../../hooks';
import { DateRangePicker } from '../../../components/ui';

export const ButtonExportReport = () => {
  const [activePopover, setActivePopover] = useState(false);
  const togglePopover = useCallback(() => setActivePopover(!activePopover), [activePopover]);

  const { toast } = useToast();
  const { modalOpen, showModal, hideModal, Modal } = useModal({});

  const [dateRange, setDateRange] = useState({});

  const [isExporting, setIsExporting] = useState(false);

  const formatDate = (date) => {
    return new Date(date.getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  };

  // export app statistics to a csv file
  const handleExportAppStatistics = useCallback(async () => {
    togglePopover();
    setIsExporting(true);

    const appStatistics = await ReportApi.getAppStatistics();

    let csvContent = appStatistics.data.map(({ label, value }) => `"${label}","${value}"`).join('\n');
    csvContent = `"Label","Value"\n${csvContent}`;
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');

    a.href = url;
    a.download = `app-statistics-report-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    toast.message('Report exported');

    setIsExporting(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [togglePopover]);

  // export merchant actions to a csv file
  const handleExportMerchantActions = useCallback(async () => {
    togglePopover();

    setIsExporting(true);

    const merchantActions = await ReportApi.getMerchantActions({
      start: formatDate(dateRange.start || new Date()),
      end: formatDate(dateRange.end || new Date()),
    });

    let csvContent = merchantActions.data
      .map(
        ({
          shop_name,
          status,
          action_logs,
          myshopify_domain,
          installed_date,
          uninstalled_date,
          uninstall_duration,
          shopify_plan,
          grouptify_plan,
          rating_point,
          theme_supported,
          app_enabled,
          swatch_on_pdp_enabled,
          swatch_on_cdp_enabled,
          group_count,
          product_count,
          single_group_count,
          multiple_group_count,
          automation_group_count,
          active_group_count,
          active_product_count,
        }) =>
          `"${shop_name}","${status}","${action_logs}","${myshopify_domain}","${installed_date}","${uninstalled_date}","${uninstall_duration}","${shopify_plan}","${grouptify_plan}","${rating_point}","${theme_supported}","${app_enabled}","${swatch_on_pdp_enabled}","${swatch_on_cdp_enabled}","${group_count}","${product_count}","${single_group_count}","${multiple_group_count}","${automation_group_count}","${active_group_count}","${active_product_count}"`
      )
      .join('\n');
    csvContent = `"Shop Name","Status","Action Logs","My Shopify Domain","Installed Date","Uninstalled Date","Uninstall Duration","Shopify Plan","Grouptify Plan","Rating Point","Theme Supported","App Enabled","Swatch on PDP Enabled","Swatch on CDP Enabled","Group Count","Product Count","Single Option Group Count","Multiple Options Group Count","Automation Group Count","Active Group Count","Product In Active Group Count"\n${csvContent}`;
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');

    a.href = url;
    a.download = `merchant-actions-report-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();

    toast.message('Report exported');
    setIsExporting(false);
    setDateRange({});
    hideModal();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [togglePopover, dateRange.start, dateRange.end, hideModal]);

  return (
    <>
      <ButtonGroup variant="segmented">
        <Button icon={ExportIcon} loading={isExporting} size={isExporting ? 'large' : 'medium'} onClick={togglePopover}>
          Export report
        </Button>
        <Popover
          active={activePopover}
          preferredAlignment="right"
          preferredPosition="below"
          activator={
            <Button
              onClick={togglePopover}
              icon={ChevronDownIcon}
              loading={isExporting}
              size={isExporting ? 'large' : 'medium'}
            />
          }
          autofocusTarget="first-node"
          onClose={togglePopover}
        >
          <ActionList
            actionRole="menuitem"
            items={[
              {
                content: 'App Statistics',
                onAction: handleExportAppStatistics,
              },
              {
                content: 'Merchant Actions',
                onAction: showModal,
              },
            ]}
          />
        </Popover>
      </ButtonGroup>
      <Modal
        open={modalOpen}
        onClose={hideModal}
        title="Export Merchant Actions"
        primaryAction={{
          content: 'Export',
          loading: isExporting,
          onAction: handleExportMerchantActions,
        }}
        secondaryActions={[
          {
            content: 'Cancel',
            disabled: isExporting,
            onAction: hideModal,
          },
        ]}
      >
        <BlockStack gap="300">
          <InlineStack align="space-between" blockAlign="center">
            <Text fontWeight="medium">Choose date range</Text>

            <DateRangePicker value={dateRange} onDateRangeSelect={({ start, end }) => setDateRange({ start, end })} />
          </InlineStack>

          <Text>
            You will export the merchant actions from{' '}
            <InlineCode>00:00:00 {formatDate(dateRange.start || new Date())}</InlineCode> to{' '}
            <InlineCode>23:59:59 {formatDate(dateRange.end || new Date())}</InlineCode>
          </Text>
        </BlockStack>
      </Modal>
    </>
  );
};

