import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card, InlineGrid, InlineStack, List, Text } from '@shopify/polaris';
import { ThemeIcon, PersonIcon, PersonLockIcon, StoreIcon, RefreshIcon, NoteIcon } from '@shopify/polaris-icons';
import { useMutation, useQuery } from 'react-query';
import { Shop<PERSON>pi, ActionLogApi } from '../../../apis';
import { useButtonNavigate, useModal, useToast } from '../../../hooks';
import { toHHMMSSDDMMYYYY } from '../../../utils/date';
import './SearchItem.scss';

export const SearchItem = ({ data }) => {
  const {
    isFetching: isCompetitorsGetting,
    data: competitorsData,
    refetch: refetchCompetitors,
  } = useQuery(`competitors-${data?._id}-repo`, () => ShopApi.getCompetitors(data?._id), { enabled: false });

  const SHOP_STATUS = {
    0: 'ENABLED',
    1: 'INSTALLED',
    2: 'UNINSTALLED',
    3: 'CLOSED STORE',
    4: 'API 404',
  };

  const renderData = [
    { label: 'Shop ID', value: data?._id },
    { label: 'Shop Name', value: data?.shopify_data?.name },
    { label: 'Shop Status', value: SHOP_STATUS?.[data?.shop_status || 0] },
    { label: 'Shop Owner', value: data?.shopify_data?.shop_owner },

    { label: 'Email 1', value: data?.shopify_data?.email },
    { label: 'Email 2', value: data?.shopify_data?.customer_email },
    {
      label: 'Address',
      value: [
        data?.shopify_data?.address1,
        data?.shopify_data?.city,
        data?.shopify_data?.province,
        data?.shopify_data?.country_name,
      ]
        ?.filter((a) => a)
        ?.join(', '),
    },
    { label: 'Currency', value: data?.shopify_data?.currency },

    { label: 'Regular Domain', value: data?.shopify_data?.domain },
    { label: 'Myshopify Domain', value: data?.shopify_data?.myshopify_domain },

    { label: 'Shopify Plan', value: `${data?.shopify_data?.plan_display_name} (${data?.shopify_data?.plan_name})` },
    { label: 'Tapita Plan', value: `${data?.app_data?.plan_display_name} (${data?.app_data?.plan_name})` },
    { label: 'Charge Name', value: data?.app_data?.charge_name },
    { label: 'Charge Status', value: data?.app_data?.charge_status?.toUpperCase() },
    { label: 'Payment Date', value: data?.app_data?.payment_date },

    { label: 'Theme Name', value: data?.app_data?.theme_name },
    { label: 'Theme Supported', value: data?.app_data?.theme_supported ? 'YES' : 'NO' },
    { label: 'Rating Point', value: data?.app_data?.rating_point },
    { label: 'Password', value: data?.app_data?.password },

    { label: 'Group Count (Total)', value: data?.app_data?.group_count },
    { label: 'Group Count (Manual - Single)', value: data?.app_data?.group_count_manual_single },
    { label: 'Group Count (Manual - Multiple)', value: data?.app_data?.group_count_manual_multiple },
    { label: 'Group Count (Automation - Single)', value: data?.app_data?.group_count_automation_single },
    { label: 'Group Count (Automation - Multiple)', value: data?.app_data?.group_count_automation_multiple },
    { label: 'Grouped Product Count', value: data?.app_data?.product_count },

    { label: 'App Enabled', value: data?.app_data?.app_enabled ? 'YES' : 'NO' },
    { label: 'App Touched', value: data?.app_data?.app_touched ? 'YES' : 'NO' },
    { label: 'Swatch On Product Page Enabled', value: data?.app_data?.swatch_on_pdp_enabled ? 'YES' : 'NO' },
    { label: 'Swatch On Collection Page Enabled', value: data?.app_data?.swatch_on_cdp_enabled ? 'YES' : 'NO' },

    {
      label: 'Installed Competitors',
      value:
        competitorsData?.data?.installedCompetitors?.length > 0
          ? competitorsData?.data?.installedCompetitors?.join(', ')
          : data?.app_data?.installed_competitors,
    },

    { label: 'Survey Status', value: data?.app_data?.survey_status },

    { label: 'Created Time', value: data?.createdAt },
    { label: 'Updated Time', value: data?.updatedAt },
  ];

  const { navigate } = useButtonNavigate();

  // Sync actions
  const { isLoading: isSyncingShopLimit, mutateAsync: syncShopLimit } = useMutation((data) => ShopApi.sync(data));

  // Log actions
  const {
    modalOpen: modalActionLogOpen,
    showModal: showModalActionLog,
    hideModal: hideModalActionLog,
    Modal: ModalActionLog,
  } = useModal({});

  const {
    isFetching: isLogsGetting,
    data: logsData,
    refetch: refetchLogs,
  } = useQuery(`action-logs-${data?._id}-repo`, () => ActionLogApi.search({ shop: data?._id }), { enabled: false });

  // Survey result
  const {
    modalOpen: modalSurveyResultOpen,
    showModal: showModalSurveyResult,
    hideModal: hideModalSurveyResult,
    Modal: ModalSurveyResult,
  } = useModal({});

  const surveyData = {
    survey_status: data?.app_data?.survey_status || 'pending',
    survey_response: data?.app_data?.survey_response || {},
    survey_result: data?.app_data?.survey_result || {},
  };

  const { toast } = useToast();

  const uninstalled = data?.shop_status !== 1;

  return (
    <div className="search-item">
      <Card>
        <InlineGrid columns="2" gap="400">
          <BlockStack>
            {renderData?.map((d, i) => (
              <div className="search-item__info" key={i}>
                <Text fontWeight="medium">{d.label}</Text>
                <Text>{d.value}</Text>
              </div>
            ))}
          </BlockStack>

          <BlockStack gap="300">
            <InlineStack blockAlign="start" gap="300">
              <Button
                icon={ThemeIcon}
                variant="primary"
                onClick={() => window.open(`/admin/portal?token=${data?.auth_token}`, '_blank')}
              >
                PORTAL PAGE
              </Button>

              <Button
                icon={PersonIcon}
                onClick={() =>
                  window.open(
                    `https://admin.shopify.com/store/${data?.shopify_data?.myshopify_domain?.split('.myshopify.com')?.[0]}`,
                    '_blank'
                  )
                }
              >
                ADMIN PAGE
              </Button>

              <Button icon={StoreIcon} onClick={() => window.open(`https://${data?.shopify_data?.domain}`, '_blank')}>
                STORE FRONT
              </Button>

              <Button icon={PersonLockIcon} onClick={navigate(`/admin/shop/${data?._id}/limit`)}>
                CUSTOM LIMIT
              </Button>

              <Button
                icon={RefreshIcon}
                loading={isSyncingShopLimit}
                disabled={uninstalled}
                onClick={async () => {
                  await syncShopLimit({ shop: data?._id });
                  toast.message('Shop limit synced');
                }}
              >
                SYNC LIMIT
              </Button>

              <Button
                icon={RefreshIcon}
                loading={isCompetitorsGetting}
                onClick={async () => {
                  const competitorsData = await refetchCompetitors();
                  toast.message(`${competitorsData?.data?.data?.installedCompetitors?.length} competitor(s) found`);
                }}
              >
                CHECK COMPETITORS
              </Button>

              <Button
                icon={NoteIcon}
                loading={isLogsGetting}
                onClick={async () => {
                  await refetchLogs();
                  showModalActionLog();
                }}
              >
                VIEW ACTION LOGS
              </Button>

              <Button
                icon={NoteIcon}
                disabled={!['completed', 'aborted'].includes(data?.app_data?.survey_status)}
                onClick={showModalSurveyResult}
              >
                VIEW SURVEY RESULTS
              </Button>
            </InlineStack>
          </BlockStack>
        </InlineGrid>
      </Card>

      <ModalActionLog
        open={modalActionLogOpen}
        title="Latest 20 Action Logs"
        onClose={hideModalActionLog}
        primaryAction={{ content: 'Close', onAction: hideModalActionLog }}
      >
        <BlockStack gap="300">
          {logsData?.data?.map((log) => (
            <Text key={log?._id}>
              {toHHMMSSDDMMYYYY(log?.createdAt)} - {log.message}
            </Text>
          ))}
        </BlockStack>
      </ModalActionLog>

      <ModalSurveyResult
        open={modalSurveyResultOpen}
        title="Survey Results"
        onClose={hideModalSurveyResult}
        primaryAction={{ content: 'Close', onAction: hideModalSurveyResult }}
      >
        <BlockStack gap="200">
          <Text>Survey Status: {surveyData?.survey_status?.toUpperCase()}</Text>
          <Text>Product Organization: {surveyData?.survey_response?.productOrganization}</Text>
          <Text>Business Expectations:</Text>
          {surveyData?.survey_response?.businessExpectations?.length > 0 && (
            <List>
              {surveyData?.survey_response?.businessExpectations?.map((expectation, i) => (
                <List.Item key={i}>{expectation}</List.Item>
              ))}
              {surveyData?.survey_response?.otherExpectation && (
                <List.Item key="other">
                  <Text>Other: {surveyData?.survey_response?.otherExpectation}</Text>
                </List.Item>
              )}
            </List>
          )}
          <Text>Suggested Feature: {surveyData?.survey_result?.feature}</Text>
          <Text>
            Scores For Combined Listings / Redesign: {surveyData?.survey_result?.scores?.combinedListingsScore} /{' '}
            {surveyData?.survey_result?.scores?.redesignScore}
          </Text>
        </BlockStack>
      </ModalSurveyResult>
    </div>
  );
};

