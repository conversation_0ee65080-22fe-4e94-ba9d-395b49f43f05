import { BlockStack, Button, InlineStack, Text } from '@shopify/polaris';
import { SearchItem } from './SearchItem';

export const SearchList = ({ data, handleNext = () => {}, handlePrev = () => {} }) => {
  const totalResults = data?.pageInfo?.totalResults;
  const totalPages = data?.pageInfo?.totalPages;
  const currentPage = data?.pageInfo?.currentPage;

  return (
    <BlockStack gap="400">
      <Text variant="headingLg" fontWeight="medium" alignment="center">
        {totalResults
          ? `Search result - ${totalResults} store(s) found - page ${currentPage} / ${totalPages}`
          : 'No stores found. Please modify your search and try again.'}
      </Text>

      <BlockStack gap="400">
        {data?.results?.map((r, i) => (
          <SearchItem data={r} key={i} />
        ))}
      </BlockStack>

      {data?.pageInfo?.totalPages > 1 && (
        <InlineStack gap="300" align="center" blockAlign="center">
          <Button size="large" disabled={!data?.pageInfo?.hasPrev} onClick={handlePrev}>
            Prev
          </Button>

          <Text fontWeight="medium">Page {data?.pageInfo?.currentPage}</Text>

          <Button size="large" disabled={!data?.pageInfo?.hasNext} onClick={handleNext}>
            Next
          </Button>
        </InlineStack>
      )}
    </BlockStack>
  );
};

