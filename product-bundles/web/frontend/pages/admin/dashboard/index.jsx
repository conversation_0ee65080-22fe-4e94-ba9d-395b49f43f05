import {
  <PERSON><PERSON><PERSON>ck,
  Box,
  Button,
  Card,
  Divider,
  Form,
  Grid,
  InlineStack,
  Layout,
  Page,
  Select,
  Text,
  TextField,
} from '@shopify/polaris';
import { useEffect, useMemo, useState } from 'react';
import {
  ToggleOffIcon,
  ToggleOnIcon,
  QuestionCircleIcon,
  NoteIcon,
  AppsIcon,
  DiscountIcon,
} from '@shopify/polaris-icons';
import { useMutation, useQuery } from 'react-query';
import { AppConfigApi, ShopApi } from '../../../apis';
import { SearchList } from './SearchList';
import { useButtonNavigate, useToast } from '../../../hooks';
import { removeAdminToken } from '../../../utils/auth';
import { ButtonExportReport } from './ButtonExportReport';

window.fetchSubscribedShops = async () => {
  try {
    const response = await ShopApi.search({
      query: 'mtm_custom: {"shop_status":1,"app_data.charge_id":{"$ne":""}}',
      page: 1,
      page_size: 1000,
    });

    const shops = response.data.results;
    const totalAmount = shops.reduce((sum, shop) => {
      const planAmount = parseFloat(shop.app_data?.plan_amount || 0);
      return sum + planAmount;
    }, 0);

    console.log('Total subscribed shops:', shops.length);
    console.log('Total plan amount:', totalAmount);

    return { shops, totalAmount };
    //
  } catch (error) {
    console.error('Error fetching subscribed shops:', error);
    throw error;
  }
};

export default function DashboardPage() {
  const [query, setQuery] = useState({ query: '', page: 1, page_size: 10 });
  const [searchMode, setSearchMode] = useState('all');
  const [triggerSearch, setTriggerSearch] = useState(false);
  const { navigate } = useButtonNavigate();

  // search mode
  const searchModeMapper = useMemo(
    () => ({
      all: '',
      unsupported_theme: 'mtm_custom: {"shop_status":1,"app_data.theme_supported":false}',
      not_touch_app: 'mtm_custom: {"shop_status":1,"app_data.app_touched":false}',
      not_enable_app: 'mtm_custom: {"shop_status":1,"app_data.app_enabled":false}',
      swatch_on_pdp: 'mtm_custom: {"shop_status":1,"app_data.swatch_on_pdp_enabled":true}',
      swatch_on_cdp: 'mtm_custom: {"shop_status":1,"app_data.swatch_on_cdp_enabled":true}',

      plan_shopify_basic: 'mtm_custom: {"shop_status":1,"shopify_data.plan_name":"basic"}',
      plan_shopify_shopify: 'mtm_custom: {"shop_status":1,"shopify_data.plan_name":"professional"}',
      plan_shopify_advanced: 'mtm_custom: {"shop_status":1,"shopify_data.plan_name":"unlimited"}',
      plan_shopify_plus: 'mtm_custom: {"shop_status":1,"shopify_data.plan_name":"shopify_plus"}',

      completed_starter_survey: 'mtm_custom: {"shop_status":1,"app_data.survey_status":"completed"}',
      subscribed_tapita: 'mtm_custom: {"shop_status":1,"app_data.charge_id":{"$ne":""}}',
      plan_tapita_free: 'mtm_custom: {"shop_status":1,"app_data.charge_id":""}',
      plan_tapita_basic:
        'mtm_custom: {"shop_status":1,"app_data.charge_id":{"$ne":""},"app_data.plan_display_name":"Basic"}',
      plan_tapita_standard:
        'mtm_custom: {"shop_status":1,"app_data.charge_id":{"$ne":""},"app_data.plan_display_name":"Standard"}',
      plan_tapita_advanced:
        'mtm_custom: {"shop_status":1,"app_data.charge_id":{"$ne":""},"app_data.plan_display_name":"Advanced"}',

      create_manual_single: 'mtm_custom: {"shop_status":1,"app_data.group_count_manual_single":{"$gt":0}}',
      create_manual_multiple: 'mtm_custom: {"shop_status":1,"app_data.group_count_manual_multiple":{"$gt":0}}',
      create_automation_single: 'mtm_custom: {"shop_status":1,"app_data.group_count_automation_single":{"$gt":0}}',
      create_automation_multiple: 'mtm_custom: {"shop_status":1,"app_data.group_count_automation_multiple":{"$gt":0}}',
    }),
    []
  );

  const searchModeOptions = useMemo(
    () => [
      { label: 'All Stores', value: 'all' },
      { label: 'Stores with Unsupported Themes', value: 'unsupported_theme' },
      { label: 'Stores with App Installed but Unused', value: 'not_touch_app' },
      { label: 'Stores with App Installed but Not Enabled', value: 'not_enable_app' },
      { label: 'Stores with Swatch On Product Page Feature Enabled', value: 'swatch_on_pdp' },
      { label: 'Stores with Swatch On Collection Page Feature Enabled', value: 'swatch_on_cdp' },

      { label: 'Stores on Shopify Basic Plan', value: 'plan_shopify_basic' },
      { label: 'Stores on Shopify Plan', value: 'plan_shopify_shopify' },
      { label: 'Stores on Shopify Advanced Plan', value: 'plan_shopify_advanced' },
      { label: 'Stores on Shopify Plus Plan', value: 'plan_shopify_plus' },

      { label: 'Stores that Completed Starter Survey', value: 'completed_starter_survey' },
      { label: 'Stores that Subscribed to Tapita Plan', value: 'subscribed_tapita' },
      { label: 'Stores on Tapita Free Plan', value: 'plan_tapita_free' },
      { label: 'Stores on Tapita Basic Plan', value: 'plan_tapita_basic' },
      { label: 'Stores on Tapita Standard Plan', value: 'plan_tapita_standard' },
      { label: 'Stores on Tapita Advanced Plan', value: 'plan_tapita_advanced' },

      { label: 'Stores that created Manual Single Option Group', value: 'create_manual_single' },
      { label: 'Stores that created Manual Multiple Options Group', value: 'create_manual_multiple' },
      { label: 'Stores that created Automated Single Option Group', value: 'create_automation_single' },
      { label: 'Stores that created Automated Multiple Options Group', value: 'create_automation_multiple' },
    ],
    []
  );

  const { data: appConfigData, isFetching: isAppConfigGetting } = useQuery(
    'app-config-data',
    () => AppConfigApi.get(),
    { refetchOnWindowFocus: false }
  );

  const { isLoading: isAppConfigUpdating, mutateAsync: updateAppConfig } = useMutation((data) =>
    AppConfigApi.update(data)
  );

  const { toast } = useToast();

  const toggleChat = async () => {
    const appConfig = appConfigData?.data;
    appConfig.support_enabled = !appConfig.support_enabled;
    await updateAppConfig(appConfig);
    toast.message('Live chat status updated');
  };

  const {
    data: searchData,
    isFetching: isSearching,
    refetch: refetchSearch,
    //
  } = useQuery('search-shops-repo', () => ShopApi.search(query), {
    refetchOnWindowFocus: false,
    enabled: false,
  });

  useEffect(() => {
    if (triggerSearch) refetchSearch();
    setTriggerSearch(false);
    //
  }, [triggerSearch, refetchSearch]);

  return (
    <div className="dashboard-page">
      <Page
        title="Admin dashboard"
        fullWidth
        primaryAction={{
          content: 'Logout',
          destructive: true,
          onAction: () => {
            removeAdminToken();
            window.location.reload();
          },
        }}
        secondaryActions={[
          appConfigData?.data?.support_enabled
            ? {
                icon: ToggleOnIcon,
                content: 'Chat is ON',
                onAction: toggleChat,
                loading: isAppConfigGetting || isAppConfigUpdating,
              }
            : {
                destructive: true,
                icon: ToggleOffIcon,
                content: 'Chat is OFF',
                onAction: toggleChat,
                loading: isAppConfigGetting || isAppConfigUpdating,
              },
        ]}
      >
        <Layout>
          <Layout.Section>
            <Grid>
              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 3, lg: 4, xl: 2 }}>
                <Card>
                  <Form
                    onSubmit={() => {
                      setQuery({ ...query, page: 1 });
                      setTriggerSearch(true);
                    }}
                  >
                    <BlockStack gap="300">
                      <Text fontWeight="medium">
                        <div className="ellipsis ellipsis--2">
                          Search by name / email / phone / domain / shop owner / myshopify domain / custom query (JSON)
                        </div>
                      </Text>
                      <Select
                        label={<Text fontWeight="medium">Filter:</Text>}
                        labelInline
                        options={searchModeOptions}
                        onChange={(value) => {
                          setSearchMode(value);
                          setQuery({ ...query, query: searchModeMapper[value] || '' });
                        }}
                        value={searchMode}
                      />
                      <Divider />
                      <TextField
                        value={query.query}
                        onChange={(value) => setQuery({ ...query, query: value })}
                        placeholder="Leave search box blank to get all stores . . ."
                      />
                      <Button submit variant="primary" size="large" loading={isSearching}>
                        Get stores
                      </Button>
                    </BlockStack>
                  </Form>
                </Card>
              </Grid.Cell>
              <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 3, lg: 8, xl: 10 }}>
                <Card>
                  <Box minHeight="185px">
                    <InlineStack gap="200">
                      <Button icon={QuestionCircleIcon}>FAQs</Button>
                      <Button icon={NoteIcon} onClick={navigate('/admin/release-note')}>
                        Release notes
                      </Button>
                      <Button icon={AppsIcon}>Recommended apps</Button>
                      <Button icon={DiscountIcon} onClick={navigate('/admin/discount')}>
                        Discounts
                      </Button>
                      <ButtonExportReport />
                    </InlineStack>
                  </Box>
                </Card>
              </Grid.Cell>
            </Grid>
          </Layout.Section>

          <Layout.Section />

          {isSearching && (
            <Layout.Section>
              <Text variant="headingLg" fontWeight="medium" alignment="center">
                Searching . . .
              </Text>
            </Layout.Section>
          )}

          {searchData?.data?.results && !isSearching && (
            <Layout.Section>
              <SearchList
                data={searchData?.data}
                handlePrev={() => {
                  setQuery({ ...query, page: query.page - 1 });
                  setTriggerSearch(true);
                }}
                handleNext={() => {
                  setQuery({ ...query, page: query.page + 1 });
                  setTriggerSearch(true);
                }}
              />
            </Layout.Section>
          )}

          <Layout.Section />
        </Layout>
      </Page>
    </div>
  );
}

