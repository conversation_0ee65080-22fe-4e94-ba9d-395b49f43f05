import _ from 'lodash';
import { BlockStack, Card, Divider, Form, FormLayout, Text } from '@shopify/polaris';
import { CustomInput } from '../../../components/input';

export const FormDiscount = ({ formData, errors, handleFieldChange }) => {
  return (
    <Form onSubmit={() => {}}>
      <BlockStack gap="400">
        <Card>
          <BlockStack gap="300">
            <Text variant="headingLg" alignment="center">
              DISCOUNT INFO
            </Text>

            <Divider />

            <FormLayout.Group>
              <CustomInput
                id="name"
                type="text"
                label="Name"
                placeholder="Enter discount name . . ."
                helpText="Use for management purposes."
                value={_.get(formData, 'name')}
                error={_.get(errors, 'name')}
                onChange={handleFieldChange}
              />
              <CustomInput
                id="display_name"
                type="text"
                label="Display Name"
                placeholder="Enter discount display name . . ."
                helpText="This is the name customers will see. Leave blank to use the discount name."
                value={_.get(formData, 'display_name')}
                error={_.get(errors, 'display_name')}
                onChange={handleFieldChange}
              />
            </FormLayout.Group>

            <FormLayout.Group>
              <CustomInput
                id="expired_at"
                type="date"
                label="Expiration Date"
                helpText="The discount will expire at 23:59 UTC on the selected date."
                value={_.get(formData, 'expired_at')}
                error={_.get(errors, 'expired_at')}
                onChange={handleFieldChange}
              />
              <CustomInput
                id="is_active"
                type="basic-select"
                label="Status"
                options={[
                  { label: 'Active', value: true },
                  { label: 'Inactive', value: false },
                ]}
                helpText="Set whether this discount is currently active or inactive."
                value={_.get(formData, 'is_active')}
                error={_.get(errors, 'is_active')}
                onChange={handleFieldChange}
              />
            </FormLayout.Group>

            <Divider />

            <FormLayout.Group>
              <CustomInput
                id="discount_code"
                type="text"
                label="Discount Code"
                helpText="Unique code customers enter to apply discount."
                placeholder="Enter discount code . . ."
                value={_.get(formData, 'discount_code')}
                error={_.get(errors, 'discount_code')}
                onChange={handleFieldChange}
              />
              <CustomInput
                id="discount_percent"
                type="number"
                label="Discount Percentage"
                helpText="Set a discount percentage (0-100%)."
                min={0}
                max={100}
                step={1}
                value={_.get(formData, 'discount_percent')}
                error={_.get(errors, 'discount_percent')}
                onChange={handleFieldChange}
              />
              <CustomInput
                id="discount_interval"
                type="number"
                label="Discount Interval"
                helpText="Defines how many billing cycles this discount applies to. Values above 100 mean permanent."
                min={1}
                max={31122003}
                step={1}
                value={_.get(formData, 'discount_interval')}
                error={_.get(errors, 'discount_interval')}
                onChange={handleFieldChange}
              />
            </FormLayout.Group>

            <Divider />

            <CustomInput
              id="targeted_shop_ids"
              type="text"
              label="Targeted Shops"
              placeholder="Enter shop IDs, separated by commas, or leave blank to apply to all shops . . ."
              helpText="Specify which shops can use this discount."
              value={_.get(formData, 'targeted_shop_ids')}
              error={_.get(errors, 'targeted_shop_ids')}
              onChange={handleFieldChange}
            />

            <CustomInput
              id="targeted_tapita_plans"
              type="text"
              label="Eligible Tapita Plans"
              placeholder="Enter plan names, separated by commas, or leave blank to apply to all plans . . ."
              helpText="Specify which Tapita subscription plans are eligible for this discount. Eligible plans: free, free_feb25, basic_feb25, standard_feb25, advanced_feb25, basic_apr25, standard_apr25, advanced_apr25."
              value={_.get(formData, 'targeted_tapita_plans')}
              error={_.get(errors, 'targeted_tapita_plans')}
              onChange={handleFieldChange}
            />

            <CustomInput
              id="targeted_shopify_plans"
              type="text"
              label="Eligible Shopify Plans"
              placeholder="Enter plan names, separated by commas, or leave blank to apply to all plans . . ."
              helpText="Specify which Shopify subscription plans are eligible for this discount. Popular plans: basic, professional, unlimited, shopify_plus, enterprise."
              value={_.get(formData, 'targeted_shopify_plans')}
              error={_.get(errors, 'targeted_shopify_plans')}
              onChange={handleFieldChange}
            />
          </BlockStack>
        </Card>
      </BlockStack>
    </Form>
  );
};

