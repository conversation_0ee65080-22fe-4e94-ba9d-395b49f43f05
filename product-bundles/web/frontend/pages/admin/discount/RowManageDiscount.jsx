import { Text, Button, IndexTable, ButtonGroup, InlineStack, Badge } from '@shopify/polaris';
import { DuplicateIcon, DeleteIcon, EditIcon } from '@shopify/polaris-icons';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useModal } from '../../../hooks';
import _ from 'lodash';

export const RowManageDiscount = ({ discount, handleDelete = async () => {}, handleDuplicate = async () => {} }) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const { modalOpen, showModal, hideModal, Modal } = useModal({});

  return (
    <IndexTable.Row id={discount._id} key={discount._id} onClick={() => {}}>
      <IndexTable.Cell className="cursor-default">
        <Text>{discount.name}</Text>
      </IndexTable.Cell>

      <IndexTable.Cell className="cursor-default">
        <Text alignment="center">{discount.discount_percent}%</Text>
      </IndexTable.Cell>

      <IndexTable.Cell className="cursor-default">
        <Text alignment="center">{discount.discount_interval}</Text>
      </IndexTable.Cell>

      <IndexTable.Cell className="cursor-default">
        <Text alignment="center">
          {new Date().getTime() - discount.expired_at >= 0 ? (
            <Badge tone="attention">Expired</Badge>
          ) : discount.is_active ? (
            <Badge tone="success">Active</Badge>
          ) : (
            <Badge>Inactive</Badge>
          )}
        </Text>
      </IndexTable.Cell>

      <IndexTable.Cell className="cursor-default">
        <Text alignment="center">{discount.discount_code}</Text>
      </IndexTable.Cell>

      <IndexTable.Cell className="cursor-default">
        <Text alignment="center">{discount.used_count}</Text>
      </IndexTable.Cell>

      <IndexTable.Cell className="cursor-default">
        <Text alignment="center">
          23:59 -{' '}
          {new Date(discount.expired_at).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
          })}
        </Text>
      </IndexTable.Cell>

      <IndexTable.Cell className="cursor-default">
        <InlineStack align="end">
          <ButtonGroup noWrap>
            <Button
              icon={DuplicateIcon}
              onClick={() => handleDuplicate(discount._id, setIsLoading)}
              loading={isLoading}
            />
            <Button
              icon={EditIcon}
              variant="primary"
              onClick={() => navigate(`/admin/discount/${discount._id}`)}
              loading={isLoading}
            />
            <Button icon={DeleteIcon} variant="primary" tone="critical" onClick={showModal} loading={isLoading} />
          </ButtonGroup>
        </InlineStack>
      </IndexTable.Cell>

      <Modal
        open={modalOpen}
        title="Confirm delete"
        onClose={hideModal}
        primaryAction={{
          content: 'Yes, delete it',
          destructive: true,
          loading: isLoading,
          onAction: async () => {
            await handleDelete(discount._id, setIsLoading);
            hideModal();
          },
        }}
        secondaryActions={[{ content: 'Cancel', loading: isLoading, onAction: hideModal }]}
      >
        <Text>Are you sure you want to delete this discount?</Text>
      </Modal>
    </IndexTable.Row>
  );
};

