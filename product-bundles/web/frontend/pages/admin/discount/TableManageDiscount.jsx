import { <PERSON>, Card, Tabs, Spinner, BlockStack, IndexTable, EmptyState, InlineStack } from '@shopify/polaris';
import { useMutation, useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import _ from 'lodash';

import { useToast } from '../../../hooks';
import { DiscountApi } from '../../../apis';
import { RowManageDiscount } from './RowManageDiscount';

export const TableManageDiscount = () => {
  // TOAST
  const { toast } = useToast();

  // RESOURCE
  const {
    data: discountsData,
    refetch: refetchDiscounts,
    isFetching: isDiscountsGetting,
  } = useQuery('discounts-repo', () => DiscountApi.get(), {
    refetchOnWindowFocus: false,
  });
  const discounts = discountsData?.data?.results || [];

  // ACTIONS
  const { mutateAsync: deleteDiscount } = useMutation((id) => DiscountApi.deleteById(id));
  const { mutateAsync: duplicateDiscount } = useMutation((id) => DiscountApi.duplicateById(id));

  const handleDeleteDiscount = async (id, setLoading = () => {}) => {
    setLoading(true);
    await deleteDiscount(id);
    await refetchDiscounts();
    setLoading(false);
    toast.message('Discount deleted');
  };

  const handleDuplicateDiscount = async (id, setLoading = () => {}) => {
    setLoading(true);
    await duplicateDiscount(id);
    await refetchDiscounts();
    setLoading(false);
    toast.message('Discount duplicated');
  };

  // TABLE TABS
  const tabs = [{ id: 'all', index: 0, content: 'All', target: 'all' }];

  // TABLE
  const rowMarkup = discounts.map((g, i) => (
    <RowManageDiscount
      key={i}
      discount={g}
      handleDelete={handleDeleteDiscount}
      handleDuplicate={handleDuplicateDiscount}
    />
  ));

  return (
    <Card padding="0">
      <InlineStack align="space-between" blockAlign="center">
        <Tabs tabs={tabs} selected={0} onSelect={() => {}} />
      </InlineStack>

      {!!discounts?.length && !isDiscountsGetting && (
        <IndexTable
          loading={isDiscountsGetting}
          resourceName={{ singular: 'discount', plural: 'discounts' }}
          itemCount={discounts.length}
          selectable={false}
          headings={[
            { title: 'Name' },
            { title: 'Discount', alignment: 'center' },
            { title: 'Interval', alignment: 'center' },
            { title: 'Status', alignment: 'center' },
            { title: 'Coupon', alignment: 'center' },
            { title: 'Used', alignment: 'center' },
            { title: 'Expired time (UTC)', alignment: 'center' },
            { title: '' },
          ]}
          lastColumnSticky
        >
          {rowMarkup}
        </IndexTable>
      )}

      {!discounts?.length && !isDiscountsGetting && <EmptyTableState />}

      {isDiscountsGetting && <LoadingTableState />}
    </Card>
  );
};

function LoadingTableState() {
  return (
    <BlockStack align="center" inlineAlign="center">
      <Box paddingBlock="800">
        <Spinner size="large" />
      </Box>
    </BlockStack>
  );
}

function EmptyTableState() {
  const navigate = useNavigate();

  return (
    <EmptyState
      heading="Create discounts to keep users informed."
      action={{ content: 'Create new', onAction: () => navigate('/admin/discount/new') }}
      image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
    >
      <Box padding="100">Start by adding your first discount to share updates and improvements.</Box>
    </EmptyState>
  );
}

