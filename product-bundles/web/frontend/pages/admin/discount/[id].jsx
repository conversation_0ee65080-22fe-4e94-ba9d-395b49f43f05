import _ from 'lodash';
import { useMutation, useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import { Layout, Page } from '@shopify/polaris';
import { DiscountApi } from '../../../apis';
import { FormDiscount } from './FormDiscount';
import { useFormHandler, useToast } from '../../../hooks';
import { useMemo } from 'react';

export default function EditDiscountPage() {
  const navigate = useNavigate();
  const discountId = useParams().id;

  const { data: discountData } = useQuery(`discount-${discountId}-repo`, () => DiscountApi.getById(discountId), {
    refetchOnWindowFocus: false,
  });

  const validationSchema = useMemo(
    () => ({
      name: (value) => {
        if (!value) return 'Name is required';
        return null;
      },
      discount_code: (value) => {
        if (!value) return 'Discount code is required';
        return null;
      },
    }),
    []
  );

  const convertDiscount = (discount) => {
    if (!discount) return null;
    const expiredDateStr = new Date(discount.expired_at).toISOString().split('T')[0];
    discount.expired_at = expiredDateStr;
    return discount;
  };

  const { formData, errors, hasError, runValidations, handleFieldChange } = useFormHandler(
    convertDiscount(discountData?.data),
    validationSchema
  );

  const { toast } = useToast();
  const { isLoading: isDiscountUpdating, mutateAsync: updateDiscount } = useMutation((data) =>
    DiscountApi.updateById(data?._id, data)
  );

  const handleSubmitForm = async () => {
    const { newHasError } = runValidations();
    if (newHasError) return;

    const submitData = {
      ...formData,
      targeted_shop_ids: formData.targeted_shop_ids
        .replaceAll(' ', '')
        .split(',')
        .filter((id) => id)
        .join(','),
      targeted_tapita_plans: formData.targeted_tapita_plans
        .replaceAll(' ', '')
        .split(',')
        .filter((plan) => plan)
        .join(','),
      targeted_shopify_plans: formData.targeted_shopify_plans
        .replaceAll(' ', '')
        .split(',')
        .filter((plan) => plan)
        .join(','),
      expired_at: new Date(formData.expired_at).getTime(),
    };

    await updateDiscount(submitData);
    toast.message('Discount updated');
    navigate('/admin/discount');
  };

  return (
    <Page
      title={`Edit discount - ${discountData?.data?.name}`}
      backAction={{ onAction: () => navigate('/admin/discount') }}
      primaryAction={{
        content: 'Save changes',
        disabled: hasError,
        loading: isDiscountUpdating,
        onAction: handleSubmitForm,
      }}
      secondaryActions={[
        {
          content: 'Discard',
          destructive: true,
          disabled: isDiscountUpdating,
          onAction: () => navigate('/admin/discount'),
        },
      ]}
    >
      <Layout>
        <Layout.Section>
          <FormDiscount formData={formData} errors={errors} handleFieldChange={handleFieldChange} />
        </Layout.Section>

        <Layout.Section />
      </Layout>
    </Page>
  );
}

