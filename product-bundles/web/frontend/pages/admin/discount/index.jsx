import { Layout, Page } from '@shopify/polaris';
import { useNavigate } from 'react-router-dom';
import { TableManageDiscount } from './TableManageDiscount';

export default function ManageDiscountPage() {
  const navigate = useNavigate();

  return (
    <Page
      title="Manage discounts"
      backAction={{ onAction: () => navigate('/admin/dashboard') }}
      primaryAction={{ content: 'Create new', onAction: () => navigate('/admin/discount/new') }}
    >
      <Layout>
        <Layout.Section>
          <TableManageDiscount />
        </Layout.Section>
      </Layout>
    </Page>
  );
}

