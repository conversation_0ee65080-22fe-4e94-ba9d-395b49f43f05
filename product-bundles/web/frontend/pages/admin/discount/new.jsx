import _ from 'lodash';
import { useMutation } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { Layout, Page } from '@shopify/polaris';
import { DiscountApi } from '../../../apis';
import { FormDiscount } from './FormDiscount';
import { useFormHandler, useToast } from '../../../hooks';
import { useMemo } from 'react';

export default function NewDiscountPage() {
  const navigate = useNavigate();
  const INITIAL_DATA = {
    is_active: false,
    name: '',
    display_name: '',
    discount_code: '',
    discount_percent: 10,
    discount_interval: 1,
    targeted_shop_ids: '',
    targeted_tapita_plans: '',
    targeted_shopify_plans: '',
    expired_at: '2025-12-31',
  };

  const validationSchema = useMemo(
    () => ({
      name: (value) => {
        if (!value) return 'Name is required';
        return null;
      },
      discount_code: (value) => {
        if (!value) return 'Discount code is required';
        return null;
      },
    }),
    []
  );

  const { formData, errors, hasError, runValidations, handleFieldChange } = useFormHandler(
    INITIAL_DATA,
    validationSchema
  );

  const { toast } = useToast();
  const { isLoading: isDiscountCreating, mutateAsync: createDiscount } = useMutation((data) =>
    DiscountApi.create(data)
  );

  const handleSubmitForm = async () => {
    const { newHasError } = runValidations();
    if (newHasError) return;

    const submitData = {
      ...formData,
      targeted_shop_ids: formData.targeted_shop_ids
        .replaceAll(' ', '')
        .split(',')
        .filter((id) => id)
        .join(','),
      targeted_tapita_plans: formData.targeted_tapita_plans
        .replaceAll(' ', '')
        .split(',')
        .filter((plan) => plan)
        .join(','),
      targeted_shopify_plans: formData.targeted_shopify_plans
        .replaceAll(' ', '')
        .split(',')
        .filter((plan) => plan)
        .join(','),
      expired_at: new Date(formData.expired_at).getTime(),
    };

    await createDiscount(submitData);
    toast.message('Discount created');
    navigate('/admin/discount');
  };

  return (
    <Page
      title="New discount"
      backAction={{ onAction: () => navigate('/admin/discount') }}
      primaryAction={{
        content: 'Save',
        disabled: hasError,
        loading: isDiscountCreating,
        onAction: handleSubmitForm,
      }}
    >
      <Layout>
        <Layout.Section>
          <FormDiscount formData={formData} errors={errors} handleFieldChange={handleFieldChange} />
        </Layout.Section>

        <Layout.Section />
      </Layout>
    </Page>
  );
}

