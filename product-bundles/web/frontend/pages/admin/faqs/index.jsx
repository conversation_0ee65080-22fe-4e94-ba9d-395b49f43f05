import { Page, Layout } from '@shopify/polaris';
import { useState } from 'react';
import { SortableList } from '../../../components/ui';
import { nav } from '../../../utils/mixed';
import { useNavigate } from 'react-router-dom';

export default function FAQs() {
  const [isSorting, setIsSorting] = useState(false);

  const [items, setItems] = useState([
    { id: 1, title: 'T-Shirt', status: 'active' },
    { id: 2, title: 'Skateboard', status: 'active' },
    { id: 3, title: 'Snowboard', status: 'archived' },
    { id: 4, title: 'Ultimate Snowboard', status: 'active' },
    { id: 5, title: 'Mechanical Pencil', status: 'draft' },
  ]);

  const toggleSort = () => {
    if (isSorting) items.sort((a, b) => a.id - b.id);
    setIsSorting(!isSorting);
  };

  const saveOrder = () => {
    const nItems = items.map((item, i) => ({ ...item, id: i }));
    setItems(nItems);
    setIsSorting(false);
  };

  const navigate = useNavigate();

  return (
    <Page
      title="Manage FAQs"
      backAction={{ onAction: () => navigate(nav('/product-group')) }}
      primaryAction={isSorting ? { content: 'Save order', onAction: saveOrder } : { content: 'Add new' }}
      secondaryActions={
        isSorting ? [{ content: 'Cancel', onAction: toggleSort }] : [{ content: 'Reorder', onAction: toggleSort }]
      }
    >
      <Layout>
        <Layout.Section>
          <SortableList items={items} setItems={setItems} sortable={isSorting} />
        </Layout.Section>
        <Layout.Section />
      </Layout>
    </Page>
  );
}
