import { Route, Routes } from 'react-router-dom';
import { getAdminToken, setShopToken } from '../../utils/auth';
import { AdminWrapper } from './AdminWrapper';

import NotFound from '../NotFound';
import LoginPage from './login';
import DashboardPage from './dashboard';

import HomePage from '../app';
import SettingPage from '../app/setting';
import PricingPage from '../app/pricing';

import ManageReleaseNotePage from './release-note';
import NewReleaseNotePage from './release-note/new';

import ManageDiscountPage from './discount';
import NewDiscountPage from './discount/new';
import EditDiscountPage from './discount/[id]';

import ShopLimitPage from './shop/[id]/limit';
import { useContext } from 'react';
import { AppDataContext } from '../../context/AppDataContext';

export default function AdminRoutes() {
  const { isMerchant, globalSwatchCSS } = useContext(AppDataContext);

  if (isMerchant) return <NotFound />;

  const isLoggedIn = !!getAdminToken();
  if (!isLoggedIn)
    return (
      <AdminWrapper isPortal={false}>
        <LoginPage />
      </AdminWrapper>
    );

  const shopToken = new URLSearchParams(window.location.search).get('token');
  if (shopToken) setShopToken(shopToken);

  const routes = [
    { path: '/admin/dashboard', component: DashboardPage },

    { path: '/admin/release-note', component: ManageReleaseNotePage },
    { path: '/admin/release-note/new', component: NewReleaseNotePage },

    { path: '/admin/discount', component: ManageDiscountPage },
    { path: '/admin/discount/new', component: NewDiscountPage },
    { path: '/admin/discount/:id', component: EditDiscountPage },

    { path: '/admin/shop/:id/limit', component: ShopLimitPage },

    { path: '/admin/portal', component: HomePage },
    { path: '/admin/portal/pricing', component: PricingPage },
    { path: '/admin/portal/setting', component: SettingPage },
  ];

  const routeComponents = routes.map(({ path, component: Component }) => (
    <Route
      key={path}
      path={path}
      element={
        <AdminWrapper isPortal={path?.startsWith('/admin/portal')}>
          <Component />
        </AdminWrapper>
      }
    />
  ));

  return (
    <>
      <style>{globalSwatchCSS}</style>
      <Routes>
        {routeComponents}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </>
  );
}

