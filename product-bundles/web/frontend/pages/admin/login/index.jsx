import { useState } from 'react';
import './styles.scss';
import { useMutation } from 'react-query';
import { AdminApi } from '../../../apis';
import { useToast } from '../../../hooks';
import { setAdminToken } from '../../../utils/auth';
import { useNavigate } from 'react-router-dom';

export default function LoginPage() {
  const { isLoading: isLoggingIn, mutateAsync: login } = useMutation((data) => AdminApi.login(data));
  const [formData, setFormData] = useState({ username: '', password: '' });
  const { toast } = useToast();

  const navigate = useNavigate();

  const handleLogin = async (e) => {
    e.preventDefault();
    login(formData)
      .then((data) => {
        const session = data?.data;
        if (session?.token) setAdminToken(session?.token);
        navigate('/admin/dashboard');
      })
      .catch((err) => toast.error(err.message));
  };

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData({ ...formData, [id]: value });
  };

  return (
    <div className="login-page">
      <div className="login-pane">
        <h1>LOGIN</h1>

        <form className="login-form" onSubmit={handleLogin} onChange={handleChange}>
          <div className="login-form__input">
            <label>Username</label>
            <input
              id="username"
              placeholder="Enter username . . ."
              value={formData.username}
              onChange={() => {}}
              required
            />
          </div>

          <div className="login-form__input">
            <label>Password</label>
            <input
              id="password"
              type="password"
              placeholder="Enter password . . ."
              value={formData.password}
              onChange={() => {}}
              required
            />
          </div>

          <button className="login-form__submit" type="submit" disabled={isLoggingIn}>
            {isLoggingIn ? 'Please wait . . .' : 'Login'}
          </button>
        </form>
      </div>
    </div>
  );
}
