body::before {
  content: '';
  position: absolute;
  top: -9999px;
  left: -9999px;
  width: 1px;
  height: 1px;
  background-image: url('https://cdn.shopify.com/s/files/1/0711/8759/5477/files/1.avif?v=1733594486'),
    url('https://cdn.shopify.com/s/files/1/0711/8759/5477/files/2.avif?v=1733594486'),
    url('https://cdn.shopify.com/s/files/1/0711/8759/5477/files/3.avif?v=1733594486'),
    url('https://cdn.shopify.com/s/files/1/0711/8759/5477/files/4.avif?v=1733594486'),
    url('https://cdn.shopify.com/s/files/1/0711/8759/5477/files/5.avif?v=1733594486'),
    url('https://cdn.shopify.com/s/files/1/0711/8759/5477/files/6.avif?v=1733594486');
  background-size: cover;
  z-index: -1;
}

@keyframes changeLoginImage {
  0% {
    background-image: url('https://cdn.shopify.com/s/files/1/0711/8759/5477/files/1.avif?v=1733594486');
  }
  16.6% {
    background-image: url('https://cdn.shopify.com/s/files/1/0711/8759/5477/files/2.avif?v=1733594486');
  }
  33.3% {
    background-image: url('https://cdn.shopify.com/s/files/1/0711/8759/5477/files/3.avif?v=1733594486');
  }
  50% {
    background-image: url('https://cdn.shopify.com/s/files/1/0711/8759/5477/files/4.avif?v=1733594486');
  }
  66.6% {
    background-image: url('https://cdn.shopify.com/s/files/1/0711/8759/5477/files/5.avif?v=1733594486');
  }
  83.3% {
    background-image: url('https://cdn.shopify.com/s/files/1/0711/8759/5477/files/6.avif?v=1733594486');
  }
  100% {
    background-image: url('https://cdn.shopify.com/s/files/1/0711/8759/5477/files/1.avif?v=1733594486');
  }
}

.login-page {
  background-size: cover;
  background-position: center;
  width: 100vw;
  height: 100vh;
  color: white;
  animation: changeLoginImage 18s infinite;

  * {
    font-size: 1.2rem !important;
    font-weight: 500;
  }

  .login-pane {
    width: 100%;
    max-width: 420px;
    background: transparent;
    backdrop-filter: blur(3px);
    padding: 4rem 2rem;
    border-radius: 1rem;
    box-shadow: 0 0 12px white;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
    font-weight: 700;

    h1 {
      text-align: center;
      margin-bottom: 2rem;
      font-size: 2rem !important;
      font-weight: 900;
      text-shadow: white 0px 0px 12px;
    }

    .login-form {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .login-form__input {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        label {
          text-shadow: white 0px 0px 12px;
        }

        input {
          padding: 0.5rem 1rem;
          border-radius: 100px;
          background: transparent;
          color: white;
          box-shadow: 0 0 12px white;
        }

        input::placeholder {
          color: rgba(255, 255, 255, 0.75);
        }
      }

      .login-form__submit {
        color: white;
        cursor: pointer;
        opacity: 1;
        padding: 0.5rem 1rem;
        margin-top: 2rem;
        transition: all 0.15s;
        box-shadow: 0 0 12px white;
        text-shadow: white 0px 0px 12px;
        border-radius: 100px;
        background-color: rgba(0, 0, 0, 0.75);

        &:hover {
          opacity: 0.65;
        }
      }
    }
  }
}

