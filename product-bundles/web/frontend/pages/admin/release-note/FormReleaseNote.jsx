import _ from 'lodash';
import { BlockStack, Card, Divider, Form, FormLayout, Text } from '@shopify/polaris';
import { CustomInput } from '../../../components/input';

export const FormReleaseNote = ({ formData, errors, handleFieldChange }) => {
  return (
    <Form onSubmit={() => {}}>
      <BlockStack gap="400">
        <Card>
          <BlockStack gap="300">
            <Text variant="headingLg" alignment="center">
              SETTING INFO
            </Text>

            <Divider />

            <FormLayout.Group>
              <CustomInput
                id="order"
                type="number"
                label="Order"
                placeholder="Enter theme setting name . . ."
                helpText="Use a name related to the supported themes for easy identification."
                value={_.get(formData, 'order')}
                error={_.get(errors, 'order')}
                onChange={handleFieldChange}
              />
              <CustomInput
                id="title"
                type="text"
                label="Title"
                placeholder="Enter theme setting target (global or shop ID) . . ."
                helpText="Use 'global' for all shops, or a shop ID for specific settings."
                value={_.get(formData, 'title')}
                error={_.get(errors, 'title')}
                onChange={handleFieldChange}
              />
              <CustomInput
                id="display_date"
                type="date"
                label="Target"
                placeholder="Enter theme setting target (global or shop ID) . . ."
                helpText="Use 'global' for all shops, or a shop ID for specific settings."
                value={_.get(formData, 'display_date')}
                error={_.get(errors, 'display_date')}
                onChange={handleFieldChange}
              />
            </FormLayout.Group>

            <CustomInput
              id="description"
              type="text"
              label="Original variant button getter"
              placeholder="(optionName, optionValue) => {}"
              multiline={6}
              helpText="JS function to get clickable variant button from option name and option value"
              value={_.get(formData, 'description')}
              error={_.get(errors, 'description')}
              onChange={handleFieldChange}
            />
          </BlockStack>
        </Card>
      </BlockStack>
    </Form>
  );
};

