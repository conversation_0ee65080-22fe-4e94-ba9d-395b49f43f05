import { Text, Button, IndexTable, ButtonGroup, InlineStack } from '@shopify/polaris';
import { DuplicateIcon, DeleteIcon, EditIcon } from '@shopify/polaris-icons';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useModal } from '../../../hooks';
import _ from 'lodash';

export const RowManageReleaseNote = ({
  releaseNote,
  handleDelete = async () => {},
  handleDuplicate = async () => {},
}) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const { modalOpen, showModal, hideModal, Modal } = useModal({});

  return (
    <IndexTable.Row id={releaseNote._id} key={releaseNote._id} onClick={() => {}}>
      <IndexTable.Cell className="cursor-default">
        <Text>{releaseNote.order}</Text>
      </IndexTable.Cell>

      <IndexTable.Cell className="cursor-default">
        <Text alignment="center">{releaseNote.title}</Text>
      </IndexTable.Cell>

      <IndexTable.Cell className="cursor-default">
        <Text alignment="center">{releaseNote.display_date}</Text>
      </IndexTable.Cell>

      <IndexTable.Cell className="cursor-default">
        <InlineStack align="end">
          <ButtonGroup noWrap>
            <Button
              icon={DuplicateIcon}
              onClick={() => handleDuplicate(releaseNote._id, setIsLoading)}
              loading={isLoading}
            />
            <Button
              icon={EditIcon}
              variant="primary"
              onClick={() => navigate(`/admin/release-note/${releaseNote._id}`)}
              loading={isLoading}
            />
            <Button icon={DeleteIcon} variant="primary" tone="critical" onClick={showModal} loading={isLoading} />
          </ButtonGroup>
        </InlineStack>
      </IndexTable.Cell>

      <Modal
        open={modalOpen}
        title="Confirm delete"
        onClose={hideModal}
        primaryAction={{
          content: 'Yes, delete it',
          destructive: true,
          loading: isLoading,
          onAction: async () => {
            await handleDelete(releaseNote._id, setIsLoading);
            hideModal();
          },
        }}
        secondaryActions={[{ content: 'Cancel', loading: isLoading, onAction: hideModal }]}
      >
        <Text>Are you sure you want to delete this release note?</Text>
      </Modal>
    </IndexTable.Row>
  );
};

