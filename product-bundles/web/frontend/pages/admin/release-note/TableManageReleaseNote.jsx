import { <PERSON>, <PERSON>, Tabs, Spinner, BlockStack, IndexTable, EmptyState, InlineStack } from '@shopify/polaris';
import { useMutation, useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import _ from 'lodash';

import { useToast } from '../../../hooks';
import { ReleaseNoteApi } from '../../../apis';
import { RowManageReleaseNote } from './RowManageReleaseNote';

export const TableManageReleaseNote = () => {
  // TOAST
  const { toast } = useToast();

  // RESOURCE
  const {
    data: releaseNotesData,
    refetch: refetchReleaseNotes,
    isFetching: isReleaseNotesGetting,
  } = useQuery('release-notes-repo', () => ReleaseNoteApi.get(), {
    refetchOnWindowFocus: false,
  });
  const releaseNotes = releaseNotesData?.data?.results || [];

  // ACTIONS
  const { mutateAsync: deleteReleaseNote } = useMutation((id) => ReleaseNoteApi.deleteById(id));
  const { mutateAsync: duplicateReleaseNote } = useMutation((id) => ReleaseNoteApi.duplicateById(id));

  const handleDeleteReleaseNote = async (id, setLoading = () => {}) => {
    setLoading(true);
    await deleteReleaseNote(id);
    await refetchReleaseNotes();
    setLoading(false);
    toast.message('Release note deleted');
  };

  const handleDuplicateReleaseNote = async (id, setLoading = () => {}) => {
    setLoading(true);
    await duplicateReleaseNote(id);
    await refetchReleaseNotes();
    setLoading(false);
    toast.message('Release note duplicated');
  };

  // TABLE TABS
  const tabs = [{ id: 'all', index: 0, content: 'All', target: 'all' }];

  // TABLE
  const rowMarkup = releaseNotes.map((g, i) => (
    <RowManageReleaseNote
      key={i}
      releaseNote={g}
      handleDelete={handleDeleteReleaseNote}
      handleDuplicate={handleDuplicateReleaseNote}
    />
  ));

  return (
    <Card padding="0">
      <InlineStack align="space-between" blockAlign="center">
        <Tabs tabs={tabs} selected={0} onSelect={() => {}} />
      </InlineStack>

      {!!releaseNotes?.length && !isReleaseNotesGetting && (
        <IndexTable
          loading={isReleaseNotesGetting}
          resourceName={{ singular: 'release note', plural: 'release notes' }}
          itemCount={releaseNotes.length}
          selectable={false}
          headings={[
            { title: 'Order' },
            { title: 'Title', alignment: 'center' },
            { title: 'Display date', alignment: 'center' },
            { title: '' },
          ]}
          lastColumnSticky
        >
          {rowMarkup}
        </IndexTable>
      )}

      {!releaseNotes?.length && !isReleaseNotesGetting && <EmptyTableState />}

      {isReleaseNotesGetting && <LoadingTableState />}
    </Card>
  );
};

function LoadingTableState() {
  return (
    <BlockStack align="center" inlineAlign="center">
      <Box paddingBlock="800">
        <Spinner size="large" />
      </Box>
    </BlockStack>
  );
}

function EmptyTableState() {
  const navigate = useNavigate();

  return (
    <EmptyState
      heading="Create release notes to keep users informed."
      action={{ content: 'Create new', onAction: () => navigate('/admin/release-note/new') }}
      image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
    >
      <Box padding="100">Start by adding your first release note to share updates and improvements.</Box>
    </EmptyState>
  );
}

