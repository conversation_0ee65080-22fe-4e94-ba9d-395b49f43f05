import _ from 'lodash';
import { useMutation, useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import { Layout, Page } from '@shopify/polaris';
import { ReleaseNoteApi } from '../../../apis';
import { FormReleaseNote } from './FormReleaseNote';
import { useFormHandler, useToast } from '../../../hooks';
import { useMemo } from 'react';

export default function EditReleaseNotePage() {
  const navigate = useNavigate();
  const releaseNoteId = useParams().id;

  const { data: releaseNoteData } = useQuery(
    `release-note-${releaseNoteId}-repo`,
    () => ReleaseNoteApi.getById(releaseNoteId),
    { refetchOnWindowFocus: false }
  );

  const validationSchema = useMemo(
    () => ({
      order: (value) => {
        if (!value) return 'Order is required';
        return null;
      },
      title: (value) => {
        if (!value) return 'Title is required';
        return null;
      },
      description: (value) => {
        if (!value) return 'Description is required';
        return null;
      },
      display_date: (value) => {
        if (!value) return 'Display date is required';
        return null;
      },
    }),
    []
  );

  const convertReleaseNote = (releaseNote) => {
    if (!releaseNote) return null;
    const supportedThemeStr = releaseNote.supported_themes?.join(', ');
    releaseNote.supported_themes_str = supportedThemeStr;
    return releaseNote;
  };

  const { formData, errors, hasError, runValidations, handleFieldChange } = useFormHandler(
    convertReleaseNote(releaseNoteData?.data),
    validationSchema
  );

  const { toast } = useToast();
  const { isLoading: isReleaseNoteUpdating, mutateAsync: updateReleaseNote } = useMutation((data) =>
    ReleaseNoteApi.updateById(data?._id, data)
  );

  const handleSubmitForm = async () => {
    const { newHasError } = runValidations();
    if (newHasError) return;

    await updateReleaseNote(formData);
    toast.message('Release note updated');
    navigate('/admin/release-note');
  };

  return (
    <Page
      title={`Edit release note - ${releaseNoteData?.data?.title}`}
      backAction={{ onAction: () => navigate('/admin/release-note') }}
      primaryAction={{
        content: 'Save changes',
        disabled: hasError,
        loading: isReleaseNoteUpdating,
        onAction: handleSubmitForm,
      }}
      secondaryActions={[
        {
          content: 'Discard',
          destructive: true,
          disabled: isReleaseNoteUpdating,
          onAction: () => navigate('/admin/release-note'),
        },
      ]}
    >
      <Layout>
        <Layout.Section>
          <FormReleaseNote formData={formData} errors={errors} handleFieldChange={handleFieldChange} />
        </Layout.Section>

        <Layout.Section />
      </Layout>
    </Page>
  );
}

