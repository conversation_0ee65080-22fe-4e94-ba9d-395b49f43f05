import { Layout, Page } from '@shopify/polaris';
import { useNavigate } from 'react-router-dom';
import { TableManageReleaseNote } from './TableManageReleaseNote';

export default function ManageReleaseNotePage() {
  const navigate = useNavigate();

  return (
    <Page
      title="Manage release notes"
      backAction={{ onAction: () => navigate('/admin/dashboard') }}
      primaryAction={{ content: 'Create new', onAction: () => navigate('/admin/release-note/new') }}
    >
      <Layout>
        <Layout.Section>
          <TableManageReleaseNote />
        </Layout.Section>
      </Layout>
    </Page>
  );
}

