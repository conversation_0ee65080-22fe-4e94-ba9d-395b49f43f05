import _ from 'lodash';
import { useMutation } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { Layout, Page } from '@shopify/polaris';
import { ReleaseNoteApi } from '../../../apis';
import { FormReleaseNote } from './FormReleaseNote';
import { useFormHandler, useToast } from '../../../hooks';
import { useMemo } from 'react';

export default function NewReleaseNotePage() {
  const navigate = useNavigate();
  const INITIAL_DATA = {
    order: 1,
    version: '',
    title: '',
    description: '',
    display_date: '',
    features: [],
    tags: [],
  };

  const validationSchema = useMemo(
    () => ({
      order: (value) => {
        if (!value) return 'Order is required';
        return null;
      },
      title: (value) => {
        if (!value) return 'Title is required';
        return null;
      },
      description: (value) => {
        if (!value) return 'Description is required';
        return null;
      },
      display_date: (value) => {
        if (!value) return 'Display date is required';
        return null;
      },
    }),
    []
  );

  const { formData, errors, hasError, runValidations, handleFieldChange } = useFormHandler(
    INITIAL_DATA,
    validationSchema
  );

  const { toast } = useToast();
  const { isLoading: isReleaseNoteCreating, mutateAsync: createReleaseNote } = useMutation((data) =>
    ReleaseNoteApi.create(data)
  );

  const handleSubmitForm = async () => {
    const { newHasError } = runValidations();
    if (newHasError) return;

    await createReleaseNote(formData);
    toast.message('Release note created');
    navigate('/admin/release-note');
  };

  return (
    <Page
      title="New release note"
      backAction={{ onAction: () => navigate('/admin/release-note') }}
      primaryAction={{
        content: 'Save',
        disabled: hasError,
        loading: isReleaseNoteCreating,
        onAction: handleSubmitForm,
      }}
    >
      <Layout>
        <Layout.Section>
          <FormReleaseNote formData={formData} errors={errors} handleFieldChange={handleFieldChange} />
        </Layout.Section>

        <Layout.Section />
      </Layout>
    </Page>
  );
}

