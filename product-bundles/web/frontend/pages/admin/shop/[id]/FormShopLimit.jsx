import _ from 'lodash';
import { Autocomplete, BlockStack, Card, Divider, Form, FormLayout, InlineStack, Tag, Text } from '@shopify/polaris';
import { CustomInput } from '../../../../components/input';
import { useCallback, useMemo, useState } from 'react';

export const FormShopLimit = ({ formData, plans, handleFieldChange }) => {
  const planOptions = useMemo(
    () =>
      plans?.map((plan) => ({
        label: `${plan?.plan_display_name} - ${plan?.plan_name}`,
        value: plan?.plan_name,
        limitation: plan?.default_limitation,
      })) || [],
    [plans]
  );

  const [planSearch, setPlanSearch] = useState('');
  const [filteredPlans, setFilteredPlans] = useState(planOptions);

  const updatePlanSearch = useCallback(
    (value) => {
      setPlanSearch(value);

      if (value === '') {
        setFilteredPlans(planOptions);
        return;
      }

      const filterRegex = new RegExp(value, 'i');
      const resultOptions = planOptions.filter((option) => option.label.match(filterRegex));

      setFilteredPlans(resultOptions);
    },
    [planOptions]
  );

  const verticalContentMarkup =
    formData?.plan_list?.length > 0 ? (
      <InlineStack gap="200" wrap={true}>
        {formData?.plan_list?.map((plan) => {
          const matchPlan = planOptions.find((p) => p.value === plan);

          return (
            <Tag
              key={`plan-${plan}`}
              onRemove={() => {
                const newPlans = formData?.plan_list?.filter((p) => p !== plan);
                handleFieldChange(newPlans, 'plan_list');
              }}
            >
              {matchPlan?.label}
            </Tag>
          );
        })}
      </InlineStack>
    ) : null;

  const pageTextField = (
    <Autocomplete.TextField
      label={<Text fontWeight="medium">Plans available for shop</Text>}
      value={planSearch}
      onChange={updatePlanSearch}
      verticalContent={verticalContentMarkup}
      placeholder="Select plan . . ."
      autoComplete="off"
      onBlur={() => updatePlanSearch('')}
    />
  );

  return (
    <Form onSubmit={() => {}}>
      <BlockStack gap={400}>
        <Card>
          <BlockStack gap="300">
            <Text variant="headingLg" alignment="center">
              Pricing Plan
            </Text>

            <Divider />

            <BlockStack gap="600">
              <FormLayout.Group>
                <CustomInput
                  id="plan_name"
                  type="basic-select"
                  options={planOptions}
                  label="Active plan"
                  value={_.get(formData, 'plan_name')}
                  onChange={(value, path) => {
                    const matchPlan = planOptions?.find((plan) => plan?.value === value);
                    handleFieldChange(value, path, [{ value: matchPlan?.label, path: 'plan_display_name' }]);
                  }}
                />
              </FormLayout.Group>

              <Autocomplete
                allowMultiple
                options={filteredPlans}
                selected={_.get(formData, 'plan_list') || []}
                textField={pageTextField}
                onSelect={(plans) => handleFieldChange(plans, 'plan_list')}
              />
            </BlockStack>
          </BlockStack>
        </Card>

        <Card>
          <BlockStack gap="300">
            <Text variant="headingLg" alignment="center">
              Limitation
            </Text>

            <Divider />

            <BlockStack gap="800">
              <FormLayout.Group>
                <CustomInput
                  id="freeze_limitation"
                  type="basic-select"
                  options={[
                    { label: "FREEZE - Custom limit won't be override by plan limit when app load", value: true },
                    { label: 'NOT FREEZE - Custom limit will be override by plan limit when app load', value: false },
                  ]}
                  label="Freeze custom limit"
                  value={_.get(formData, 'freeze_limitation')}
                  onChange={handleFieldChange}
                />
                <CustomInput
                  id="max_group_count"
                  type="number"
                  min="0"
                  max="20000"
                  label="Max group allowed"
                  value={_.get(formData, 'max_group_count')}
                  onChange={handleFieldChange}
                />
                <CustomInput
                  id="max_product_count"
                  type="number"
                  min="0"
                  max="100000"
                  label="Max products allowed"
                  value={_.get(formData, 'max_product_count')}
                  onChange={handleFieldChange}
                />
                <CustomInput
                  id="max_sync_count"
                  type="number"
                  min="0"
                  max="24"
                  label="Max auto-sync per day"
                  value={_.get(formData, 'max_sync_count')}
                  onChange={handleFieldChange}
                />
              </FormLayout.Group>

              <FormLayout.Group>
                <CustomInput
                  id="unlocked_import_export_groups"
                  type="switch2"
                  label="Import / export groups feature"
                  value={_.get(formData, 'unlocked_import_export_groups')}
                  onChange={handleFieldChange}
                />
                <CustomInput
                  id="unlocked_import_export_swatches"
                  type="switch2"
                  label="Import / export swatches feature"
                  value={_.get(formData, 'unlocked_import_export_swatches')}
                  onChange={handleFieldChange}
                />
                <CustomInput
                  id="unlocked_auto_resync"
                  type="switch2"
                  label="Auto resync products info feature"
                  value={_.get(formData, 'unlocked_auto_resync')}
                  onChange={handleFieldChange}
                />
              </FormLayout.Group>

              <FormLayout.Group>
                <CustomInput
                  id="allowed_show_option_on_card"
                  type="switch2"
                  label="Allow show options on card"
                  value={_.get(formData, 'allowed_show_option_on_card')}
                  onChange={handleFieldChange}
                />
                <CustomInput
                  id="allowed_request_api_key"
                  type="switch2"
                  label="Allow manage groups through API"
                  value={_.get(formData, 'allowed_request_api_key')}
                  onChange={handleFieldChange}
                />
                <CustomInput
                  id="allowed_upload_image"
                  type="switch2"
                  label="Allow upload image for swatch"
                  value={_.get(formData, 'allowed_upload_image')}
                  onChange={handleFieldChange}
                />
              </FormLayout.Group>

              <FormLayout.Group>
                <CustomInput
                  id="unlocked_auto_group"
                  type="switch2"
                  label="Group automation by title"
                  value={_.get(formData, 'unlocked_auto_group')}
                  onChange={handleFieldChange}
                />
                <CustomInput
                  id="unlocked_auto_group_by_tag"
                  type="switch2"
                  label="Group automation by tag"
                  value={_.get(formData, 'unlocked_auto_group_by_tag')}
                  onChange={handleFieldChange}
                />
                <CustomInput
                  id="unlocked_swatch"
                  type="switch2"
                  label="Re-design option feature"
                  value={_.get(formData, 'unlocked_swatch')}
                  onChange={handleFieldChange}
                />
                <CustomInput
                  id="removed_trademark"
                  type="switch2"
                  label="Remove trademark"
                  value={_.get(formData, 'removed_trademark')}
                  onChange={handleFieldChange}
                />
              </FormLayout.Group>

              <FormLayout.Group>
                <CustomInput
                  id="allowed_update_stock_instantly"
                  type="switch2"
                  label="Allow update product stock in group instantly by webhook"
                  value={_.get(formData, 'allowed_update_stock_instantly')}
                  onChange={handleFieldChange}
                />
                <CustomInput
                  id="allowed_update_automation_group"
                  type="switch2"
                  label="Allow update automation group by webhook"
                  value={_.get(formData, 'allowed_update_automation_group')}
                  onChange={handleFieldChange}
                />
              </FormLayout.Group>
            </BlockStack>
          </BlockStack>
        </Card>
      </BlockStack>
    </Form>
  );
};

