import _ from 'lodash';
import { useMutation, useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import { Layout, Page } from '@shopify/polaris';
import { useFormHandler, useToast } from '../../../../hooks';
import { Pricing<PERSON>lan<PERSON>pi, ShopApi } from '../../../../apis';
import { FormShopLimit } from './FormShopLimit';
import { useCallback, useMemo } from 'react';

export default function ShopLimitPage() {
  const navigate = useNavigate();
  const shopId = useParams().id;

  const {
    isFetching: isShopGetting,
    data: shopData,
    refetch: refetchShop,
  } = useQuery(`shop-${shopId}-repo`, () => ShopApi.getById(shopId), {
    refetchOnWindowFocus: false,
  });

  const { isFetching: isPlansGetting, data: plansData } = useQuery('plans-repo', () => PricingPlanApi.getAllPlans(), {
    refetchOnWindowFocus: false,
  });

  const INITIAL_DATA = useMemo(() => {
    const limitation = JSON.parse(
      shopData?.data?.app_data?.feature_limitation ||
        `
          {
            "max_sync_count": 0,
            "max_group_count": 5,
            "max_product_count": 50,

            "unlocked_swatch": true,
            "removed_trademark": false,
            "unlocked_auto_group": false,
            "unlocked_auto_group_by_tag": false,
            "allowed_upload_image": false,
            "allowed_request_api_key": false,
            "allowed_show_option_on_card": false,
            "allowed_update_stock_instantly": false,
            "allowed_update_automation_group": false,
            "unlocked_import_export_groups": false,
            "unlocked_import_export_swatches": false,
            "unlocked_auto_resync": false
          }
        `
    );

    limitation.plan_list = shopData?.data?.app_data?.plan_list;
    limitation.plan_name = shopData?.data?.app_data?.plan_name;
    limitation.plan_display_name = shopData?.data?.app_data?.plan_display_name;
    limitation.freeze_limitation = shopData?.data?.app_data?.freeze_limitation;

    return limitation;
    //
  }, [
    shopData?.data?.app_data?.feature_limitation,
    shopData?.data?.app_data?.freeze_limitation,
    shopData?.data?.app_data?.plan_display_name,
    shopData?.data?.app_data?.plan_list,
    shopData?.data?.app_data?.plan_name,
  ]);

  const validationSchema = useMemo(() => ({}), []);

  const { formData, hasError, handleFieldChange } = useFormHandler(INITIAL_DATA, validationSchema);

  const { toast } = useToast();
  const { isLoading: isShopUpdating, mutateAsync: updateShop } = useMutation((data) =>
    ShopApi.updateById(data?._id, data)
  );

  const handleSubmitForm = useCallback(async () => {
    const submitData = shopData?.data;
    submitData.app_data.plan_list = formData?.plan_list;
    submitData.app_data.plan_name = formData?.plan_name;
    submitData.app_data.plan_display_name = formData?.plan_display_name;
    submitData.app_data.freeze_limitation = formData?.freeze_limitation;
    delete formData.plan_list;
    delete formData.plan_name;
    delete formData.plan_display_name;
    delete formData.freeze_limitation;
    submitData.app_data.feature_limitation = JSON.stringify(formData);

    await updateShop(submitData);
    await refetchShop();
    toast.message('Shop updated');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData, shopData?.data, refetchShop, updateShop]);

  return (
    <Page
      title={`Feature limitation - ${shopData?.data?.shopify_data?.name}`}
      backAction={{ onAction: () => navigate('/admin/dashboard') }}
      primaryAction={{
        content: 'Save',
        disabled: hasError,
        loading: isShopGetting || isShopUpdating || isPlansGetting,
        onAction: handleSubmitForm,
      }}
    >
      <Layout>
        {!(isShopGetting || isShopUpdating || isPlansGetting) && (
          <Layout.Section>
            <FormShopLimit formData={formData} plans={plansData?.data} handleFieldChange={handleFieldChange} />
          </Layout.Section>
        )}

        <Layout.Section />
      </Layout>
    </Page>
  );
}

