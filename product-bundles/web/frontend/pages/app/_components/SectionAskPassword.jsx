import { Banner, Layout } from '@shopify/polaris';
import { LockIcon } from '@shopify/polaris-icons';
import { useHidden } from '../../../hooks';

export const SectionAskPassword = () => {
  const { isHidden, hide } = useHidden('mpg_section_ask_password_hidden');

  if (isHidden) return null;

  return (
    <Layout.Section>
      <Banner
        title="Your online store is password protected"
        icon={LockIcon}
        tone="warning"
        action={{ content: 'Add password' }}
        onDismiss={hide}
      >
        <p>Please share your online store password so our team can access your store and support you better.</p>
      </Banner>
    </Layout.Section>
  );
};

