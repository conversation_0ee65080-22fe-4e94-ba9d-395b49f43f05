import { Banner, Button, InlineStack, Layout, Text } from '@shopify/polaris';

export const SectionAskResync = () => {
  return (
    <Layout.Section variant="fullWidth">
      <Banner tone="warning">
        <InlineStack wrap={false} gap="300" blockAlign="center">
          <Text>Product changes detected. Please resync product info with the group to keep data updated.</Text>
          <Button variant="primary">Resync products</Button>
        </InlineStack>
      </Banner>
    </Layout.Section>
  );
};
