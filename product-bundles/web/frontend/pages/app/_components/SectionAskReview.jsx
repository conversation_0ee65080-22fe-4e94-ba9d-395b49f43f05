import { Banner, BlockStack, Box, Icon, InlineStack, Layout, Text } from '@shopify/polaris';
import { StarIcon, StarFilledIcon } from '@shopify/polaris-icons';
import { useState } from 'react';
import { useHidden, useSupport } from '../../../hooks';
import './SectionAskReview.scss';

export const SectionAskReview = ({ type = 'block' }) => {
  const [spin, setSpin] = useState(false);
  const [rating, setRating] = useState(null);
  const [hoveringStar, setHoveringStar] = useState(null);
  const { isHidden, hide, hideForever } = useHidden('mtm_section_ask_review_hidden');
  const { startSupport } = useSupport();

  const content = (
    <>
      <Text variant="bodyLg" fontWeight="semibold">
        How was your experience using Grouptify: Combined Listings?
      </Text>

      <div style={{ transform: 'scale(1.1)', paddingRight: '10px' }}>
        <InlineStack gap="050">
          {[1, 2, 3, 4, 5].map((id) => (
            <div
              key={id}
              className={`mpg-rating-star ${id <= rating && 'selected'} ${spin && 'spin'}`}
              onMouseEnter={() => setHoveringStar(id)}
              onMouseLeave={() => setHoveringStar(null)}
              onClick={() => {
                setSpin(false);
                setRating(id);

                window.setTimeout(() => {
                  if (id <= 3)
                    startSupport("I'm not satisfied with some features of the app and would like to discuss it.");
                  //
                  else window.open('https://apps.shopify.com/grouptify-combined-listings', '_blank');
                  hideForever();
                }, 500);
              }}
            >
              <Box key={id} padding="050">
                <Icon source={rating >= id || hoveringStar >= id ? StarFilledIcon : StarIcon} />
              </Box>
            </div>
          ))}
        </InlineStack>
      </div>
    </>
  );

  if (isHidden) return null;

  return (
    <Layout.Section variant={type === 'block' ? 'oneHalf' : 'fullWidth'}>
      <Banner
        onDismiss={() => {
          if (rating) hideForever();
          // else if (!spin) setSpin(true);
          else hide();
        }}
      >
        {type === 'block' ? (
          <BlockStack gap={200} inlineAlign="start">
            {content}
            <Box minHeight="4px" />
          </BlockStack>
        ) : (
          <InlineStack gap={200} align="space-between" blockAlign="center">
            {content}
          </InlineStack>
        )}
      </Banner>
    </Layout.Section>
  );
};

