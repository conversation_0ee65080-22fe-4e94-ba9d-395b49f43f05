import { BlockStack, <PERSON>, <PERSON><PERSON>, Card, Image, InlineStack, Layout, Text } from '@shopify/polaris';

export const SectionChangeLog = () => {
  return (
    <Layout.Section>
      <Card>
        <InlineStack wrap={false} gap={600} align="space-between">
          <BlockStack gap={400}>
            <Text fontWeight="semibold" variant="bodyLg">
              Combined Listings - What&apos;s New?
            </Text>

            <div>
              <Text>Stay up to date with the latest improvements, new features, and fixes!</Text>
              <Text>📢 Click below to view the full changelog and see what&apos;s new.</Text>
            </div>

            <div>
              <Button
                onClick={() =>
                  window.open(
                    'https://tapita0.zohodesk.com/portal/en/kb/articles/grouptify-color-swatch-variant-options',
                    '_blank'
                  )
                }
              >
                Explore here
              </Button>
            </div>
          </BlockStack>
          <Box paddingInlineEnd={600}>
            <Image
              height={240}
              source="https://cdn.shopify.com/s/files/1/0711/8759/5477/files/Changelog_2_1.png?v=1741680960"
            />
          </Box>
        </InlineStack>
      </Card>
    </Layout.Section>
  );
};

