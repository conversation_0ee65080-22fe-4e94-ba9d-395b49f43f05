import { BlockStack, Box, Button, ButtonGroup, Image, Layout, RadioButton } from '@shopify/polaris';
import { useContext, useEffect, useMemo, useState } from 'react';
import { SetupGuide } from '../../../components/ui';
import { ExternalIcon, RefreshIcon } from '@shopify/polaris-icons';
import { nav } from '../../../utils/mixed';
import { useHidden } from '../../../hooks';
import { useNavigate } from 'react-router-dom';
import { AppDataContext } from '../../../context/AppDataContext';
import _ from 'lodash';

export const SectionColorSwatchGuide = () => {
  const { isLoading, shop, warning, refetchData } = useContext(AppDataContext);
  const { isHidden, hide } = useHidden('mpg_section_color_swatch_guide_hidden');
  const enableAppUrlProd = `https://${shop?.shopify_data?.myshopify_domain}${warning?.enable_swatch_on_page_url}`;
  const enableAppUrlCard = `https://${shop?.shopify_data?.myshopify_domain}${warning?.enable_swatch_on_card_url}`;

  const [recheckApp, setRecheckApp] = useState(false);
  const [selectedGuideId, setSelectedGuideId] = useState('product_page');
  const [items, setItems] = useState([]);
  const navigate = useNavigate();

  const guides = useMemo(
    () => [
      {
        id: 'product_page',
        label: 'Product Page Swatches',
        image:
          'https://cdn.shopify.com/s/files/1/0711/8759/5477/files/1_414cca64-e99a-463f-8e9a-78077e3592dd.png?v=1745576045',
        items: [
          {
            id: 0,
            title: 'Enable App and Swatch on Product Page',
            loading: isLoading,
            description: `Enable the app embed, then turn on "Restyle variant picker on product page" to apply Grouptify's swatch and variant picker styles.`,
            complete: !_.isEmpty(warning) && !warning?.need_enable_app && !warning?.need_enable_swatch_on_page,
            primaryButton: {
              content: recheckApp ? 'Refresh' : 'Enable App',
              props: {
                onClick: async () => {
                  if (recheckApp) {
                    await refetchData({ warning: true });
                    setRecheckApp(false);
                  } else {
                    window.open(enableAppUrlProd, '_blank');
                    setRecheckApp(true);
                  }
                },
                icon: recheckApp ? RefreshIcon : ExternalIcon,
                loading: isLoading,
              },
            },
            secondaryButton: {
              content: 'Learn More',
              props: {
                onClick: () =>
                  window.open(
                    'https://tapita0.zohodesk.com/portal/en/kb/articles/how-to-enable-grouptify-app-embed-in-your-theme',
                    '_blank'
                  ),
                loading: isLoading,
              },
            },
          },
          {
            id: 1,
            title: 'Assign Option Display Styles',
            loading: isLoading,
            description:
              'Choose display styles for options (e.g., color as swatches, size as buttons) from 8+ options like buttons, swatches, dropdowns for a tailored store experience.',
            complete:
              !_.isEmpty(warning) &&
              !warning?.need_enable_app &&
              !warning?.need_enable_swatch_on_page &&
              !warning?.need_configure_option,
            primaryButton: {
              content: 'Configure',
              props: {
                onClick: () => navigate(nav('/swatch-config?type=shopify')),
                loading: isLoading,
                disabled: warning?.need_enable_app || warning?.need_enable_swatch_on_page,
              },
            },
            secondaryButton: {
              content: 'Learn More',
              props: {
                onClick: () =>
                  window.open(
                    'https://tapita0.zohodesk.com/portal/en/kb/articles/how-to-configure-shopify-option-redesign',
                    '_blank'
                  ),
                loading: isLoading,
              },
            },
          },
          {
            id: 2,
            title: 'Customize Option Appearance',
            loading: isLoading,
            description:
              "Fine-tune swatches, buttons, and more to match your store's theme, with hover effects and cohesive design for product pages.",
            complete:
              !_.isEmpty(warning) &&
              !warning?.need_enable_app &&
              !warning?.need_enable_swatch_on_page &&
              !warning?.need_configure_option &&
              !warning?.need_customize_template,
            primaryButton: {
              content: 'Customize',
              props: {
                onClick: () => navigate(nav('/swatch-template')),
                loading: isLoading,
                disabled:
                  warning?.need_enable_app || warning?.need_enable_swatch_on_page || warning?.need_configure_option,
              },
            },
            // secondaryButton: {
            //   content: 'Learn More',
            //   props: {
            //     onClick: () =>
            //       window.open(
            //         'https://tapita0.zohdesk.com/portal/en/kb/articles/how-to-configure-shopify-option-redesign',
            //         '_blank'
            //       ),
            //     loading: isLoading,
            //   },
            // },
          },
        ],
      },
      {
        id: 'product_card',
        label: 'Collection Page Swatches',
        image:
          'https://cdn.shopify.com/s/files/1/0711/8759/5477/files/2_1f427f74-76f0-42fc-a14a-7dcfacaa93c0.png?v=1745576045',
        items: [
          {
            id: 0,
            title: 'Enable App and Swatch on Collection Page',
            loading: isLoading,
            description:
              'Enable the app embed, then turn on "Show swatches on product cards" to display options as swatches or other styles on collection pages.',
            complete: !_.isEmpty(warning) && !warning?.need_enable_app && !warning?.need_enable_swatch_on_card,
            primaryButton: {
              content: recheckApp ? 'Refresh' : 'Enable App',
              props: {
                onClick: async () => {
                  if (recheckApp) {
                    await refetchData({ warning: true });
                    setRecheckApp(false);
                  } else {
                    window.open(enableAppUrlCard, '_blank');
                    setRecheckApp(true);
                  }
                },
                icon: recheckApp ? RefreshIcon : ExternalIcon,
                loading: isLoading,
              },
            },
            secondaryButton: {
              content: 'Learn More',
              props: {
                onClick: () =>
                  window.open(
                    'https://tapita0.zohodesk.com/portal/en/kb/articles/how-to-apply-swatches-on-your-product-detail-page-with-grouptify',
                    '_blank'
                  ),
                loading: isLoading,
              },
            },
          },
          {
            id: 1,
            title: 'Assign Option Display Styles',
            loading: isLoading,
            description:
              'Choose display styles for options (e.g., color as swatches, size as buttons) from 8+ options like buttons, swatches, dropdowns for a tailored store experience.',
            complete:
              !_.isEmpty(warning) &&
              !warning?.need_enable_app &&
              !warning?.need_enable_swatch_on_card &&
              !warning?.need_configure_option,
            primaryButton: {
              content: 'Configure',
              props: {
                onClick: () => navigate(nav('/swatch-config?type=shopify')),
                loading: isLoading,
                disabled: warning?.need_enable_app || warning?.need_enable_swatch_on_card,
              },
            },
            secondaryButton: {
              content: 'Learn More',
              props: {
                onClick: () =>
                  window.open(
                    'https://tapita0.zohodesk.com/portal/en/kb/articles/how-to-configure-shopify-option-redesign',
                    '_blank'
                  ),
                loading: isLoading,
              },
            },
          },
          {
            id: 2,
            title: 'Customize Option Appearance',
            loading: isLoading,
            description:
              'Fine-tune swatches, buttons, and more to match your store’s theme, with hover effects and cohesive design for collection pages.',
            complete:
              !_.isEmpty(warning) &&
              !warning?.need_enable_app &&
              !warning?.need_enable_swatch_on_card &&
              !warning?.need_configure_option &&
              !warning?.need_customize_template,
            primaryButton: {
              content: 'Customize',
              props: {
                onClick: () => navigate(nav('/swatch-template')),
                loading: isLoading,
                disabled:
                  warning?.need_enable_app || warning?.need_enable_swatch_on_card || warning?.need_configure_option,
              },
            },
            // secondaryButton: {
            //   content: 'Learn More',
            //   props: {
            //     onClick: () =>
            //       window.open(
            //         'https://tapita0.zohdesk.com/portal/en/kb/articles/how-to-configure-shopify-option-redesign',
            //         '_blank'
            //       ),
            //     loading: isLoading,
            //   },
            // },
          },
        ],
      },
    ],
    [enableAppUrlCard, enableAppUrlProd, isLoading, navigate, recheckApp, refetchData, warning]
  );

  const selectedGuide = useMemo(() => guides.find((g) => g.id === selectedGuideId), [guides, selectedGuideId]);

  useEffect(() => {
    setItems(selectedGuide?.items);
  }, [selectedGuide?.items]);

  const onStepComplete = async (id) => {
    try {
      await new Promise((res) => setTimeout(res, [1000]));
      setItems((prev) => prev.map((item) => (item.id === id ? { ...item, complete: !item.complete } : item)));
      //
    } catch (e) {
      console.error(e);
    }
  };

  if (isHidden) return null;

  return (
    <Layout.Section variant="fullWidth">
      <SetupGuide
        heading="Setup Guide for Re-designing Shopify Option"
        isLoading={isLoading}
        onDismiss={hide}
        onStepComplete={onStepComplete}
        items={items}
        additionalElement={
          <Box padding={200}>
            <ButtonGroup noWrap>
              {guides.map((guide) => {
                const isActive = guide.id === selectedGuideId;

                return (
                  <Box borderRadius="200" background={isActive && 'bg-surface-active'} key={guide.id}>
                    <Button variant="tertiary" fullWidth onClick={() => setSelectedGuideId(guide.id)}>
                      <Box padding={200}>
                        <BlockStack gap={200}>
                          <RadioButton
                            label={guide.label}
                            checked={isActive}
                            id={guide.id}
                            name="enable_app"
                            onChange={() => setSelectedGuideId(guide.id)}
                          />
                          <Image
                            source={guide.image}
                            width="100%"
                            alt="TPXĐTMNNCPMQ đã nàm ảnh lày"
                            style={{ filter: isActive ? 'none' : 'grayscale(100%)' }}
                          />
                        </BlockStack>
                      </Box>
                    </Button>
                  </Box>
                );
              })}
            </ButtonGroup>
          </Box>
        }
      />
    </Layout.Section>
  );
};

