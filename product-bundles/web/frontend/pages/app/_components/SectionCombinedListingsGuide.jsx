import { Layout } from '@shopify/polaris';
import { useContext, useEffect, useMemo, useState } from 'react';
import { SetupGuide } from '../../../components/ui';
import { ExternalIcon, RefreshIcon } from '@shopify/polaris-icons';
import { nav } from '../../../utils/mixed';
import { useHidden } from '../../../hooks';
import { useNavigate } from 'react-router-dom';
import { AppDataContext } from '../../../context/AppDataContext';
import _ from 'lodash';

export const SectionCombinedListingsGuide = () => {
  const { isLoading, shop, warning, refetchData } = useContext(AppDataContext);
  const { isHidden, hide } = useHidden('mpg_section_combined_listings_guide_hidden');
  const enableAppUrl = `https://${shop?.shopify_data?.myshopify_domain}${warning?.enable_app_url}`;

  const [recheckApp, setRecheckApp] = useState(false);
  const [items, setItems] = useState([]);
  const navigate = useNavigate();

  const ITEMS = useMemo(
    () => [
      {
        id: 0,
        title: 'Enable App Embed',
        loading: isLoading,
        description:
          'Enable app embed in your theme to allow the product groups to function and display properly on your Online Store.',
        complete: !_.isEmpty(warning) && !warning?.need_enable_app,
        primaryButton: {
          content: recheckApp ? 'Refresh' : 'Enable App',
          props: {
            onClick: async () => {
              if (recheckApp) {
                await refetchData({ warning: true });
                setRecheckApp(false);
              } else {
                window.open(enableAppUrl, '_blank');
                setRecheckApp(true);
              }
            },
            icon: recheckApp ? RefreshIcon : ExternalIcon,
            loading: isLoading,
          },
        },
        secondaryButton: {
          content: 'Learn More',
          props: {
            onClick: () =>
              window.open(
                'https://tapita0.zohodesk.com/portal/en/kb/articles/how-to-enable-grouptify-combined-listings-app-embed-in-your-theme',
                '_blank'
              ),
            loading: isLoading,
          },
        },
      },
      {
        id: 1,
        title: 'Create Product Group',
        loading: isLoading,
        description:
          'Group products together to display them as variants on the product page, making it easier for customers to explore different options and enhancing the shopping experience.',
        complete: !_.isEmpty(warning) && !warning?.need_enable_app && !warning?.need_create_group,
        primaryButton: {
          content: 'Create Group',
          props: {
            onClick: () => navigate(nav('/product-group/single-option/new')),
            loading: isLoading,
            disabled: warning?.need_enable_app,
          },
        },
        secondaryButton: {
          content: 'Learn More',
          props: {
            onClick: () =>
              window.open(
                'https://tapita0.zohodesk.com/portal/en/kb/articles/what-is-a-product-group-and-how-to-create-one',
                '_blank'
              ),
            loading: isLoading,
          },
        },
      },
      {
        id: 2,
        title: 'Manage Translations',
        loading: isLoading,
        description:
          'Create translations for each language in your store to ensure product options are displayed in multiple languages, offering a seamless experience for your international customers.',
        complete:
          !_.isEmpty(warning) &&
          !warning?.need_enable_app &&
          !warning?.need_create_group &&
          !warning?.need_create_translation,
        primaryButton: {
          content: 'Add A Translation',
          props: {
            onClick: () => navigate(nav('/localization')),
            loading: isLoading,
            disabled: warning?.need_enable_app,
          },
        },
        secondaryButton: {
          content: 'Learn More',
          props: {
            onClick: () =>
              window.open(
                'https://tapita0.zohodesk.com/portal/en/kb/articles/how-to-create-translations-for-multilingual-display',
                '_blank'
              ),
            loading: isLoading,
          },
        },
      },
    ],
    [enableAppUrl, isLoading, navigate, recheckApp, refetchData, warning]
  );

  useEffect(() => {
    setItems(ITEMS);
  }, [ITEMS]);

  const onStepComplete = async (id) => {
    try {
      await new Promise((res) => setTimeout(res, [1000]));
      setItems((prev) => prev.map((item) => (item.id === id ? { ...item, complete: !item.complete } : item)));
      //
    } catch (e) {
      console.error(e);
    }
  };

  if (isHidden) return null;

  return (
    <Layout.Section variant="fullWidth">
      <SetupGuide
        heading="Setup Guide for Creating Product Groups"
        isLoading={isLoading}
        onDismiss={hide}
        onStepComplete={onStepComplete}
        items={items}
      />
    </Layout.Section>
  );
};

