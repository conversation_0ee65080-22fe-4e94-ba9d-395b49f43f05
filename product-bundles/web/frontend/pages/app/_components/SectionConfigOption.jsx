import { BlockStack, Button, Layout, Text } from '@shopify/polaris';
import { CustomCalloutCard } from '../../../components/input';
import { useNavigate } from 'react-router-dom';
import { nav } from '../../../utils/mixed';

export const SectionConfigOption = ({ loading, disabled }) => {
  const navigate = useNavigate();

  return (
    <Layout.Section variant="oneHalf">
      <CustomCalloutCard
        title="Configure Option Appearance & Swatch Style"
        illustration="https://cdn.shopify.com/s/files/1/0711/8759/5477/files/redesign.png?v=1733309049"
      >
        <BlockStack gap="150" inlineAlign="start">
          <div style={{ marginBottom: -2 }} />
          <Text>Replace the default Shopify variant styling with a sleek design from our custom template.</Text>
          <div />
          <Button onClick={() => navigate(nav('/swatch-config?type=shopify'))} loading={loading} disabled={disabled}>
            Configure
          </Button>
        </BlockStack>
      </CustomCalloutCard>
    </Layout.Section>
  );
};

