import { BlockStack, Button, Layout, Text } from '@shopify/polaris';
import { CustomCalloutCard } from '../../../components/input';
import { useNavigate } from 'react-router-dom';
import { nav } from '../../../utils/mixed';

export const SectionCustomTemplate = ({ loading, disabled }) => {
  const navigate = useNavigate();

  return (
    <Layout.Section variant="oneHalf">
      <CustomCalloutCard
        title="Customize Appearance Template"
        illustration="https://cdn.shopify.com/s/assets/admin/checkout/settings-customizecart-705f57c725ac05be5a34ec20c05b94298cb8afd10aac7bd9c7ad02030f48cfa0.svg"
      >
        <BlockStack gap="150" inlineAlign="start">
          <div style={{ marginBottom: -2 }} />
          <Text>Personalize the appearance of each template to align with your branding and preferences.</Text>
          <div />
          <Button
            onClick={() => navigate(nav('/swatch-template?back_action=true'))}
            loading={loading}
            disabled={disabled}
          >
            Customize
          </Button>
        </BlockStack>
      </CustomCalloutCard>
    </Layout.Section>
  );
};

