import { Card, Layout } from '@shopify/polaris';
import { useEffect, useRef, useState } from 'react';

export const SectionEcosystem = ({ data = null }) => {
  const containerRef = useRef(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(null);
  const scriptSrc =
    (window.scriptSrc ||= `https://cdn.shopify.com/s/files/1/0597/3783/3627/files/tptecos-ecosystem.js?v=${Math.round(Math.random() * 100000)}`);

  useEffect(() => {
    if (window.TapitaEcosystem) return setIsLoaded(true);
    const script = Object.assign(document.createElement('script'), { src: scriptSrc, async: true });
    script.onload = () => setIsLoaded(true);
    script.onerror = () => setError('Failed to load TapitaEcosystem script');
    document.body.appendChild(script);
    return () => document.body.removeChild(script);
  }, [scriptSrc]);

  useEffect(() => {
    if (!isLoaded || !containerRef.current || !window.TapitaEcosystem) return;
    try {
      const options = { activePhase: 1, ...(data && { data }) };
      containerRef.current._instance = new window.TapitaEcosystem(containerRef.current, options);
    } catch (err) {
      setError(`Error initializing TapitaEcosystem: ${err.message}`);
    }
  }, [isLoaded, data]);

  useEffect(() => {
    if (containerRef.current?._instance && isLoaded) {
      containerRef.current._instance.update({ activePhase: 1 });
    }
  }, [isLoaded]);

  if (error) {
    console.warn('Ecosystem Error:', error);
    return null;
  }
  if (!isLoaded) return null;

  return (
    <Layout.Section>
      <Card>
        <div id="tapita-container" ref={containerRef} />
      </Card>
    </Layout.Section>
  );
};

