import { BlockStack, But<PERSON>, Card, Layout, List, Text } from '@shopify/polaris';
import { Accordion } from '../../../components/ui/Accordion';
import { nav } from '../../../utils/mixed';
import { useNavigate } from 'react-router-dom';
import { useSupport } from '../../../hooks';

export const SectionFAQ = () => {
  const navigate = useNavigate();
  const { startSupport } = useSupport();

  const faqs = [
    {
      id: 0,
      title: 'Can I display group options on the product card?',
      content: (
        <List>
          <BlockStack gap="150">
            <List.Item>
              Yes, you can enable this by going to Settings and selecting the pages where you want the group options to
              be shown on the product card. Configure it{' '}
              <Button variant="plain" onClick={() => navigate(nav('/setting'))}>
                here
              </Button>
              .
            </List.Item>

            <List.Item>
              If you&apos;re using an app like Boost AI Search & Filter, make sure to check the box for &quot;I use
              Boost AI Search & Filter app.&quot; This app alters the structure of the product card, and letting us know
              ensures the options display correctly.
            </List.Item>
          </BlockStack>
        </List>
      ),
    },
    {
      id: 1,
      title: 'Can I create two product groups for the same product?',
      content: (
        <Text>
          No, you cannot. Having the same product in two groups would create conflicts and result in errors when
          displaying options in your store.
        </Text>
      ),
    },
    {
      id: 2,
      title: 'Why do I get a notification saying my theme is unsupported, asking me to contact support?',
      content: (
        <List>
          <BlockStack gap="150">
            <List.Item>
              Each theme has a unique structure, so specific settings are needed to ensure product groups display
              correctly.
            </List.Item>

            <List.Item>
              Currently, our app supports over 100 different themes. You can check them{' '}
              <Button variant="plain">here</Button>, but your theme might not be included.
            </List.Item>

            <List.Item>
              If you encounter this issue, please{' '}
              <Button
                variant="plain"
                onClick={() =>
                  startSupport("My theme isn't supported. I need help to display product groups correctly.")
                }
              >
                contact us
              </Button>
              , and we&apos;ll adjust things to make sure our app works seamlessly with your theme.
            </List.Item>
          </BlockStack>
        </List>
      ),
    },
    {
      id: 3,
      title: "My theme is supported, but the options still aren't displaying correctly. Why?",
      content: (
        <List>
          <BlockStack gap="150">
            <List.Item>
              This can happen if your store uses apps like page builders or section builders, which may modify the
              store&apos;s structure and cause conflicts.
            </List.Item>

            <List.Item>
              To ensure proper display of options on the product page and product card, please{' '}
              <Button
                variant="plain"
                onClick={() =>
                  startSupport("My store is using page builder apps and the group options aren't displaying correctly.")
                }
              >
                contact us
              </Button>
              , and we&apos;ll make the necessary adjustments.
            </List.Item>
          </BlockStack>
        </List>
      ),
    },
    {
      id: 4,
      title: 'Can I change the option style to match the variant style in our theme?',
      content: (
        <List>
          <BlockStack gap="150">
            <List.Item>
              In the current version of the app, you can only choose from the available appearances we provide. However,
              you can customize the style through custom CSS{' '}
              <Button variant="plain" onClick={() => navigate(nav('/setting'))}>
                here
              </Button>
              .
            </List.Item>

            <List.Item>
              Writing custom CSS can be complex. While we&apos;re working on a more user-friendly visual editor, feel
              free to{' '}
              <Button variant="plain" onClick={() => startSupport('I need help with style customization.')}>
                contact us
              </Button>
              , and we&apos;ll help customize the style for you.
            </List.Item>
          </BlockStack>
        </List>
      ),
    },
  ];

  return (
    <Layout.Section>
      <Card padding="0">
        <Accordion items={faqs} title="FAQs" />
      </Card>
    </Layout.Section>
  );
};
