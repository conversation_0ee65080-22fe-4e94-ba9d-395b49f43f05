import { BlockStack, But<PERSON>, Card, Layout, List } from '@shopify/polaris';
import { Accordion } from '../../../components/ui/Accordion';
import { nav } from '../../../utils/mixed';
import { useNavigate } from 'react-router-dom';
import { useSupport } from '../../../hooks';

export const SectionFAQColorSwatch = () => {
  const navigate = useNavigate();
  const { startSupport } = useSupport();

  const faqs = [
    {
      id: 0,
      title: 'How can I re-design the appearance of Shopify Product Options?',
      content: (
        <List>
          <BlockStack gap="150">
            <List.Item>
              To customize how your product options are displayed, you need to configure the appearance settings for
              each option. Click{' '}
              <Button variant="plain" onClick={() => navigate(nav('/swatch-config?type=shopify'))}>
                here
              </Button>{' '}
              to access the settings. These options allow you to choose different display styles, such as buttons,
              swatches, dropdowns, and more.
            </List.Item>

            <List.Item>
              For example, if you set the appearance of the &quot;Color&quot; option to a circle swatch, all products
              with the &quot;Color&quot; option will display it as a circular swatch. Similarly, you can configure the
              &quot;Size&quot; option to display as a dropdown or any other style you prefer.
            </List.Item>
          </BlockStack>
        </List>
      ),
    },
    {
      id: 1,
      title: 'How can I configure styles for swatches?',
      content: (
        <List>
          <BlockStack gap="150">
            <List.Item>
              When you set the appearance of an option, such as &quot;Color&quot;, to a swatch style (e.g., circle
              swatch, square swatch, etc.), a gear-shaped button will appear next to that option. Clicking this button
              will take you to the swatch management interface for the selected option.
            </List.Item>

            <List.Item>
              In the swatch management interface, you can configure various settings such as style (single color, dual
              color, or image) and assign specific colors or images to the swatches. For example, if you configure a
              swatch with the name &quot;Rose&quot; for the &quot;Color&quot; option and set it to display as a red
              color swatch, all option values with the name &quot;Rose&quot; will appear as red swatches.
            </List.Item>
          </BlockStack>
        </List>
      ),
    },
    {
      id: 2,
      title: 'Why do I get a notification saying my theme is unsupported, asking me to contact support?',
      content: (
        <List>
          <BlockStack gap="150">
            <List.Item>
              Each theme has a unique structure, so specific settings are needed to ensure swatches display correctly.
            </List.Item>

            <List.Item>
              Currently, our app supports over 100 different themes. You can check them{' '}
              <Button variant="plain">here</Button>, but your theme might not be included.
            </List.Item>

            <List.Item>
              If you encounter this issue, please{' '}
              <Button
                variant="plain"
                onClick={() => startSupport("My theme isn't supported. I need help to display swatches correctly.")}
              >
                contact us
              </Button>
              , and we&apos;ll adjust things to make sure our app works seamlessly with your theme.
            </List.Item>
          </BlockStack>
        </List>
      ),
    },
    {
      id: 3,
      title: "My theme is supported, but the options still aren't displaying correctly. Why?",
      content: (
        <List>
          <BlockStack gap="150">
            <List.Item>
              This can happen if your store uses apps like page builders or section builders, which may modify the
              store&apos;s structure and cause conflicts.
            </List.Item>

            <List.Item>
              To ensure proper display of options on the product page and product card, please{' '}
              <Button
                variant="plain"
                onClick={() =>
                  startSupport("My store is using page builder apps and the group options aren't displaying correctly.")
                }
              >
                contact us
              </Button>
              , and we&apos;ll make the necessary adjustments.
            </List.Item>
          </BlockStack>
        </List>
      ),
    },
    {
      id: 4,
      title: 'Can I customize the style of swatches, buttons, or dropdowns?',
      content: (
        <List>
          <BlockStack gap="150">
            <List.Item>
              Yes, you can customize these styles through the appearance settings available{' '}
              <Button variant="plain" onClick={() => navigate(nav('/swatch-template'))}>
                here
              </Button>
              . All option appearances are displayed here, allowing you to make adjustments such as size, font, border,
              and more.
            </List.Item>

            <List.Item>
              If the built-in appearance customization options are not sufficient, you can perform deeper customization
              through custom CSS, accessible{' '}
              <Button variant="plain" onClick={() => navigate(nav('/setting'))}>
                here
              </Button>
              .
            </List.Item>

            <List.Item>
              Writing custom CSS can be complex. While we are working on a more user-friendly visual editor, feel free
              to{' '}
              <Button variant="plain" onClick={() => startSupport('I need help with style customization.')}>
                contact us
              </Button>
              , and we’ll assist you in customizing the styles.
            </List.Item>
          </BlockStack>
        </List>
      ),
    },
  ];

  return (
    <Layout.Section>
      <Card padding="0">
        <Accordion items={faqs} title="FAQs" />
      </Card>
    </Layout.Section>
  );
};

