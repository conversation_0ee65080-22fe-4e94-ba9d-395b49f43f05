import { BlockStack, Card, Layout, Text } from '@shopify/polaris';
import './SectionFeature.scss';

export const SectionFeature = ({ image, title, subTitle, selected, faded, onClick = () => {} }) => {
  return (
    <Layout.Section variant="oneHalf">
      <div className={`section-feature ${faded ? 'faded' : ''} ${selected ? 'selected' : ''}`} onClick={onClick}>
        <Card>
          <BlockStack gap="200" align="center" inlineAlign="center">
            <div
              style={{
                backgroundImage: `url("${image}")`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                width: '100%',
                aspectRatio: '3/2',
                border: '1px solid #ddd',
                borderRadius: '6px',
              }}
            >
              &nbsp;
            </div>
            <Text variant="headingLg">{title}</Text>
            <Text>{subTitle}</Text>
          </BlockStack>
        </Card>
      </div>
    </Layout.Section>
  );
};

