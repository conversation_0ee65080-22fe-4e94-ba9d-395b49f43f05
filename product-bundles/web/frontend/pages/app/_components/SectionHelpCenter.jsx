import { <PERSON>Stack, Button, Card, Icon, InlineStack, Layout, Text } from '@shopify/polaris';
import { QuestionCircleIcon } from '@shopify/polaris-icons';

export const SectionHelpCenter = () => {
  return (
    <Layout.Section variant="oneHalf">
      <Card>
        <BlockStack gap={200}>
          <InlineStack gap={100}>
            <div>
              <Icon source={QuestionCircleIcon} />
            </div>
            <Text variant="bodyLg" fontWeight="semibold">
              Help Center
            </Text>
          </InlineStack>
          <Text>Find quick answers in detail and frequently asked questions.</Text>
          <div>
            <Button
              variant="plain"
              onClick={() => window.open('https://tapita0.zohodesk.com/portal/en/home', '_blank')}
            >
              Help Center
            </Button>
          </div>
        </BlockStack>
      </Card>
    </Layout.Section>
  );
};

