import { Layout, MediaCard } from '@shopify/polaris';
import { useHidden } from '../../../hooks';

export const SectionIntroduceAzzel = () => {
  const { isHidden, hideForever } = useHidden('mtm_section_introduce_azzel_hidden');

  if (isHidden) return null;

  return (
    <Layout.Section variant="fullWidth">
      <MediaCard
        title="Boost your Shopify performance with Azzel theme."
        primaryAction={{
          content: 'Explore Now',
          onAction: () => window.open('https://themes.shopify.com/themes/azzel/styles/primary', '_blank'),
        }}
        description="The newly released Azzel theme is highly recommended for integration with Grouptify: Combined Listing, offering perfect integration, optimized performance, and a sleek design to enhance user experience. Azzel theme is designed to work well with Grouptify: Combined Listings, it ensures smooth navigation and a visually appealing storefront, helping you boost engagement and conversions effortlessly."
        popoverActions={[{ content: 'Dismiss', onAction: hideForever }]}
      >
        <img
          alt="azzel"
          width="100%"
          height="250px"
          style={{ objectFit: 'cover', objectPosition: 'center' }}
          src="https://cdn.shopify.com/s/files/1/0711/8759/5477/files/azzel.png?width=800"
        />
      </MediaCard>
    </Layout.Section>
  );
};

