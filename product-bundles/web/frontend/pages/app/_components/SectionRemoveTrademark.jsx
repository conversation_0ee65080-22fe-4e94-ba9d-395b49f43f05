import { Banner, Button, Layout, Text } from '@shopify/polaris';
import { useHidden, useSupport } from '../../../hooks';
import './SectionAskReview.scss';
import { useContext, useEffect } from 'react';
import { AppDataContext } from '../../../context/AppDataContext';

export const SectionRemoveTrademark = () => {
  const { isHidden, hide, hideForever, unhide } = useHidden('mtm_section_remove_trademark_hidden');
  const { startSupport } = useSupport();
  const { shop } = useContext(AppDataContext);

  useEffect(() => {
    const featureLimit = JSON.parse(shop?.app_data?.feature_limitation || '{}');
    if (featureLimit?.removed_trademark) hideForever();
    else unhide();
    //
  }, [hideForever, unhide, shop?.app_data?.feature_limitation]);

  if (isHidden || !shop?.app_data?.feature_limitation) return null;

  return (
    <Layout.Section variant="fullWidth">
      <Banner onDismiss={hide}>
        <Text variant="bodyLg">
          If you want to remove the &quot;
          <span style={{ fontWeight: 500 }}>
            Powered by{' '}
            <a
              href="https://apps.shopify.com/grouptify-combined-listings"
              target="_blank"
              rel="noreferrer"
              style={{ color: '#E86046', fontWeight: 700 }}
            >
              Grouptify
            </a>
          </span>
          &quot; trademark, contact us{' '}
          <Button variant="plain" onClick={() => startSupport('I want to remove Grouptify trademark')}>
            <Text variant="bodyLg">here</Text>
          </Button>
          .
        </Text>
      </Banner>
    </Layout.Section>
  );
};
