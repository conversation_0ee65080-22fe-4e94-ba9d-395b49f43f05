import { Block<PERSON>ta<PERSON>, <PERSON><PERSON>, Card, Layout, Text } from '@shopify/polaris';
// import { useSupport } from '../../../hooks';
import { useNavigate } from 'react-router-dom';
import { nav } from '../../../utils/mixed';

export const SectionStyleSupport = () => {
  // const { startSupport } = useSupport();
  const navigate = useNavigate();

  return (
    <Layout.Section variant="oneHalf">
      <Card>
        <BlockStack gap="300" inlineAlign="start">
          <Text variant="headingMd">Manage & customize group appearance</Text>
          <Text>This is a feature for designing your group options (currently in beta with limited access).</Text>
          <Button onClick={() => navigate(nav('/swatch-config'))}>Access feature</Button>
        </BlockStack>
      </Card>
    </Layout.Section>
  );

  // return (
  //   <Layout.Section variant="oneHalf">
  //     <Card>
  //       <BlockStack gap="300" inlineAlign="start">
  //         <Text variant="headingMd">Need help customizing swatches to fit your theme?</Text>
  //         <Text>
  //           Allow us access to your Shopify Admin to provide faster, detailed support through Apps and Online Store &gt;
  //           Themes.
  //         </Text>
  //         <Button onClick={() => startSupport('I need help with style customization.')}>Contact us</Button>
  //       </BlockStack>
  //     </Card>
  //   </Layout.Section>
  // );
};

