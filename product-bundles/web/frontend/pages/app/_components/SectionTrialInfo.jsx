import { Banner, BlockStack, Box, Button, Layout, Text } from '@shopify/polaris';
import { useContext, useMemo } from 'react';
import { AppDataContext } from '../../../context/AppDataContext';
import { useNavigate } from 'react-router-dom';
import { nav } from '../../../utils/mixed';

export const SectionTrialInfo = () => {
  const { shop, limitation } = useContext(AppDataContext);
  const navigate = useNavigate();

  const innerContent = useMemo(() => {
    if (limitation.unlocked_swatch) return null;

    if (shop?.app_data?.trial_status === 1)
      return (
        <BlockStack wrap={false} gap="150">
          <Text>You are currently using the app&apos;s premium features on a trial basis.</Text>
          <Text>
            This trial grants you access to Shopify option redesign functionality and the ability to display product
            options on product cards.
          </Text>
          <Text>
            The trial will expire at 23:59 UTC+0 on {shop?.app_data?.trial_end?.split('-')?.reverse()?.join('-')}. To
            continue using these features in the long run, you should{' '}
            <Button variant="plain" onClick={() => navigate(nav('/pricing'))}>
              upgrade your plan
            </Button>
            .
          </Text>
          <Box paddingBlockStart="150">
            <Button variant="primary" onClick={() => navigate(nav('/pricing'))}>
              Upgrade Plan
            </Button>
          </Box>
        </BlockStack>
      );

    if (shop?.app_data?.trial_status === 2)
      return (
        <BlockStack wrap={false} gap="150">
          <Text>
            Your free trial has ended. During the trial, you accessed premium features like Shopify option redesign and
            product options on product cards.
          </Text>
          <Text>
            To continue using these features without interruption, please{' '}
            <Button variant="plain" onClick={() => navigate(nav('/pricing'))}>
              upgrade your plan
            </Button>
            .
          </Text>
          <Box paddingBlockStart="150">
            <Button variant="primary" onClick={() => navigate(nav('/pricing'))}>
              Upgrade Now
            </Button>
          </Box>
        </BlockStack>
      );

    return null;
    //
  }, [limitation.unlocked_swatch, navigate, shop?.app_data?.trial_end, shop?.app_data?.trial_status]);

  if (!innerContent) return null;

  return (
    <Layout.Section variant="fullWidth">
      <Banner tone="warning" title={shop?.app_data?.trial_status === 1 ? 'Trial in Progress' : 'Trial Ended'}>
        {innerContent}
      </Banner>
    </Layout.Section>
  );
};

