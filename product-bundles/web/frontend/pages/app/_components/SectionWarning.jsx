import { Banner, BlockStack, Button, Layout, Text } from '@shopify/polaris';
import { ExternalIcon, RefreshIcon } from '@shopify/polaris-icons';
import { useContext, useState } from 'react';
import { AppDataContext } from '../../../context/AppDataContext';
import { useSupport } from '../../../hooks';

export const SectionWarning = ({
  appWarning = true,
  // themeWarning = true
}) => {
  const {
    isLoading,
    shop,
    warning,
    refetchData,
    // shared
  } = useContext(AppDataContext);
  const [recheck, setRecheck] = useState(false);
  const { startSupport } = useSupport();

  const showAppWarning = appWarning && warning?.need_enable_app;
  // const showThemeWarning = themeWarning && !showAppWarning && warning?.need_create_theme_setting && shared.themeChecked;
  const showThemeWarning = false;
  const enableAppUrl = `https://${shop?.shopify_data?.myshopify_domain}${warning?.enable_app_url}`;

  if (!showAppWarning && !showThemeWarning) return null;

  return (
    <Layout.Section variant="fullWidth">
      <BlockStack gap="0">
        {showThemeWarning && (
          <Banner tone="warning" title="Your theme is temporarily not supported">
            <BlockStack gap="200">
              <Text>
                Our app currently supports over 16 themes, but your theme is not included. This may cause the display of
                options on your storefront to be inaccurate. Please{' '}
                <Button
                  variant="plain"
                  onClick={() =>
                    startSupport("My theme isn't supported. I need help to display product groups correctly.")
                  }
                >
                  contact us
                </Button>
                , and we&apos;ll update the app to support your theme as soon as possible.
              </Text>
              <div>
                <Button
                  onClick={() =>
                    startSupport("My theme isn't supported. I need help to display product groups correctly.")
                  }
                >
                  Contact us
                </Button>
              </div>
            </BlockStack>
          </Banner>
        )}

        {showAppWarning && (
          <Banner tone="warning" title="App embed is not enabled">
            <BlockStack gap="200">
              <Text>
                To allow the app to function, please go to Online Store &gt; Theme Editor &gt; App embeds, and enable
                &quot;Gtify: Combined Listings&quot;, or click the button below.
              </Text>
              <div>
                <Button
                  variant="primary"
                  onClick={async () => {
                    if (recheck) {
                      await refetchData({ warning: true });
                      setRecheck(false);
                    } else {
                      window.open(enableAppUrl, '_blank');
                      setRecheck(true);
                    }
                  }}
                  icon={recheck ? RefreshIcon : ExternalIcon}
                  loading={isLoading}
                >
                  {recheck ? 'Refresh' : 'Enable App'}
                </Button>
              </div>
            </BlockStack>
          </Banner>
        )}
      </BlockStack>
    </Layout.Section>
  );
};

