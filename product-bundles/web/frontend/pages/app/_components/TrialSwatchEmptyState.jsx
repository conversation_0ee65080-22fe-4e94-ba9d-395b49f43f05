import { Box, Button, EmptyState, Text } from '@shopify/polaris';
import { nav } from '../../../utils/mixed';
import { useNavigate } from 'react-router-dom';
import { useModal, useRedirect } from '../../../hooks';
import { useCallback, useContext, useMemo } from 'react';
import { useMutation } from 'react-query';
import { PricingPlanApi } from '../../../apis';
import { AppDataContext } from '../../../context/AppDataContext';

export const TrialSwatchEmptyState = () => {
  const navigate = useNavigate();
  const redirect = useRedirect();
  const { shop } = useContext(AppDataContext);
  const { modalOpen, showModal, hideModal, Modal } = useModal({});

  const { isLoading: isTrialStarting, mutateAsync: startTrial } = useMutation(() =>
    PricingPlanApi.startTrial({ feature: 'redesign_option', duration: 8 })
  );

  const handleStartTrial = useCallback(async () => {
    const trialData = await startTrial();
    if (trialData?.data?.confirmationUrl) redirect(trialData.data.confirmationUrl, true);
  }, [redirect, startTrial]);

  const trialEndTime = useMemo(() => {
    const currentDate = new Date();
    currentDate.setDate(currentDate.getDate() + 8);

    const day = String(currentDate.getDate()).padStart(2, '0');
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');
    const year = currentDate.getFullYear();

    return `${day}/${month}/${year}`;
  }, []);

  const trialEnded = shop?.app_data?.trial_status === 2;

  const innerContent = useMemo(() => {
    if (trialEnded)
      return (
        <Box padding="100">
          This feature is not available on your current plan. You’ve already explored it during your 7-day trial. To
          continue using it, please <Button variant="plain">upgrade your plan</Button>.
        </Box>
      );
    return (
      <Box padding="100">
        This feature is not available on your current plan. Please upgrade or{' '}
        <Button variant="plain" onClick={showModal}>
          start your 7-day free trial
        </Button>{' '}
        to experience it fully.
      </Box>
    );
  }, [showModal, trialEnded]);

  return (
    <EmptyState
      heading="Unlock Premium Features"
      action={{ content: 'Upgrade Now', onAction: () => navigate(nav('/pricing')) }}
      secondaryAction={trialEnded ? undefined : { content: 'Start 7-day Trial', onAction: showModal }}
      image="https://cdn.shopify.com/s/files/1/0711/8759/5477/files/images.png?v=1738887336"
    >
      {innerContent}

      <Modal
        open={modalOpen}
        title="Start Your Free Trial"
        onClose={hideModal}
        primaryAction={{
          content: 'Start Trial',
          destructive: false,
          loading: isTrialStarting,
          onAction: async () => {
            await handleStartTrial();
            hideModal();
          },
        }}
        secondaryActions={[{ content: 'Cancel', loading: isTrialStarting, onAction: hideModal }]}
      >
        <Text>
          Your free trial will begin today and end at 23:59 (UTC) on {trialEndTime}. During this period, you will have
          full access to the Re-design Shopify Option feature. Are you sure you want to start your trial now?
        </Text>
      </Modal>
    </EmptyState>
  );
};

