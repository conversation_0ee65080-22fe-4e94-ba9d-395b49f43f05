import { Page, Layout, Card } from '@shopify/polaris';
import { useContext, useEffect, useState } from 'react';
import { AppDataContext } from '../../context/AppDataContext';
import {
  SectionAskReview,
  SectionRemoveTrademark,
  SectionFeature,
  SectionWarning,
  SectionCombinedListingsGuide,
  SectionColorSwatchGuide,
  SectionCustomTemplate,
  SectionConfigOption,
  TrialSwatchEmptyState,
  SectionTrialInfo,
  SectionChangeLog,
  SectionHelpCenter,
  SectionEcosystem,
  SectionFAQColorSwatch,
  SectionFAQCombinedListings,
} from './_components';

export default function HomePage() {
  const { shop, limitation, refetchData } = useContext(AppDataContext);

  const [selectedFeature, setSelectedFeature] = useState(
    window.sessionStorage.getItem('mtm_selected_feature') || 'product_group'
  );

  useEffect(() => {
    refetchData({ warning: true });
  }, [refetchData]);

  return (
    <Page
      title={
        shop?.shopify_data?.shop_owner
          ? `Welcome, ${shop?.shopify_data?.shop_owner} 🎉`
          : 'Grouptify: Combined Listings'
      }
    >
      <Layout>
        <SectionFeature
          title="Create Product Groups"
          subTitle="Boost Conversion with the Combined Listings feature"
          image="https://cdn.shopify.com/s/files/1/0711/8759/5477/files/image_01.png?v=1739334790"
          selected={selectedFeature === 'product_group'}
          faded={selectedFeature === 'color_swatch'}
          onClick={() => {
            setSelectedFeature('product_group');
            window.sessionStorage.setItem('mtm_selected_feature', 'product_group');
          }}
        />
        <SectionFeature
          title="Re-design Shopify Option"
          subTitle="Visualize Shopify's variant with color swatch, variant images and more"
          image="https://cdn.shopify.com/s/files/1/0711/8759/5477/files/image_02.png?v=1739334789"
          selected={selectedFeature === 'color_swatch'}
          faded={selectedFeature === 'product_group'}
          onClick={() => {
            setSelectedFeature('color_swatch');
            window.sessionStorage.setItem('mtm_selected_feature', 'color_swatch');
          }}
        />

        {selectedFeature === 'product_group' && (
          <>
            <SectionWarning appWarning />
            <SectionCombinedListingsGuide />
            <SectionRemoveTrademark />
            <SectionChangeLog />
            <SectionHelpCenter />
            <SectionAskReview />
            <SectionFAQCombinedListings />
            <SectionEcosystem />
          </>
        )}

        {selectedFeature === 'color_swatch' ? (
          limitation?.unlocked_swatch ? (
            <>
              <SectionTrialInfo />
              <SectionWarning appWarning />
              <SectionColorSwatchGuide />
              <SectionConfigOption />
              <SectionCustomTemplate />
              <SectionRemoveTrademark />
              <SectionChangeLog />
              <SectionHelpCenter />
              <SectionAskReview />
              <SectionFAQColorSwatch />
              <SectionEcosystem />
            </>
          ) : (
            <>
              <SectionTrialInfo />
              <Layout.Section>
                <Card>
                  <TrialSwatchEmptyState />
                </Card>
              </Layout.Section>
            </>
          )
        ) : null}

        <Layout.Section />
      </Layout>
    </Page>
  );
}

