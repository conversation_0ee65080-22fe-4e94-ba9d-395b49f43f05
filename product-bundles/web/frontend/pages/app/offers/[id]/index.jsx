import React from 'react';
import { ROUTES } from '@/constants/navigation';

import { Card, Page, Text } from '@shopify/polaris';
import { nav } from '@/utils/mixed';

export default function OfferDetail() {
  return (
    <Page title="Edit Quantity Breaks" backAction={{ content: 'Edit Offers', url: nav(ROUTES.OFFERS) }}>
      <Card>
        <Text>`EDIT PAGE` ... FORM EDIT HERE ...</Text>
      </Card>
    </Page>
  );
}

