import React from 'react';
import { useNavigate } from 'react-router-dom';

import { BlockStack, Box, Button, Card, Grid, InlineStack, Text } from '@shopify/polaris';
import { ROUTES } from '@/constants/navigation';
import { nav } from '@/utils/mixed';
import './SectionCreateCard.scss';

export const SectionCreateCard = ({ data }) => {
  const navigate = useNavigate();

  return (
    <Box className="wrapper">
      <Grid>
        {data.map(({ title, subTitle, buttonContent, imgSource, type, disabled }, index) => (
          <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 2, lg: 4, xl: 4 }} key={index}>
            <Box
              className={`card-box ${disabled ? 'disabled' : ''}`}
              onClick={() => navigate(nav(ROUTES.OFFER_CREATE(type)))}
            >
              <Card padding={0}>
                <img src={imgSource} alt={title} style={{ width: '100%', height: '253px', objectFit: 'cover' }} />
                <Box className="card-content">
                  <BlockStack gap={400}>
                    <Text as="h3" variant="headingMd">
                      {title}
                    </Text>
                    <Text as="span" tone="subdued">
                      {subTitle}
                    </Text>
                  </BlockStack>
                  <InlineStack align="end">
                    <Button disabled={disabled}>{buttonContent}</Button>
                  </InlineStack>
                </Box>
              </Card>
            </Box>
          </Grid.Cell>
        ))}
      </Grid>
    </Box>
  );
};

