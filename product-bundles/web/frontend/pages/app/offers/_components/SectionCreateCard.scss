.wrapper {
  .Polaris-ShadowBevel {
    height: 100%;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }
  }

  .card-box.disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
    filter: grayscale(60%);
    transition: 0.2s ease;
  }

  .card-box.disabled img {
    filter: grayscale(100%);
  }

  .card-box.disabled:hover {
    box-shadow: none;
  }

  .card-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 16px;
    min-height: 160px;
  }
}

