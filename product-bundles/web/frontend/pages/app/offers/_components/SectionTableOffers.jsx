import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

import {
  IndexTable,
  IndexFilters,
  useSetIndexFiltersMode,
  useIndexResourceState,
  Text,
  Badge,
  Card,
  Button,
  BlockStack,
  Box,
  InlineStack,
  Popover,
  ActionList,
  ButtonGroup,
} from '@shopify/polaris';
import { EditIcon } from '@shopify/polaris-icons';
import { ROUTES } from '@/constants/navigation';
import {
  ITEM_STRINGS,
  ACTION_LIST_PUBLISH,
  ACTION_LIST_UNPUBLISH,
  RESOURCE_NAME,
  HEADING_TABLE_OFFER,
  MAX_PAGE_ITEM,
  OFFER_STATUS,
} from '@/constants/offer/OfferList';
import './SectionTableOffers.scss';
import { nav } from '@/utils/mixed';

export const SectionTableOffers = () => {
  const navigate = useNavigate();

  const [selected, setSelected] = useState(0);
  const [queryValue, setQueryValue] = useState('');
  const [activePopoverId, setActivePopoverId] = useState(null);

  const { mode, setMode } = useSetIndexFiltersMode();
  const { selectedResources } = useIndexResourceState(offerData);

  const handleFiltersQueryChange = useCallback((value) => setQueryValue(value), []);

  const tabs = ITEM_STRINGS.map((item, index) => ({
    content: item,
    index,
    onAction: () => {},
    id: `${item}-${index}`,
    isLocked: index === 0,
  }));

  const rowMarkup = offerData.map(({ id, name, type, revenue, impressions, status }, index) => {
    const isActive = activePopoverId === id;

    return (
      <IndexTable.Row
        id={id}
        key={id}
        selected={selectedResources.includes(id)}
        position={index}
        onClick={() => navigate(nav(ROUTES.OFFER_DETAIL(id)))}
      >
        <IndexTable.Cell>
          <BlockStack gap={100}>
            <Box className="hover-name">
              <Text variant="bodyMd" as="span" truncate>
                {name}
              </Text>
            </Box>
            <Box>
              <Badge>
                <Text as="span" variant="bodyMd">
                  {type}
                </Text>
              </Badge>
            </Box>
          </BlockStack>
        </IndexTable.Cell>
        <IndexTable.Cell>
          <Text alignment="end">{impressions}</Text>
        </IndexTable.Cell>
        <IndexTable.Cell>
          <Text alignment="end">{revenue}</Text>
        </IndexTable.Cell>
        <IndexTable.Cell>
          <Text alignment="end">
            <Badge tone={OFFER_STATUS[status]?.tone}>{OFFER_STATUS[status]?.title}</Badge>
          </Text>
        </IndexTable.Cell>
        <IndexTable.Cell>
          <InlineStack align="end">
            <Box className="button-group">
              <ButtonGroup variant="segmented">
                <Button icon={EditIcon} onClick={() => navigate(nav(ROUTES.OFFER_DETAIL(id)))}>
                  Edit
                </Button>
                <Popover
                  active={isActive}
                  activator={
                    <Button
                      onClick={(e) => {
                        e.stopPropagation(), setActivePopoverId(isActive ? null : id);
                      }}
                      disclosure
                    />
                  }
                  preferredAlignment={'right'}
                  autofocusTarget="first-node"
                  onClose={() => setActivePopoverId(null)}
                >
                  <ActionList
                    actionRole="menuitem"
                    items={status === 0 ? ACTION_LIST_PUBLISH : ACTION_LIST_UNPUBLISH}
                  />
                </Popover>
              </ButtonGroup>
            </Box>
          </InlineStack>
        </IndexTable.Cell>
      </IndexTable.Row>
    );
  });

  return (
    <Card padding={0}>
      <IndexFilters
        queryValue={queryValue}
        queryPlaceholder="Searching in all"
        onQueryChange={handleFiltersQueryChange}
        onQueryClear={() => setQueryValue('')}
        cancelAction={{
          onAction: () => {},
          disabled: false,
          loading: false,
        }}
        tabs={tabs}
        selected={selected}
        onSelect={setSelected}
        canCreateNewView={false}
        filters={[]}
        appliedFilters={[]}
        mode={mode}
        setMode={setMode}
      />
      <IndexTable
        selectable={false}
        resourceName={RESOURCE_NAME}
        itemCount={offerData.length}
        headings={HEADING_TABLE_OFFER}
        pagination={{
          hasNext: offerData.length > MAX_PAGE_ITEM,
          onNext: () => {},
        }}
      >
        {rowMarkup}
      </IndexTable>
    </Card>
  );
};

// FAKE DATA
const offerData = [
  {
    id: '1020',
    name: (
      <Text as="span" variant="bodyMd" fontWeight="semibold">
        Bundle offer
      </Text>
    ),
    date: 'Jul 20 at 4:34pm',
    revenue: 0,
    impressions: 0,
    type: 'volume discounts',
    status: 1,
  },
  {
    id: '1023',
    name: (
      <Text as="span" variant="bodyMd" fontWeight="semibold">
        Buy 2 and get 10% off
      </Text>
    ),
    date: 'Jul 20 at 4:34pm',
    revenue: 0,
    impressions: 0,
    type: 'quantity discounts',
    status: 0,
  },
  {
    id: '1024',
    name: (
      <Text as="span" variant="bodyMd" fontWeight="semibold">
        Buy 2 and get 10% off
      </Text>
    ),
    date: 'Jul 20 at 4:34pm',
    revenue: 0,
    impressions: 0,
    type: 'quantity discounts',
    status: 1,
  },
  {
    id: '1025',
    name: (
      <Text as="span" variant="bodyMd" fontWeight="semibold">
        Buy 2 and get 10% off
      </Text>
    ),
    revenue: 0,
    impressions: 0,
    date: 'Jul 20 at 4:34pm',
    type: 'quantity discounts',
    status: 1,
  },
];

