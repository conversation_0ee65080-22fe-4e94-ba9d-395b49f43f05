import React from 'react';

import { Controller, useFormContext } from 'react-hook-form';
import { BlockStack, Card, Checkbox, Text } from '@shopify/polaris';

const COMBINE_OPTIONS = [
  { label: 'Order discounts', name: 'settings.combined_with_order_discount' },
  { label: 'Product discounts', name: 'settings.combined_with_product_discount' },
  { label: 'Shipping discounts', name: 'settings.combined_with_shipping_discount' },
];

const AdvancedCombined = () => {
  const { control } = useFormContext();

  return (
    <Card>
      <BlockStack gap={400}>
        <Text variant="headingMd" as="h2">
          Combinations
        </Text>

        <BlockStack>
          <Text variant="bodyMd" as="h2">
            Deal combines with other store discounts:
          </Text>
          {COMBINE_OPTIONS.map((item) => (
            <Controller
              key={item.name}
              name={item.name}
              control={control}
              render={({ field }) => <Checkbox label={item.label} checked={field.value} onChange={field.onChange} />}
            />
          ))}
        </BlockStack>
      </BlockStack>
    </Card>
  );
};

export default AdvancedCombined;

