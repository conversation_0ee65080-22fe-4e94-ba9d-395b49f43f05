import React from 'react';

import { BlockStack, Box, Card, Checkbox, Grid, RadioButton, Select, Text, TextField } from '@shopify/polaris';
import { Controller, useFormContext, useWatch } from 'react-hook-form';
import {
  PRICE_COMPARE_TYPE_OPTIONS,
  PRICE_ROUNDING_OPTIONS,
  PRICE_UNIT_OPTIONS,
} from '@/constants/offer/OfferAdvanced';

export default function AdvancedPrice() {
  const { control } = useFormContext();
  const compareType = useWatch({ control, name: 'settings.compare_at_price_type' });
  const totalUseOffer = useWatch({ control, name: 'settings.limit_total_uses_of_offer' });

  return (
    <Card>
      <BlockStack gap={400}>
        <Text variant="headingMd" as="h2">
          Price settings
        </Text>

        <BlockStack>
          <Text variant="headingSm" as="p">
            Show price unit
          </Text>
          <Controller
            name="settings.show_price_per_unit"
            control={control}
            render={({ field }) => {
              return PRICE_UNIT_OPTIONS.map((option) => (
                <RadioButton
                  id={option.id}
                  name={option.id}
                  key={option.id}
                  label={option.label}
                  checked={field.value === option.value}
                  onChange={() => field.onChange(option.value)}
                />
              ));
            }}
          />
        </BlockStack>

        <Grid columns={{ xs: 1, sm: 3, md: 3, lg: 3, xl: 3 }}>
          {/* Price rounding */}
          <BlockStack gap={200}>
            <Text variant="headingSm" as="p">
              Price rounding
            </Text>
            <Controller
              name="settings.price_rounding"
              control={control}
              render={({ field }) => (
                <Select labelHidden onChange={field.onChange} value={field.value} options={PRICE_ROUNDING_OPTIONS} />
              )}
            />
          </BlockStack>

          {/* Type compare */}
          <BlockStack gap={200}>
            <Text variant="headingSm" as="p">
              Type compare
            </Text>
            <Controller
              name="settings.compare_at_price_type"
              control={control}
              render={({ field }) => (
                <Select
                  labelHidden
                  onChange={field.onChange}
                  value={field.value}
                  options={PRICE_COMPARE_TYPE_OPTIONS}
                />
              )}
            />
          </BlockStack>

          {/* Custom compare price */}
          <Box className={compareType !== 'show_custom_price' ? 'is-disabled' : ''}>
            <BlockStack gap={200}>
              <Text variant="headingSm" as="p">
                Compare price
              </Text>
              <Controller
                name="settings.custom_compare_at_price"
                control={control}
                render={({ field }) => (
                  <TextField type="number" prefix="$" min={0} value={field.value} onChange={field.onChange} />
                )}
              />
            </BlockStack>
          </Box>
        </Grid>

        <BlockStack gap={200}>
          <Text variant="headingSm" as="p">
            Limit settings
          </Text>
          <BlockStack>
            <Controller
              name="settings.limit_one_use_per_customer"
              control={control}
              render={({ field }) => (
                <Checkbox label="Limit to one use per customer" checked={field.value} onChange={field.onChange} />
              )}
            />
            <Controller
              name="settings.limit_total_uses_of_offer"
              control={control}
              render={({ field }) => (
                <Checkbox
                  label="Limit number of times this discount can be used in total"
                  checked={field.value}
                  onChange={field.onChange}
                  helpText={
                    <Box className={totalUseOffer ? 'box-text' : 'box-text is-disabled'}>
                      <Controller
                        name="settings.total_uses"
                        control={control}
                        render={({ field: fieldText }) => (
                          <TextField type="number" min={0} value={fieldText.value} onChange={fieldText.onChange} />
                        )}
                      />
                    </Box>
                  }
                />
              )}
            />
          </BlockStack>
        </BlockStack>
      </BlockStack>
    </Card>
  );
}

