import React from 'react';

import { BlockStack, Box, Button, Card, Icon, InlineStack, Select, Text, TextField } from '@shopify/polaris';
import { Controller, useFormContext, useWatch } from 'react-hook-form';

import { HideIcon, ViewIcon, DeleteIcon } from '@shopify/polaris-icons';
import { DISCOUNT_OPTIONS, DISCOUNT_VALUES } from '@/constants/offer/OfferBlock';

export default function TireTab({ index, onRemove, isHideRemove }) {
  const { control, getValues, setValue } = useFormContext();
  const tier = useWatch({ control, name: `tiers.${index}` });
  const tireDiscountType = useWatch({ control, name: `tiers.${index}.discount_type` });

  const handleToggle = (field) => {
    const current = tier?.[field];
    setValue(`tiers.${index}.${field}`, !current, { shouldDirty: true });
  };

  return (
    <Card background="bg-surface-secondary">
      <BlockStack gap={400}>
        <InlineStack align="space-between">
          <Text variant="headingMd" as="h2">{`Tier ${index + 1}`}</Text>
          <Button onClick={onRemove} icon={DeleteIcon} disabled={isHideRemove} />
        </InlineStack>

        {/* Title */}
        <BlockStack gap={200}>
          <Text variant="headingMd" as="h2">
            Title
          </Text>
          <Controller
            name={`tiers.${index}.title`}
            control={control}
            rules={{
              required: 'Title is required',
            }}
            render={({ field, fieldState }) => (
              <TextField
                placeholder="Enter title"
                helpText="This is the title of the tier offer."
                autoComplete="off"
                value={field.value}
                onChange={field.onChange}
                disabled={!tier?.show_title}
                error={fieldState.error?.message}
              />
            )}
          />
        </BlockStack>

        {/* Quantity */}
        <BlockStack gap={200}>
          <Text variant="headingMd" as="h2">
            Quantity
          </Text>
          <InlineStack gap={200} wrap={false}>
            <Box width="100%">
              <Controller
                name={`tiers.${index}.quantity_min`}
                control={control}
                rules={{
                  required: 'Min quantity is required',
                  min: { value: 1, message: 'Min quantity must be at least 1' },
                }}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    label="Min Quantity"
                    autoComplete="off"
                    type="number"
                    min={1}
                    placeholder="Min quantity"
                    value={field.value}
                    onChange={field.onChange}
                    error={error?.message}
                  />
                )}
              />
            </Box>
            <Box width="100%">
              <Controller
                name={`tiers.${index}.quantity_max`}
                control={control}
                rules={{
                  min: { value: 0, message: 'Max quantity must be >= Min quantity' },
                  validate: (value) => {
                    const min = Number(getValues(`tiers.${index}.quantity_min`));
                    return Number(value) === 0 || Number(value) >= min || 'Max must be 0 or greater than Min quantity';
                  },
                }}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    label="Max Quantity"
                    autoComplete="off"
                    type="number"
                    min={0}
                    placeholder="Max quantity"
                    value={field.value === null ? '' : field.value}
                    onChange={field.onChange}
                    error={error?.message}
                  />
                )}
              />
            </Box>
          </InlineStack>
        </BlockStack>

        {/* Discount */}
        <BlockStack gap={200}>
          <Text variant="headingMd" as="h2">
            Discount
          </Text>
          <InlineStack gap={200} wrap={false}>
            <Box width="100%">
              <Controller
                name={`tiers.${index}.discount_type`}
                control={control}
                render={({ field }) => (
                  <Select
                    label="Discount Type"
                    options={DISCOUNT_OPTIONS.map((option) => ({
                      label: option.replace(/_/g, ' '),
                      value: option,
                    }))}
                    value={field.value}
                    onChange={field.onChange}
                  />
                )}
              />
            </Box>
            <Box width="100%">
              <Controller
                name={`tiers.${index}.discount_value`}
                control={control}
                rules={{
                  required: { value: true, message: 'Discount value is required' },
                  min: { value: 0, message: 'Discount value must be at least 0' },
                  validate: {
                    maxPercentage: (value) =>
                      tireDiscountType !== 'percentage_discount' || value <= 100 || 'Percentage must be 0 to 100%',
                  },
                }}
                render={({ field, fieldState }) => (
                  <TextField
                    label="Discount Value"
                    type="number"
                    min={0}
                    autoComplete="off"
                    onChange={field.onChange}
                    value={tireDiscountType === DISCOUNT_VALUES.NONE ? '' : field.value}
                    disabled={tireDiscountType === DISCOUNT_VALUES.NONE}
                    error={fieldState.error?.message}
                    prefix={
                      tireDiscountType === DISCOUNT_VALUES.PERCENTAGE
                        ? '%'
                        : tireDiscountType === DISCOUNT_VALUES.NONE
                          ? ''
                          : '$'
                    }
                  />
                )}
              />
            </Box>
          </InlineStack>
        </BlockStack>

        {/* Badge */}
        <BlockStack gap={200}>
          <Text variant="headingMd" as="h2">
            Badge Text
          </Text>
          <Controller
            name={`tiers.${index}.badge_text`}
            control={control}
            render={({ field }) => (
              <TextField
                placeholder="Enter badge"
                autoComplete="off"
                value={field.value}
                onChange={field.onChange}
                disabled={!tier?.show_badge}
                suffix={
                  <Box as="span" onClick={() => handleToggle('show_badge')} style={{ cursor: 'pointer' }}>
                    <Icon source={!tier?.show_badge ? HideIcon : ViewIcon} color="subdued" />
                  </Box>
                }
              />
            )}
          />
        </BlockStack>
      </BlockStack>
    </Card>
  );
}

