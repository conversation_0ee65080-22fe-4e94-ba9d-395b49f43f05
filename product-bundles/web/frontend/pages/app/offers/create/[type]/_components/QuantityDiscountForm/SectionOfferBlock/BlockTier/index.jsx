import React, { useCallback, useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';

import { BlockStack, Button, Card, InlineStack, Text, Popover, ActionList } from '@shopify/polaris';
import { PlusIcon } from '@shopify/polaris-icons';
import { DISCOUNT_VALUES } from '@/constants/offer/OfferBlock';
import TierTab from './TierTab';

export default function OfferTier() {
  const { control, getValues } = useFormContext();
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'tiers',
  });

  const TABS_LIMIT = 5;
  const [selected, setSelected] = useState(0);
  const [lastDropdownSelected, setLastDropdownSelected] = useState(null);
  const [dropdownActive, setDropdownActive] = useState(false);

  const dropdownTabs = fields.slice(TABS_LIMIT);
  const visibleTabs = fields.slice(0, TABS_LIMIT).map((_, index) => ({
    id: `tier-${index + 1}`,
    content: `Tier ${index + 1}`,
  }));

  const getNextMin = (lastMin, lastMax) => {
    if (lastMin > lastMax && lastMax > 0) return lastMin + 1;
    if (lastMin > 0 && lastMax === 0) return lastMin + 1;
    if (lastMax > 0 && lastMin === 0) return lastMax + 1;
    if (lastMin === 0 && lastMax === 0) return 1;
    return lastMax > 0 ? lastMax + 1 : lastMin + 1;
  };

  const handleAddTier = () => {
    const lastMin = getValues(`tiers.${fields.length - 1}.quantity_min`) || 1;
    const lastMax = getValues(`tiers.${fields.length - 1}.quantity_max`) || 0;
    const nextMin = fields.length > 0 ? getNextMin(Number(lastMin), Number(lastMax)) : 1;

    append({
      title: `Basic Tier ${selected + 2}`,
      badge_text: '',
      show_title: true,
      show_badge: true,
      quantity_min: nextMin,
      quantity_max: 0,
      discount_type: DISCOUNT_VALUES.NONE,
      discount_value: 0,
    });

    setLastDropdownSelected(fields.length);
    setSelected(fields.length);
  };

  const handleTabChange = useCallback((index) => {
    setSelected(index);
  }, []);

  return (
    <Card>
      <BlockStack gap={400}>
        <InlineStack align="space-between">
          <Text variant="headingMd" as="h2">
            Offer Tiers
          </Text>
          <Button variant="plain" icon={PlusIcon} onClick={handleAddTier}>
            Add Tier
          </Button>
        </InlineStack>

        <InlineStack wrap={false} align="start" gap={100}>
          {visibleTabs.map((tab, index) => (
            <Button key={tab.id} variant="tertiary" pressed={selected === index} onClick={() => handleTabChange(index)}>
              {tab.content}
            </Button>
          ))}

          {/* Visible dropdown tab when TAB reach to LIMIT */}
          {dropdownTabs.length > 0 && (
            <Popover
              active={dropdownActive}
              activator={
                <Button
                  variant="tertiary"
                  pressed={selected >= TABS_LIMIT}
                  onClick={() => setDropdownActive((prev) => !prev)}
                  disclosure
                >
                  {selected >= TABS_LIMIT
                    ? `Tier ${selected + 1}`
                    : lastDropdownSelected !== null
                      ? `Tier ${lastDropdownSelected + 1}`
                      : 'More Tiers'}
                </Button>
              }
              onClose={() => setDropdownActive(false)}
            >
              <ActionList
                items={dropdownTabs.map((_, i) => ({
                  content: `Tier ${i + TABS_LIMIT + 1}`,
                  onAction: () => {
                    const actualIndex = i + TABS_LIMIT;
                    setSelected(actualIndex);
                    setLastDropdownSelected(actualIndex);
                    setDropdownActive(false);
                  },
                }))}
              />
            </Popover>
          )}
        </InlineStack>

        {fields.length > 0 && (
          // Render the selected TIER TAB
          <TierTab
            key={`tier-tab-${selected}`}
            index={selected}
            isHideRemove={fields.length < 2}
            onRemove={() => {
              remove(selected);
              setLastDropdownSelected(null);
              setSelected((prev) => (prev > 0 ? prev - 1 : 0));
            }}
          />
        )}
      </BlockStack>
    </Card>
  );
}

