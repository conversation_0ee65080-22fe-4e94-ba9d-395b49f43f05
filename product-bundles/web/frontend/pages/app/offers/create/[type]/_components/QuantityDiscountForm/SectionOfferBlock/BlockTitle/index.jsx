import React from 'react';
import { Controller, useFormContext, useWatch } from 'react-hook-form';

import { BlockStack, Box, Card, Icon, Text, TextField } from '@shopify/polaris';
import { HideIcon, ViewIcon } from '@shopify/polaris-icons';
import { FIELDS_BLOCK_TITLE } from '@/constants/offer/OfferBlock';

export default function SectionTitle() {
  const { control, setValue } = useFormContext();

  const generalValues = useWatch({
    control,
    name: 'general',
  });

  return (
    <Card>
      <BlockStack gap={400}>
        {FIELDS_BLOCK_TITLE.map(({ name, label, placeholder, showKey }) => {
          const isHidden = generalValues?.[showKey] === false;

          const toggleVisibility = () => {
            setValue(`general.${showKey}`, !generalValues?.[showKey], { shouldDirty: true });
          };

          return (
            <BlockStack gap={200} key={name}>
              <Text variant="headingMd" as="h2">
                {label}
              </Text>
              <Controller
                name={name}
                control={control}
                render={({ field }) => (
                  <TextField
                    placeholder={placeholder}
                    value={field.value ?? ''}
                    onChange={field.onChange}
                    autoComplete="off"
                    disabled={isHidden}
                    suffix={
                      <Box as="span" onClick={toggleVisibility} style={{ cursor: 'pointer' }}>
                        <Icon source={isHidden ? HideIcon : ViewIcon} color="subdued" />
                      </Box>
                    }
                  />
                )}
              />
            </BlockStack>
          );
        })}
      </BlockStack>
    </Card>
  );
}

