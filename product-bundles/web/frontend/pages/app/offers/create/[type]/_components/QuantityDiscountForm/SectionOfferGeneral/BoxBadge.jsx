import React, { useState } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

import { XSmallIcon, PlusIcon, DeleteIcon } from '@shopify/polaris-icons';
import { BlockStack, Box, Button, InlineStack, Text, TextField, Badge } from '@shopify/polaris';
import FieldSelector from './FieldSelector';
import {
  CUSTOM_PRODUCT_VALUES,
  OPERATOR_OPTIONS_TAGS,
  OPERATOR_OPTIONS_TYPE_VENDOR,
} from '@/constants/offer/OfferGeneral';

const BoxBadge = ({ index, fieldKey, label, value = [], onChange, error, onDelete, conditionType }) => {
  const { control } = useFormContext();
  const [inputValue, setInputValue] = useState('');

  const values =
    useWatch({
      control,
      name: `${conditionType}.${index}.${fieldKey}`,
    }) || [];

  const handleAdd = () => {
    const newVal = inputValue.trim();
    if (!newVal || value.includes(newVal)) {
      setInputValue('');
      return;
    }

    onChange([...value, newVal]);
    setInputValue('');
  };

  const handleRemove = (val) => {
    onChange(value.filter((v) => v !== val));
  };

  return (
    <Box padding={300} borderColor="border-secondary" borderWidth="025" borderRadius="300">
      <BlockStack gap={400}>
        <InlineStack align="space-between" blockAlign="center">
          <Text variant="headingMd" as="h2">
            {label}
          </Text>
          <Button variant="plain" icon={DeleteIcon} onClick={onDelete} />
        </InlineStack>

        <InlineStack gap={300} blockAlign="start" wrap={false}>
          <FieldSelector
            name={`${conditionType}.${index}.operator`}
            options={
              fieldKey === CUSTOM_PRODUCT_VALUES.TAG.FIELD ? OPERATOR_OPTIONS_TAGS : OPERATOR_OPTIONS_TYPE_VENDOR
            }
          />

          <Box
            width="100%"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleAdd();
              }
            }}
          >
            <TextField
              value={inputValue}
              onChange={(val) => setInputValue(val)}
              placeholder={`Enter ${label.toLowerCase()}`}
              autoComplete="off"
              connectedRight={<Button icon={PlusIcon} onClick={handleAdd} disabled={!inputValue.trim()} size="large" />}
              error={error}
            />
          </Box>
        </InlineStack>

        {values.length > 0 && (
          <InlineStack gap={200} wrap>
            {values.map((val, index) => (
              <Badge key={`${val}-${index}`}>
                <InlineStack gap={150} blockAlign="center">
                  <Text>{val}</Text>
                  <Button icon={XSmallIcon} variant="plain" size="micro" onClick={() => handleRemove(val)} />
                </InlineStack>
              </Badge>
            ))}
          </InlineStack>
        )}
      </BlockStack>
    </Box>
  );
};

export default BoxBadge;

