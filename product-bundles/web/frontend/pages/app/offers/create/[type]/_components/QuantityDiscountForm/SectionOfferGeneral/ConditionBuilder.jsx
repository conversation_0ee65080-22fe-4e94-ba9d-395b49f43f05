import React, { useCallback, useState } from 'react';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';

import { ActionList, Badge, BlockStack, Button, InlineStack, Popover } from '@shopify/polaris';

const ConditionBuilder = ({ name, itemList, renderField, defaultItemData }) => {
  const [popoverActive, setPopoverActive] = useState(false);
  const { control, clearErrors } = useFormContext();
  const { fields, append, remove } = useFieldArray({ control, name });

  const togglePopoverActive = useCallback(() => {
    setPopoverActive((prev) => !prev);
    const checkName = name === 'customer_conditions' ? 'general.apply_to_customers' : 'general.apply_to_products';
    clearErrors(checkName);
  }, []);

  const activator = (
    <Button onClick={togglePopoverActive} disclosure fullWidth>
      Add rule
    </Button>
  );

  return (
    <BlockStack gap={400}>
      {fields.map((field, index) => {
        const isNotFirst = index !== 0;

        return (
          <BlockStack gap={400} key={field.id}>
            {isNotFirst && (
              <InlineStack align="center" blockAlign="center" gap={200}>
                <Badge status="attention">AND</Badge>
              </InlineStack>
            )}

            {renderField(field, index, remove)}
          </BlockStack>
        );
      })}

      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Popover
            fullWidth
            active={popoverActive}
            activator={activator}
            autofocusTarget="first-node"
            onClose={togglePopoverActive}
          >
            <ActionList
              actionRole="menuitem"
              items={itemList.map((item) => {
                const isPicked = field.value?.some((v) => v.type === item.value);

                return {
                  ...item,
                  disabled: isPicked,
                  onAction: () => {
                    append(defaultItemData(item.value));
                    togglePopoverActive();
                  },
                };
              })}
            />
          </Popover>
        )}
      />
    </BlockStack>
  );
};

export default ConditionBuilder;

