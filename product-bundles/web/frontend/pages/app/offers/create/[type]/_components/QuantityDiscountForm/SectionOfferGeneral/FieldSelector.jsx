import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import { Box, Select } from '@shopify/polaris';

const FieldSelector = ({ name, options, label }) => {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Box minWidth="120px">
          <Select onChange={field.onChange} value={field.value} options={options} label={label ?? undefined} />
        </Box>
      )}
    />
  );
};

export default FieldSelector;

