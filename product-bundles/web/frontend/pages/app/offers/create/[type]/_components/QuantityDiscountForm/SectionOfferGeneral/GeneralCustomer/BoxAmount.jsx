import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import { DeleteIcon } from '@shopify/polaris-icons';
import { BlockStack, Box, Button, InlineStack, Text, TextField } from '@shopify/polaris';

const BoxAmount = ({ index, type, remove, fieldMinKey, fieldMaxKey, label }) => {
  const { control, getValues } = useFormContext();

  return (
    <Box padding={300} borderColor="border-secondary" borderWidth="025" borderRadius="300">
      <BlockStack gap={400}>
        <InlineStack align="space-between" blockAlign="center">
          <Text variant="headingMd" as="h2">
            {label}
          </Text>
          <Button variant="plain" icon={DeleteIcon} onClick={() => remove(index)} />
        </InlineStack>
        <InlineStack gap={200} wrap={false}>
          <Box width="100%">
            <Controller
              name={`customer_conditions.${index}.${fieldMinKey}`}
              control={control}
              rules={{
                required: 'Min quantity is required',
                min: { value: 0, message: 'Min quantity must be at least 0' },
              }}
              render={({ field, fieldState }) => (
                <TextField
                  min={0}
                  type="number"
                  value={field.value}
                  onChange={field.onChange}
                  error={fieldState.error?.message}
                  label={type === 'amount' ? 'Minimum spend' : 'Minimum items'}
                  prefix={type === 'amount' && '$'}
                  autoComplete="off"
                />
              )}
            />
          </Box>
          <Box width="100%">
            <Controller
              name={`customer_conditions.${index}.${fieldMaxKey}`}
              control={control}
              rules={{
                validate: (value) => {
                  const min = Number(getValues(`customer_conditions.${index}.${fieldMinKey}`));
                  return Number(value) === 0 || Number(value) >= min || 'Max must be 0 or greater than Min quantity';
                },
              }}
              render={({ field, fieldState }) => (
                <TextField
                  min={0}
                  type="number"
                  value={field.value}
                  onChange={field.onChange}
                  error={fieldState.error?.message}
                  label={type === 'amount' ? 'Maximum spend' : 'Maximum items'}
                  autoComplete="off"
                  prefix={type === 'amount' && '$'}
                />
              )}
            />
          </Box>
        </InlineStack>
      </BlockStack>
    </Box>
  );
};

export default BoxAmount;

