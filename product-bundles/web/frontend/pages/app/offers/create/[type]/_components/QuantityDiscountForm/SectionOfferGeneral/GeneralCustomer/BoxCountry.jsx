import React, { useCallback, useMemo, useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import { SearchIcon, DeleteIcon } from '@shopify/polaris-icons';
import { BlockStack, Box, Button, Combobox, Icon, InlineStack, Listbox, Tag, Text } from '@shopify/polaris';
import { COUNTRY_OPTIONS } from '@/constants/country';
import FieldSelector from '../FieldSelector';
import {
  CUSTOM_CUSTOMER_VALUES,
  OPERATOR_OPTIONS_TAGS,
  OPERATOR_OPTIONS_TYPE_VENDOR,
} from '@/constants/offer/OfferGeneral';

const CountryBox = ({ index, remove, fieldKey, label }) => {
  const { control } = useFormContext();
  const deselectedOptions = useMemo(() => COUNTRY_OPTIONS, []);

  const [inputValue, setInputValue] = useState('');
  const [options, setOptions] = useState(deselectedOptions);

  const escapeSpecialRegExCharacters = useCallback((value) => value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), []);

  const updateText = useCallback(
    (value) => {
      setInputValue(value);

      if (value === '') {
        setOptions(deselectedOptions);
        return;
      }

      const filterRegex = new RegExp(escapeSpecialRegExCharacters(value), 'i');
      const resultOptions = deselectedOptions.filter((option) => option.label.match(filterRegex));
      setOptions(resultOptions);
    },
    [deselectedOptions, escapeSpecialRegExCharacters]
  );

  return (
    <Controller
      name={`customer_conditions.${index}.${fieldKey}`}
      control={control}
      defaultValue={[]}
      rules={{
        validate: (value) => (value && value.length > 0 ? true : 'Please add at least one customer country'),
      }}
      render={({ field, fieldState }) => {
        const selectedOptions = field.value || [];

        const updateSelection = (selected) => {
          if (selectedOptions.includes(selected)) {
            field.onChange(selectedOptions.filter((option) => option !== selected));
          } else {
            field.onChange([...selectedOptions, selected]);
          }
        };

        const removeTag = (tag) => () => {
          const updated = selectedOptions.filter((t) => t !== tag);
          field.onChange(updated);
        };

        const tagsMarkup = selectedOptions.map((option) => (
          <Tag key={`option-${option}`} onRemove={removeTag(option)}>
            {option}
          </Tag>
        ));

        const optionsMarkup =
          options.length > 0
            ? options.map(({ label, value }) => (
                <Listbox.Option
                  key={value}
                  value={value}
                  selected={selectedOptions.includes(value)}
                  accessibilityLabel={label}
                >
                  {label}
                </Listbox.Option>
              ))
            : null;

        return (
          <Box padding={300} borderColor="border-secondary" borderWidth="025" borderRadius="300">
            <BlockStack gap={400}>
              <InlineStack align="space-between" blockAlign="center">
                <Text variant="headingMd" as="h2">
                  {label}
                </Text>
                <Button variant="plain" icon={DeleteIcon} onClick={() => remove(index)} />
              </InlineStack>

              <InlineStack gap={300} blockAlign="start" wrap={false}>
                <FieldSelector
                  name={`customer_conditions.${index}.operator`}
                  options={
                    fieldKey === CUSTOM_CUSTOMER_VALUES.TAG.FIELD ? OPERATOR_OPTIONS_TAGS : OPERATOR_OPTIONS_TYPE_VENDOR
                  }
                />
                <Box width="100%">
                  <Combobox
                    allowMultiple
                    activator={
                      <Combobox.TextField
                        prefix={<Icon source={SearchIcon} />}
                        onChange={updateText}
                        label="Search countries"
                        labelHidden
                        value={inputValue}
                        placeholder="Search countries"
                        autoComplete="off"
                        error={fieldState.error?.message}
                      />
                    }
                  >
                    {optionsMarkup ? <Listbox onSelect={updateSelection}>{optionsMarkup}</Listbox> : null}
                  </Combobox>
                </Box>
              </InlineStack>

              <InlineStack gap={200} wrap>
                {tagsMarkup}
              </InlineStack>
            </BlockStack>
          </Box>
        );
      }}
    />
  );
};

export default CountryBox;

