import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import { DeleteIcon } from '@shopify/polaris-icons';
import { BlockStack, Box, Button, InlineStack, Select, Text } from '@shopify/polaris';
import { LOGIN_STATUS_OPTIONS } from '@/constants/offer/OfferGeneral';

const LoginBox = ({ index, remove, fieldKey, label }) => {
  const { control } = useFormContext();

  return (
    <Box padding={300} borderColor="border-secondary" borderWidth="025" borderRadius="300">
      <BlockStack gap={400}>
        <InlineStack align="space-between" blockAlign="center">
          <Text variant="headingMd" as="h2">
            {label}
          </Text>
          <Button variant="plain" icon={DeleteIcon} onClick={() => remove(index)} />
        </InlineStack>

        <Controller
          name={`customer_conditions.${index}.${fieldKey}`}
          control={control}
          render={({ field }) => (
            <Select options={LOGIN_STATUS_OPTIONS} onChange={field.onChange} value={field.value} />
          )}
        />
      </BlockStack>
    </Box>
  );
};

export default LoginBox;

