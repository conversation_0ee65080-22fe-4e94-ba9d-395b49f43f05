import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import BoxLogin from './BoxLogin';
import BoxBadge from '../BoxBadge';
import BoxCountry from './BoxCountry';
import ConditionBuilder from '../ConditionBuilder';
import { CUSTOM_CUSTOMER_ITEMS, CUSTOM_CUSTOMER_VALUES } from '@/constants/offer/OfferGeneral';
import BoxAmount from './BoxAmount';

const CustomerBlockStack = () => {
  const { control } = useFormContext();
  const renderField = (field, index, remove) => {
    if (field.type === CUSTOM_CUSTOMER_VALUES.LOGIN.TYPE) {
      return (
        <BoxLogin index={index} remove={remove} fieldKey={CUSTOM_CUSTOMER_VALUES.LOGIN.FIELD} label="Login status" />
      );
    }

    if (field.type === CUSTOM_CUSTOMER_VALUES.TAG.TYPE) {
      return (
        <Controller
          name={`customer_conditions.${index}.${CUSTOM_CUSTOMER_VALUES.TAG.FIELD}`}
          control={control}
          rules={{
            validate: (value) => (value && value.length > 0 ? true : 'Please add at least one customer tag'),
          }}
          render={({ field, fieldState }) => (
            <BoxBadge
              index={index}
              fieldKey={CUSTOM_CUSTOMER_VALUES.TAG.FIELD}
              conditionType="customer_conditions"
              error={fieldState.error?.message}
              onDelete={() => remove(index)}
              onChange={field.onChange}
              value={field.value}
              label="Tag customer"
            />
          )}
        />
      );
    }

    if (field.type === CUSTOM_CUSTOMER_VALUES.COUNTRY.TYPE) {
      return (
        <BoxCountry index={index} remove={remove} fieldKey={CUSTOM_CUSTOMER_VALUES.COUNTRY.FIELD} label="Country" />
      );
    }

    if (field.type === CUSTOM_CUSTOMER_VALUES.AMOUNT.TYPE) {
      return (
        <BoxAmount
          type="amount"
          label="Amount spent (Only Logged in users)"
          index={index}
          remove={remove}
          fieldMinKey={CUSTOM_CUSTOMER_VALUES.AMOUNT.MIN}
          fieldMaxKey={CUSTOM_CUSTOMER_VALUES.AMOUNT.MAX}
        />
      );
    }

    if (field.type === CUSTOM_CUSTOMER_VALUES.ORDER_COUNT.TYPE) {
      return (
        <BoxAmount
          type="order"
          label="Orders completed (Only Logged in users)"
          index={index}
          remove={remove}
          fieldMinKey={CUSTOM_CUSTOMER_VALUES.ORDER_COUNT.MIN}
          fieldMaxKey={CUSTOM_CUSTOMER_VALUES.ORDER_COUNT.MAX}
        />
      );
    }

    return null;
  };

  const defaultItemData = (type) => ({
    type,
    operator: type === CUSTOM_CUSTOMER_VALUES.TAG.TYPE ? 'include_all' : 'is',
    login_status: '',
    customer_tags: [],
    customer_countries: [],
    customer_min_amount_spent: 0,
    customer_max_amount_spent: 0,
    customer_min_order_count: 0,
    customer_max_order_count: 0,
  });

  return (
    <ConditionBuilder
      name="customer_conditions"
      itemList={CUSTOM_CUSTOMER_ITEMS}
      renderField={renderField}
      defaultItemData={defaultItemData}
    />
  );
};

export default CustomerBlockStack;

