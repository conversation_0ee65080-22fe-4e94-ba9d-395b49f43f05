import React from 'react';
import { Controller, useFormContext, useWatch } from 'react-hook-form';

import { BlockStack, Card, RadioButton, Text } from '@shopify/polaris';
import CustomerBlockStack from './CustomerBlockStack';
import { CUSTOMER_OPTIONS, CUSTOMER_OPTION_VALUE } from '@/constants/offer/OfferGeneral';

const GeneralCustomer = () => {
  const { control, getValues } = useFormContext();

  return (
    <Card>
      <BlockStack gap={400}>
        <BlockStack gap={200}>
          <Text variant="headingMd" as="h2">
            Customer criteria
          </Text>

          <Controller
            name="general.apply_to_customers"
            control={control}
            rules={{
              validate: (value) => {
                const conditions = getValues('customer_conditions');
                return conditions.length > 0 || value !== 'custom_segment' || 'Please add at least one custom segment';
              },
            }}
            render={({ field, fieldState }) => {
              return CUSTOMER_OPTIONS.map((option) => (
                <div key={option.id}>
                  <RadioButton
                    id={option.id}
                    name={option.id}
                    key={option.id}
                    label={option.label}
                    checked={field.value === option.value}
                    onChange={() => field.onChange(option.value)}
                    helpText={
                      option.value === 'custom_segment' && <Text tone="critical">{fieldState.error?.message}</Text>
                    }
                  />
                </div>
              ));
            }}
          />
        </BlockStack>

        <RenderPickerByOption />
      </BlockStack>
    </Card>
  );
};

const RenderPickerByOption = () => {
  const { control } = useFormContext();
  const applyToCustomer = useWatch({ control, name: 'general.apply_to_customers' });

  switch (applyToCustomer) {
    case CUSTOMER_OPTION_VALUE.CUSTOM:
      return <CustomerBlockStack />;
    default:
      return null;
  }
};

export default GeneralCustomer;

