import React from 'react';

import { BlockStack, Card, Select, Text, TextField } from '@shopify/polaris';
import { Controller, useFormContext } from 'react-hook-form';
import { ACTIVE_OPTIONS } from '@/constants/offer/OfferGeneral';

export default function GeneralHeading() {
  const { control } = useFormContext();

  return (
    <Card>
      <BlockStack gap={400}>
        <BlockStack gap={200}>
          <Text variant="headingMd" as="h2">
            Name
          </Text>
          <Controller
            name="name"
            control={control}
            rules={{
              required: 'Offer name is required.',
            }}
            render={({ field, fieldState: { error } }) => (
              <TextField
                placeholder="Enter name"
                helpText="Internal use only. Not shown to customers."
                autoComplete="off"
                value={field.value}
                onChange={field.onChange}
                error={error?.message}
              />
            )}
          />
        </BlockStack>

        <BlockStack gap={200}>
          <Text variant="headingMd" as="h2">
            Status
          </Text>
          <Controller
            name="is_active"
            control={control}
            render={({ field }) => (
              <Select
                options={ACTIVE_OPTIONS}
                onChange={(value) => field.onChange(value === 'true')}
                value={String(field.value)}
              />
            )}
          />
        </BlockStack>
      </BlockStack>
    </Card>
  );
}

