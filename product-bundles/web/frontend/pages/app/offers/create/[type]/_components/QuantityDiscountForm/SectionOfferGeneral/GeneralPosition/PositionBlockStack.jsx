import React from 'react';
import { useFieldArray, useF<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';

import { BlockStack, Box, Button, InlineStack, Text, TextField } from '@shopify/polaris';
import { PlusIcon, DeleteIcon } from '@shopify/polaris-icons';
import { POSITION_SELECTOR_OPTIONS } from '@/constants/offer/OfferGeneral';
import FieldSelector from '../FieldSelector';

const PositionBlockStack = () => {
  const { control, clearErrors } = useFormContext();
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'general.widget_custom_positions',
  });

  const handleAdd = () => {
    append({
      selector: '',
      position: 'beforebegin',
    });
    clearErrors('general.widget_position');
  };

  return (
    <Box padding={300} borderColor="border-secondary" borderWidth="025" borderRadius="300">
      <BlockStack gap={400}>
        <InlineStack align="space-between">
          <Text variant="headingMd" as="h2">
            Custom position
          </Text>
          <Button variant="plain" onClick={handleAdd} icon={PlusIcon}>
            Add Selector
          </Button>
        </InlineStack>

        {fields.map((item, index) => (
          <InlineStack key={item.id} gap={300} blockAlign="start" wrap={false}>
            {/* Position field */}
            <FieldSelector
              label="Position"
              name={`general.widget_custom_positions.${index}.position`}
              options={POSITION_SELECTOR_OPTIONS}
            />

            {/* Selector field */}
            <Box width="100%">
              <Controller
                name={`general.widget_custom_positions.${index}.selector`}
                control={control}
                rules={{
                  required: 'Selector is required',
                }}
                render={({ field, fieldState }) => (
                  <TextField
                    label="Selector"
                    placeholder=".product-form"
                    autoComplete="off"
                    value={field.value}
                    onChange={field.onChange}
                    error={fieldState.error?.message}
                    connectedRight={
                      <Button
                        icon={DeleteIcon}
                        disabled={fields.length === 1}
                        onClick={() => remove(index)}
                        variant="plain"
                      />
                    }
                  />
                )}
              />
            </Box>
          </InlineStack>
        ))}
      </BlockStack>
    </Box>
  );
};

export default PositionBlockStack;

