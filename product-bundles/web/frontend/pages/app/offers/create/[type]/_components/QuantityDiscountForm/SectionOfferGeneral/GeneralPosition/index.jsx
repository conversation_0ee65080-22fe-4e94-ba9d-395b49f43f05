import React from 'react';
import { Controller, useFormContext, useWatch } from 'react-hook-form';

import { BlockStack, Card, RadioButton, Text } from '@shopify/polaris';
import { POSITION_OPTION_VALUE, POSITION_OPTIONS } from '@/constants/offer/OfferGeneral';
import PositionBlockStack from './PositionBlockStack';

const GeneralPosition = () => {
  const { control, getValues } = useFormContext();
  return (
    <Card>
      <BlockStack gap={400}>
        <BlockStack gap={200}>
          <Text variant="headingMd" as="h2">
            Widget Position
          </Text>

          <Controller
            name="general.widget_position"
            control={control}
            rules={{
              validate: (value) => {
                const conditions = getValues('general.widget_custom_positions');
                return (
                  conditions.length > 0 || value !== 'custom_position' || 'Please add at least one selector position'
                );
              },
            }}
            render={({ field, fieldState }) => {
              return POSITION_OPTIONS.map((option) => (
                <div key={option.id}>
                  <RadioButton
                    id={option.id}
                    name={option.id}
                    key={option.id}
                    label={option.label}
                    checked={field.value === option.value}
                    onChange={() => field.onChange(option.value)}
                    helpText={
                      option.value === 'custom_position' && <Text tone="critical">{fieldState.error?.message}</Text>
                    }
                  />
                </div>
              ));
            }}
          />
        </BlockStack>

        <RenderPickerByOption />
      </BlockStack>
    </Card>
  );
};

const RenderPickerByOption = () => {
  const { control } = useFormContext();
  const applyToCustomer = useWatch({ control, name: 'general.widget_position' });

  switch (applyToCustomer) {
    case POSITION_OPTION_VALUE.CUSTOM:
      return <PositionBlockStack />;
    default:
      return null;
  }
};

export default GeneralPosition;

