import React from 'react';

import { ResourcePicker } from '@shopify/app-bridge/actions';
import ResourceSelector from './ResourceSelector';
import BoxBadge from '../BoxBadge';
import ConditionBuilder from '../ConditionBuilder';
import { Controller, useFormContext } from 'react-hook-form';
import { CUSTOM_PRODUCT_VALUES, CUSTOM_PRODUCT_ITEMS } from '@/constants/offer/OfferGeneral';

const ProductBlockStack = () => {
  const { control } = useFormContext();

  const renderField = (field, index, remove) => {
    const type = field.type;

    if (type === CUSTOM_PRODUCT_VALUES.TAG.TYPE) {
      return (
        <Controller
          name={`product_conditions.${index}.${CUSTOM_PRODUCT_VALUES.TAG.FIELD}`}
          control={control}
          rules={{
            validate: (value) => (value && value.length > 0 ? true : 'Please add at least one product tag'),
          }}
          render={({ field, fieldState }) => (
            <BoxBadge
              index={index}
              fieldKey={CUSTOM_PRODUCT_VALUES.TAG.FIELD}
              conditionType={'product_conditions'}
              error={fieldState.error?.message}
              onDelete={() => remove(index)}
              onChange={field.onChange}
              value={field.value}
              label="Tag product"
            />
          )}
        />
      );
    }

    if (type === CUSTOM_PRODUCT_VALUES.TYPE.TYPE) {
      return (
        <Controller
          name={`product_conditions.${index}.${CUSTOM_PRODUCT_VALUES.TYPE.FIELD}`}
          control={control}
          rules={{
            validate: (value) => (value && value.length > 0 ? true : 'Please add at least one product type'),
          }}
          render={({ field, fieldState }) => (
            <BoxBadge
              index={index}
              fieldKey={CUSTOM_PRODUCT_VALUES.TYPE.FIELD}
              conditionType={'product_conditions'}
              error={fieldState.error?.message}
              onDelete={() => remove(index)}
              onChange={field.onChange}
              value={field.value}
              label="Product type"
            />
          )}
        />
      );
    }

    if (type === CUSTOM_PRODUCT_VALUES.VENDOR.TYPE) {
      return (
        <Controller
          name={`product_conditions.${index}.${CUSTOM_PRODUCT_VALUES.VENDOR.FIELD}`}
          control={control}
          rules={{
            validate: (value) => (value && value.length > 0 ? true : 'Please add at least one product vendor'),
          }}
          render={({ field, fieldState }) => (
            <BoxBadge
              index={index}
              fieldKey={CUSTOM_PRODUCT_VALUES.VENDOR.FIELD}
              conditionType={'product_conditions'}
              error={fieldState.error?.message}
              onDelete={() => remove(index)}
              onChange={field.onChange}
              value={field.value}
              remove={remove}
              label="Product vendor"
            />
          )}
        />
      );
    }

    if (type === CUSTOM_PRODUCT_VALUES.COLLECTION.TYPE) {
      return (
        <Controller
          name={`product_conditions.${index}.${CUSTOM_PRODUCT_VALUES.COLLECTION.FIELD}`}
          control={control}
          rules={{
            validate: (value) => (value && value.length > 0 ? true : 'Please select at least one resource'),
          }}
          render={({ field, fieldState }) => (
            <ResourceSelector
              index={index}
              remove={remove}
              label="Collection selector"
              fieldName={field.name}
              resourceType={ResourcePicker.ResourceType.Collection}
              placeholder="Search collections"
              error={fieldState.error?.message}
            />
          )}
        />
      );
    }

    return null;
  };

  const defaultItemData = (type) => ({
    type,
    operator: type === CUSTOM_PRODUCT_VALUES.TAG.TYPE ? 'include_all' : 'is',
    products_tags: [],
    products_types: [],
    products_vendors: [],
    products_collections: [],
  });

  return (
    <ConditionBuilder
      name="product_conditions"
      itemList={CUSTOM_PRODUCT_ITEMS}
      renderField={renderField}
      defaultItemData={defaultItemData}
    />
  );
};

export default ProductBlockStack;

