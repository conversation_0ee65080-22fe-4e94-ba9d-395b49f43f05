import React, { useCallback, useState } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

import { ResourcePicker } from '@shopify/app-bridge/actions';
import { getShopifyApp } from '@/utils/shopify';
import {
  BlockStack,
  Box,
  Button,
  Card,
  InlineStack,
  ResourceItem,
  ResourceList,
  Text,
  TextField,
  Thumbnail,
} from '@shopify/polaris';
import { DeleteIcon } from '@shopify/polaris-icons';

const ResourceSelector = ({ error, index, remove, label, fieldName, resourceType, placeholder }) => {
  const { control, setValue, clearErrors } = useFormContext();
  const values = useWatch({ control, name: fieldName });
  const [textSearch, setTextSearch] = useState('');

  const handleOpenPicker = useCallback(
    (query) => {
      
      // Create Shopify App instance
      const app = getShopifyApp();

      const picker = ResourcePicker.create(app, {
        resourceType,
        options: {
          initialQuery: query,
          filterQuery: query,
          showVariants: false,
          allowMultiple: true,
          initialSelectionIds: values?.map((v) => ({ id: v.id })),
        },
      });

      picker.subscribe(ResourcePicker.Action.SELECT, ({ selection }) => {
        const filtered = selection.map((item) => ({
          id: item.id,
          title: item['title'],
          image: resourceType === 'product' ? item.images?.[0] : item.image,
          productsCount: resourceType === 'collection' ? item.productsCount : 0,
          totalVariants: item['totalVariants'] ?? 0,
        }));

        clearErrors(fieldName);
        setTextSearch('');
        setValue(fieldName, filtered, { shouldDirty: true });
      });

      picker.subscribe(ResourcePicker.Action.CANCEL, () => {
        setTextSearch('');
        picker.unsubscribe();
      });

      picker.dispatch(ResourcePicker.Action.OPEN);
    },
    [resourceType, values, fieldName, setValue, clearErrors]
  );

  const handleRemove = (id) => {
    const updated = values.filter((v) => v.id !== id);
    setValue(fieldName, updated, { shouldDirty: true });
  };

  const renderItem = (item) => {
    const imageSrc = item?.image?.originalSrc || 'https://placehold.co/600x400';
    const countItem = resourceType === 'product' ? `${item.totalVariants} selected` : `${item.productsCount} products`;
    const media = <Thumbnail source={imageSrc} alt={item.title} size="small" />;

    return (
      <ResourceItem id={item.id} media={media} accessibilityLabel={`View details for ${item.title}`}>
        <InlineStack align="space-between" blockAlign="center" wrap={false} gap={150}>
          <BlockStack gap={100}>
            <Text variant="bodyMd" fontWeight="medium">
              {item.title}
            </Text>
            <Text>{countItem}</Text>
          </BlockStack>
          <Button icon={DeleteIcon} variant="plain" onClick={() => handleRemove(item.id)} />
        </InlineStack>
      </ResourceItem>
    );
  };

  return (
    <Box padding={300} borderColor="border-secondary" borderWidth="025" borderRadius="300">
      <BlockStack gap={400}>
        {/* Resource array have button */}
        {resourceType === 'collection' && (
          <InlineStack align="space-between" blockAlign="center">
            <Text variant="headingMd" as="h2">
              {label}
            </Text>
            <Button variant="plain" icon={DeleteIcon} onClick={() => remove(index)} />
          </InlineStack>
        )}

        <InlineStack align="space-between">
          <Box width="100%">
            <TextField
              placeholder={placeholder}
              value={textSearch}
              autoComplete="off"
              error={error}
              onChange={(value) => {
                setTextSearch(value);
                handleOpenPicker(value);
              }}
              connectedRight={
                <Button onClick={() => handleOpenPicker(textSearch)} size="large">
                  Browse
                </Button>
              }
            />
          </Box>
        </InlineStack>

        {values?.length > 0 && (
          <Card padding={0}>
            <ResourceList
              resourceName={{ singular: resourceType, plural: `${resourceType}s` }}
              items={values}
              renderItem={renderItem}
            />
          </Card>
        )}
      </BlockStack>
    </Box>
  );
};

export default ResourceSelector;

