import React from 'react';
import { useF<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller, useWatch } from 'react-hook-form';

import { ResourcePicker } from '@shopify/app-bridge/actions';
import { Card, BlockStack, Text, RadioButton } from '@shopify/polaris';
import ProductBlockStack from './ProductBlockStack';
import ResourceSelector from './ResourceSelector';
import { PRODUCT_OPTIONS, PRODUCT_OPTION_VALUE } from '@/constants/offer/OfferGeneral';

const GeneralProductPicker = () => {
  const { control, getValues } = useFormContext();

  return (
    <Card>
      <BlockStack gap={400}>
        <BlockStack gap={200}>
          <Text variant="headingMd" as="h2">
            Apply to Products
          </Text>

          <Controller
            name="general.apply_to_products"
            control={control}
            rules={{
              validate: (value) => {
                const conditions = getValues('product_conditions');
                return conditions.length > 0 || value !== 'custom_segment' || 'Please add at least one custom segment';
              },
            }}
            render={({ field, fieldState }) => {
              return PRODUCT_OPTIONS.map((option) => (
                <RadioButton
                  id={option.id}
                  name={option.id}
                  key={option.id}
                  label={option.label}
                  checked={field.value === option.value}
                  onChange={() => field.onChange(option.value)}
                  helpText={
                    option.value === 'custom_segment' && <Text tone="critical">{fieldState.error?.message}</Text>
                  }
                />
              ));
            }}
          />
        </BlockStack>

        <RenderPickerByOption />
      </BlockStack>
    </Card>
  );
};

const RenderPickerByOption = () => {
  const { control } = useFormContext();
  const applyToProduct = useWatch({ control, name: 'general.apply_to_products' });

  switch (applyToProduct) {
    case PRODUCT_OPTION_VALUE.SPECIFIC:
      return (
        <Controller
          name="general.applied_products"
          control={control}
          rules={{
            validate: (value) => (value && value.length > 0 ? true : 'Please select at least one product'),
          }}
          render={({ field, fieldState }) => (
            <ResourceSelector
              fieldName={field.name}
              resourceType={ResourcePicker.ResourceType.Product}
              placeholder="Search products"
              error={fieldState.error?.message}
            />
          )}
        />
      );
    case PRODUCT_OPTION_VALUE.CUSTOM:
      return <ProductBlockStack />;
    default:
      return null;
  }
};

export default GeneralProductPicker;

