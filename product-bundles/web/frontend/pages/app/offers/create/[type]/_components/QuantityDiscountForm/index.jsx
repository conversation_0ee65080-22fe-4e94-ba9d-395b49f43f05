import React, { useCallback, useState } from 'react';

import { BlockStack, Box, Card, Layout, Tabs } from '@shopify/polaris';
import { TABS_OFFER_DETAIL } from '@/constants/offer/OfferBlock';
import SectionPreview from './SectionPreview';
import SectionO<PERSON>Block from './SectionOfferBlock';
import SectionOfferGeneral from './SectionOfferGeneral';
import SectionAdvanced from './SectionAdvanced';

const tabContent = [
  <SectionOfferBlock key="tier-block" />,
  <SectionOfferGeneral key="general" />,
  <Card key="design" />,
  <SectionAdvanced key="advanced" />,
];

export default function QuantityDiscountForm() {
  const [selected, setSelected] = useState(0);

  const handleTabChange = useCallback((selectedTabIndex) => setSelected(selectedTabIndex), []);

  return (
    <Layout>
      {/* RIGHT SECTION - OFFER CONFIG */}
      <Layout.Section>
        <Box className="section-config">
          <BlockStack gap={400}>
            {/* Tabs */}
            <Card sectioned padding={0}>
              <Tabs tabs={TABS_OFFER_DETAIL} selected={selected} onSelect={handleTabChange} fitted />
            </Card>

            {/* Tabs content */}
            {tabContent[selected]}
          </BlockStack>
        </Box>
      </Layout.Section>

      {/* LEFT SECTION - PREVIEW */}
      <Layout.Section variant="oneThird">
        <Box className="section-preview">
          <SectionPreview />
        </Box>
      </Layout.Section>
    </Layout>
  );
}

