import React, { useContext, useEffect, useMemo } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';

import { useSaveBar, useBackAction } from '@/hooks';
import { Box, Card, Form, Page } from '@shopify/polaris';
import { nav } from '@/utils/mixed';
import { ROUTES } from '@/constants/navigation';
import { INITIAL_OFFER_VALUES } from '@/constants/offer/OfferBlock';
import QuantityDiscountForm from './_components/QuantityDiscountForm';
import { AppDataContext } from '../../../../../context/AppDataContext';

export default function OfferCreate() {
  const { type } = useParams();
  const { handleBackAction } = useBackAction();
  const { open: saveBarOpen, show: showSaveBar, hide: hideSaveBar, SaveBar } = useSaveBar();

  const { locales, isLoading, shop } = useContext(AppDataContext);

  // Parse type data
  const initialValues = useMemo(() => {
    return {
      ...INITIAL_OFFER_VALUES,
      uuid: uuidv4() || INITIAL_OFFER_VALUES.uuid,
      type: type || INITIAL_OFFER_VALUES.type,
    };
  }, [type]);

  const methods = useForm({
    defaultValues: initialValues,
  });

  const {
    handleSubmit,
    reset,
    formState: { isDirty },
  } = methods;

  const onSubmit = handleSubmit((data) => {
    reset(data);
    hideSaveBar();
    console.log('Form data:', data);
  });

  const renderForm = () => {
    switch (type) {
      case 'quantity-discount':
        return <QuantityDiscountForm />;
      case 'quantity-breaks':
        return <Card>NOT YET</Card>;
      case 'volume-discount':
        return <Card>NOT YET</Card>;
      case 'boosters':
        return <Card>NOT YET</Card>;
      default:
        return <Card>Unknown type</Card>;
    }
  };

  useEffect(() => {
    isDirty ? showSaveBar() : hideSaveBar();
  }, [isDirty, showSaveBar, hideSaveBar]);

  useEffect(() => {
    showSaveBar();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!isLoading) {
      console.log('locales', locales);
      console.log('shop', shop);
    }
  }, [isLoading, locales, shop]);

  return (
    <Page
      title={`Create ${type}`}
      backAction={{
        content: 'Select Offers',
        onAction: () => handleBackAction({ isSaveBarOpen: saveBarOpen, route: nav(ROUTES.OFFER_SELECT) }),
      }}
    >
      <FormProvider {...methods}>
        <Form onSubmit={onSubmit}>{renderForm()}</Form>
      </FormProvider>

      <SaveBar
        open={saveBarOpen}
        saveAction={{
          onAction: onSubmit,
          content: 'Save',
          loading: false,
        }}
        discardAction={{
          onAction: () => {
            hideSaveBar();
          },
          content: 'Discard',
          loading: false,
        }}
      />

      {/* Spacer to ensure footer is not hidden */}
      <Box style={{ padding: '16px' }} />
    </Page>
  );
}

