import React from 'react';
import { useNavigate } from 'react-router-dom';

import { Page } from '@shopify/polaris';
import { SectionTableOffers } from './_components';
import { ROUTES } from '@/constants/navigation';
import { nav } from '@/utils/mixed';

export default function OfferPage() {
  const navigate = useNavigate();

  return (
    <Page
      fullWidth
      title="List Offer"
      subtitle="Explore multiple simple offer formats to boost sales and drive higher average order value (AOV)."
      primaryAction={{
        content: 'Create Offer',
        onAction: () => {
          navigate(nav(ROUTES.OFFER_SELECT));
        },
      }}
    >
      <SectionTableOffers />
    </Page>
  );
}

