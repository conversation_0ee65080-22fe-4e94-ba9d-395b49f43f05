import React, { useCallback, useState } from 'react';

import { BlockStack, Page, Tabs } from '@shopify/polaris';
import { ROUTES } from '@/constants/navigation';
import { SectionCreateCard } from '../_components';
import { nav } from '/utils/mixed';

export default function OfferSelect() {
  const [selected, setSelected] = useState(0);

  const handleTabChange = useCallback((selectedTabIndex) => setSelected(selectedTabIndex), []);

  const tabs = [
    {
      id: 'product',
      content: 'Product Page',
      badge: FAKE_DATA_PRODUCT.length,
      panelID: 'product-content',
    },
    {
      id: 'booster',
      content: 'Boosters',
      badge: FAKE_DATA_BOOSTS.length,
      panelID: 'booster-content',
    },
  ];

  return (
    <Page title="Select Offer" backAction={{ content: 'Offers', url: nav(ROUTES.OFFERS) }}>
      <BlockStack gap={400}>
        <Tabs tabs={tabs} selected={selected} onSelect={handleTabChange} />
        {selected === 0 && <SectionCreateCard data={FAKE_DATA_PRODUCT} />}
        {selected === 1 && <SectionCreateCard data={FAKE_DATA_BOOSTS} />}
      </BlockStack>
    </Page>
  );
}

const FAKE_DATA_PRODUCT = [
  {
    title: 'Quantity discount',
    subTitle: 'Applied when buying multiple quantities of the same product.',
    buttonContent: 'Select',
    imgSource: 'https://placehold.co/912x798',
    type: 'quantity-discount',
    disabled: false,
  },
  {
    title: 'Quantity breaks & Free Gift',
    subTitle: 'Applied when buying multiple quantities of the same product. With interactive selector.',
    buttonContent: 'Select',
    imgSource: 'https://placehold.co/912x798',
    type: 'quantity-breaks',
    disabled: true,
  },
  {
    title: 'Volume discount bundles',
    subTitle: 'A discount given for buying a certain quantity or more of multiple items.',
    buttonContent: 'Select',
    imgSource: 'https://placehold.co/912x798',
    type: 'volume-discount',
    disabled: true,
  },
];

const FAKE_DATA_BOOSTS = [
  {
    title: 'Boosters & Free Gift',
    subTitle: 'Applied when buying multiple quantities of the same product. With interactive selector.',
    buttonContent: 'Select',
    imgSource: 'https://placehold.co/912x798',
    type: 'boosters-discount',
    disabled: true,
  },
];

