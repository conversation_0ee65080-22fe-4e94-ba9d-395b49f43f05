import { BlockStack, Card, Text, InlineStack, Box, Button, ButtonGroup, Divider, Icon } from '@shopify/polaris';
import { SkeletonTextLine } from '../../../components/ui/SkeletonTextLine';
import { useMutation } from 'react-query';
import { DiscountApi, PricingPlan<PERSON>pi } from '../../../apis';
import { useCallback, useContext, useState } from 'react';
import { AppDataContext } from '../../../context/AppDataContext';
import { useModal, useRedirect, useToast } from '../../../hooks';
import { CustomInput } from '../../../components/input';
import { CheckCircleIcon } from '@shopify/polaris-icons';
import './PricingCard2.scss';

export const PricingCard2 = ({ plan, isUpgrade, loading, className = '' }) => {
  const redirect = useRedirect();
  const { shop } = useContext(AppDataContext);
  const { toast } = useToast();
  const {
    modalOpen: modalConfirmOpen,
    showModal: showModalConfirm,
    hideModal: hideModalConfirm,
    Modal: ModalConfirm,
  } = useModal({});

  // validate discount
  const { isLoading: isCouponValidating, mutateAsync: validateCoupon } = useMutation((discount) =>
    DiscountApi.validate(discount)
  );

  const [coupon, setCoupon] = useState('');
  const [couponError, setCouponError] = useState(null);
  const [couponValid, setCouponValid] = useState(null);
  const [discount, setDiscount] = useState(null);

  const handleValidateCoupon = useCallback(async () => {
    const validateResult = await validateCoupon({ discount_code: coupon });
    if (validateResult?.data?.valid) {
      setDiscount(validateResult?.data?.discount);
      setCouponValid(validateResult?.data?.message);
      setCouponError(null);
    } else {
      setDiscount(null);
      setCouponError(validateResult?.data?.message);
      setCouponValid(null);
    }
  }, [coupon, validateCoupon]);

  // create charge
  const [isChargeCreating, setIsChargeCreating] = useState(false);
  const { mutateAsync: createCharge } = useMutation((data) =>
    PricingPlanApi.createChargeByPlanId(data.planId, { discountId: data.discountId })
  );

  const handleCreateCharge = useCallback(async () => {
    setIsChargeCreating(true);
    const chargeData = await createCharge({ planId: plan?.plan_name, discountId: discount?._id });
    const confirmationUrl = chargeData?.data?.confirmationUrl;
    if (confirmationUrl) redirect(confirmationUrl, true);
    else {
      setIsChargeCreating(false);
      toast.message('Your plan has been updated.');
      redirect('/');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createCharge, discount?._id, plan?.plan_name, redirect]);

  if (loading) return <SkeletonPricingCard className={className} />;

  return (
    <div className={`mtm-pricing-card ${className}`}>
      {plan?.featured_text && <div className="mtm-pricing__featured-text">{plan?.featured_text}</div>}

      <Card padding={600}>
        <BlockStack gap={400}>
          <Text variant="headingLg" tone="subdued">
            {plan?.plan_display_name}
          </Text>

          {plan?.plan_amount_base === 0 ? (
            <Text variant="heading2xl">Free</Text>
          ) : (
            <InlineStack blockAlign="end" wrap={false}>
              {!!plan?.plan_amount_compare && (
                <Box paddingInlineEnd={200}>
                  <Text variant="heading2xl" tone="subdued" fontWeight="semibold" textDecorationLine="line-through">
                    ${plan?.plan_amount_compare}
                  </Text>
                </Box>
              )}

              <Text variant="heading2xl">
                <span style={{ color: plan?.plan_amount_compare ? '#ff5d3b' : '#000000' }}>
                  ${plan?.plan_amount_base}
                </span>
              </Text>

              <Text variant="heading2xl" fontWeight="regular">
                <span style={{ fontWeight: 300, color: plan?.plan_amount_compare ? '#ff5d3b' : '#000000' }}>/</span>
              </Text>
              <Text fontWeight="semibold">
                <span style={{ color: plan?.plan_amount_compare ? '#ff5d3b' : '#000000' }}>
                  {{ monthly: 'month', yearly: 'year' }?.[plan?.recurring_interval]}
                </span>
              </Text>
            </InlineStack>
          )}

          <Box minHeight="400px">
            <BlockStack gap={150}>
              {plan?.additional_text && (
                <Box paddingBlockEnd={200}>
                  <Text tone="subdued" variant="bodySm">
                    {plan?.additional_text}
                  </Text>
                </Box>
              )}

              <Box paddingBlockEnd={200}>
                <Divider />
              </Box>

              {plan?.plan_name === 'free' && (
                <Box paddingBlockEnd="200">
                  <Text variant="bodySm" tone="critical">
                    This plan includes limited features and will remain available unless you switch to another plan.
                  </Text>
                </Box>
              )}

              {plan?.benefits?.map((benefit, index) => (
                <Text variant="bodyMd" key={index}>
                  <b style={{ fontSize: 20, color: '#ff5d3b' }}>✓</b>&nbsp;&nbsp;&nbsp;{benefit}
                </Text>
              ))}
            </BlockStack>
          </Box>

          {shop?.app_data?.plan_name === plan?.plan_name ? (
            <span className="mtm-pricing-card__button selected disabled">Current Plan</span>
          ) : (
            <span
              onClick={plan?.plan_name === 'free' ? () => {} : showModalConfirm}
              className={`mtm-pricing-card__button ${isChargeCreating && 'loading'} ${plan?.plan_name === 'free' && 'disabled'}`}
            >
              Switch Plan
            </span>
          )}
        </BlockStack>
      </Card>

      <ModalConfirm
        open={modalConfirmOpen}
        title={isUpgrade ? 'Upgrade Plan' : 'Downgrade Plan'}
        onClose={hideModalConfirm}
        size="small"
        primaryAction={{
          content: 'Continue',
          loading: isChargeCreating,
          onAction: handleCreateCharge,
        }}
        secondaryActions={[
          {
            content: 'Cancel',
            disabled: isChargeCreating,
            onAction: hideModalConfirm,
          },
        ]}
      >
        <BlockStack gap={200}>
          <Text>
            {isUpgrade
              ? `You are upgrading to the ${plan?.plan_display_name?.toUpperCase()} plan. Enjoy enhanced features and greater flexibility. Proceed?`
              : `You are downgrading to the ${plan?.plan_display_name?.toUpperCase()} plan. Some advanced features will be removed. Continue?`}
          </Text>
          {plan?.plan_amount_base > 0 && (
            <>
              <Divider />
              <InlineStack blockAlign={couponError || couponValid ? 'center' : 'end'} gap={200}>
                <div style={{ flex: 1 }}>
                  <BlockStack gap={100}>
                    <CustomInput
                      size="slim"
                      disabled={isCouponValidating || isChargeCreating}
                      label={
                        <Text variant="bodyMd" fontWeight="medium">
                          Enter a coupon code if you have one
                        </Text>
                      }
                      placeholder="Enter code"
                      value={coupon}
                      onChange={setCoupon}
                      error={couponError}
                    />
                    {couponValid && (
                      <InlineStack gap={150} blockAlign="center">
                        <span>
                          <Icon source={CheckCircleIcon} tone="success" />
                        </span>
                        <Text tone="success" variant="bodySm">
                          Coupon applied
                        </Text>
                      </InlineStack>
                    )}
                  </BlockStack>
                </div>
                <Button
                  size="micro"
                  loading={isCouponValidating}
                  disabled={!coupon || isChargeCreating}
                  onClick={handleValidateCoupon}
                >
                  Apply
                </Button>
              </InlineStack>
            </>
          )}
        </BlockStack>
      </ModalConfirm>
    </div>
  );
};

function SkeletonPricingCard({ className }) {
  return (
    <div className={`mtm-pricing-card ${className}`}>
      <Card>
        <BlockStack gap="500">
          <SkeletonTextLine height="24px" width="50%" margin="0" />

          <SkeletonTextLine height="35px" margin="0" />

          <Divider />

          <Box minHeight="24rem">
            <BlockStack gap="200">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]?.map((id) => (
                <SkeletonTextLine key={id} margin="10px" />
              ))}
            </BlockStack>
          </Box>

          <Box paddingBlockStart="200" paddingBlockEnd="200">
            <ButtonGroup fullWidth>
              <SkeletonTextLine margin="0" height="28px" />
            </ButtonGroup>
          </Box>
        </BlockStack>
      </Card>
    </div>
  );
}

