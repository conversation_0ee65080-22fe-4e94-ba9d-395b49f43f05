.mtm-pricing-card {
  width: 100%;
  border-radius: 12px;

  &:has(.mtm-pricing__featured-text) {
    box-shadow: 0 0 1px 1.5px #ff5d3b;
    position: relative;

    .mtm-pricing__featured-text {
      position: absolute;
      z-index: 1;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      background-color: #ff5d3b;
      padding: 3px 12px;
      border-radius: 100px;
      white-space: nowrap;
    }
  }

  &:hover {
    box-shadow: 0 0 1px 1px #ff5d3b;
  }

  .mtm-pricing-card__button {
    border: 1.5px solid #ff5d3b;
    border-radius: 200px;
    box-shadow: 0 0 3px currentColor;
    padding: 10px 20px;
    background-color: white;
    color: #ff5d3b;
    text-align: center;
    font-weight: 700;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.1s;

    &.disabled,
    &.loading {
      transform: none;
      cursor: not-allowed;
      opacity: 0.5;
    }

    &:not(.disabled):hover,
    &.selected {
      color: white;
      background-color: #ff5d3b;
    }
  }
}

@mixin generate-column-widths($columns, $gap: 24px) {
  width: calc((100% - ($columns - 1) * $gap) / $columns);
}

@media screen and (min-width: 755px) {
  @for $i from 2 through 6 {
    .mtm-pricing-card--columns-#{$i}.mtm-pricing-card {
      @include generate-column-widths(2);
    }
  }
}

@media screen and (min-width: 1115px) {
  @for $i from 3 through 6 {
    .mtm-pricing-card--columns-#{$i}.mtm-pricing-card {
      @include generate-column-widths(3);
    }
  }
}

@media screen and (min-width: 1475px) {
  @for $i from 4 through 6 {
    .mtm-pricing-card--columns-#{$i}.mtm-pricing-card {
      @include generate-column-widths(4);
    }
  }
}

@media screen and (min-width: 1835px) {
  @for $i from 5 through 6 {
    .mtm-pricing-card--columns-#{$i}.mtm-pricing-card {
      @include generate-column-widths(5);
    }
  }
}

@media screen and (min-width: 2194px) {
  .mtm-pricing-card--columns-6.mtm-pricing-card {
    @include generate-column-widths(6);
  }
}

