import { BlockStack, InlineStack, Layout } from '@shopify/polaris';
import { useContext, useMemo } from 'react';
import { AppDataContext } from '../../../context/AppDataContext';
import { PricingCard2 } from './PricingCard2';
import _ from 'lodash';

export const SectionPricing = ({ mapPlans = {}, loading }) => {
  const { shop } = useContext(AppDataContext);

  const plans = useMemo(() => shop?.app_data?.plan_list || [], [shop?.app_data?.plan_list]);
  const planColumnsCount = Math.min(Object.keys(mapPlans)?.length, 6);

  console.log('shop', shop);

  return (
    <Layout.Section>
      <BlockStack gap={600}>
        <InlineStack gap="600" align="start" blockAlign="start">
          {plans.map((planId) => {
            const plan = mapPlans?.[planId];
            const currentPlan = mapPlans[shop?.app_data?.plan_name];

            const isUpgrade = plan?.plan_level > currentPlan?.plan_level;

            console.log('isUpgrade', isUpgrade);

            return (
              <PricingCard2
                plan={plan}
                isUpgrade={isUpgrade}
                loading={loading}
                key={planId}
                className={`mtm-pricing-card--columns-${planColumnsCount}`}
              />
            );
          })}
        </InlineStack>
      </BlockStack>
    </Layout.Section>
  );
};

