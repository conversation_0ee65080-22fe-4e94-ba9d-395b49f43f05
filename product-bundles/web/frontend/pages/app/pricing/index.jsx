import { Page, Layout, Banner, Text, Button } from '@shopify/polaris';
import { SectionPricing } from './SectionPricing';
import { useQuery } from 'react-query';
import { PricingPlanApi } from '../../../apis';
import { useSupport } from '../../../hooks';

export default function PricingPage() {
  const { isFetching: isPlansGetting, data: plansData } = useQuery(
    'shop-plans-repo',
    () => PricingPlanApi.getShopPlans(),
    { refetchOnWindowFocus: false }
  );

  const { startSupport } = useSupport();

  return (
    <Page title="Plans & Pricing" fullWidth>
      <Layout>
        <Layout.Section>
          <Banner>
            <Text>
              Check with us{' '}
              <Button
                variant="plain"
                onClick={() => startSupport('I want to get a discount coupon, do you have any available?')}
              >
                here
              </Button>{' '}
              to see if any discounts are available!
            </Text>
          </Banner>
        </Layout.Section>
        <div style={{ height: 80 }} />
        <SectionPricing mapPlans={plansData?.data} loading={isPlansGetting} />
        <Layout.Section />
      </Layout>
    </Page>
  );
}

