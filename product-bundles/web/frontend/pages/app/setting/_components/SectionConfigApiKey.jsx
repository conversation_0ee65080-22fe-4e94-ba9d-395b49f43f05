import { <PERSON><PERSON><PERSON>ck, Box, <PERSON>ton, Card, FormLayout, InlineStack, Layout, Text } from '@shopify/polaris';
import { SkeletonTextLine } from '../../../../components/ui';
import { useCallback, useContext, useState } from 'react';
import copy from 'copy-to-clipboard';
import { useModal, useToast } from '../../../../hooks';
import { AppDataContext } from '../../../../context/AppDataContext';
import { useMutation } from 'react-query';
import { ShopApi } from '../../../../apis';

export const SectionConfigApiKey = ({ loading, disabled }) => {
  const [showApiKey, setShowApiKey] = useState(false);
  const { toast } = useToast();
  const { isLoading, shop, refetchData } = useContext(AppDataContext);

  const { isLoading: isApiKeyRegenerating, mutateAsync: regenerateApiKey } = useMutation(() =>
    ShopApi.regenerateApiKey()
  );

  const handleRegenerateApiKey = useCallback(async () => {
    await regenerateApiKey();
    await refetchData({ shop: true });
    toast.message('API key generated');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refetchData, regenerateApiKey]);

  const { modalOpen, showModal, hideModal, Modal } = useModal({});

  if (loading) return <SkeletonSection />;

  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="300">
          <FormLayout>
            <BlockStack gap="150">
              <Text fontWeight="medium">API key</Text>
              <InlineStack gap="150">
                <div style={{ flex: 1 }}>
                  <Box
                    borderColor="border"
                    borderWidth="025"
                    paddingBlock="150"
                    paddingInline="300"
                    borderRadius="200"
                    background="bg-fill-disabled"
                    opacity={disabled ? '0.5' : '1'}
                    width="100%"
                  >
                    {showApiKey ? shop?.gtf_access_token || '‎' : '••••••••••••••••••••••••••••••••••••••••••••••••'}
                  </Box>
                </div>
                <Button
                  disabled={disabled || isApiKeyRegenerating || isLoading}
                  onClick={() => {
                    copy(shop?.gtf_access_token || '••••••••••••••••••••••••••••••••••••••••••••••••');
                    toast.message('API key copied');
                  }}
                >
                  Copy
                </Button>
                <Button
                  disabled={disabled || isApiKeyRegenerating || isLoading}
                  onClick={() => setShowApiKey(!showApiKey)}
                >
                  {showApiKey ? 'Hide' : 'Show'}
                </Button>
              </InlineStack>
              <Box paddingBlockStart="150">
                <Text variant="bodySm" tone="subdued">
                  Your API key is used to manage product groups via code and can be integrated with your external
                  applications. It will not expire, if you need a new one, you can{' '}
                  <Button variant="plain" disabled={disabled} onClick={showModal}>
                    regenerate here
                  </Button>
                  . For more details, see the{' '}
                  <Button
                    variant="plain"
                    onClick={() =>
                      window.open(
                        'https://tapita0.zohodesk.com/portal/en/kb/articles/grouptify-product-group-api-guide',
                        '_blank'
                      )
                    }
                  >
                    API documentation
                  </Button>
                  .
                </Text>
              </Box>
            </BlockStack>
          </FormLayout>
        </BlockStack>
      </Card>
      <Modal
        open={modalOpen}
        title="Confirm API key regeneration"
        onClose={hideModal}
        primaryAction={{
          content: 'Yes, regenerate',
          loading: isApiKeyRegenerating || isLoading,
          onAction: async () => {
            await handleRegenerateApiKey();
            hideModal();
          },
        }}
        secondaryActions={[{ content: 'Cancel', loading: isApiKeyRegenerating || isLoading, onAction: hideModal }]}
      >
        <Text>
          Are you sure you want to generate a new API key? Your current key will be permanently disabled and can no
          longer be used.
        </Text>
      </Modal>
    </Layout.Section>
  );
};

function SkeletonSection() {
  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="300">
          <InlineStack gap="300">
            <div style={{ flex: 1 }}>
              <SkeletonTextLine width="120px" height="16px" margin="4px" />
              <SkeletonTextLine width="100%" height="32px" margin="0" />
            </div>

            <div style={{ flex: 1 }}>
              <SkeletonTextLine width="120px" height="16px" margin="4px" />
              <SkeletonTextLine width="100%" height="32px" margin="0" />
            </div>
          </InlineStack>

          <BlockStack gap="100">
            <SkeletonTextLine width="100%" />
            <SkeletonTextLine lastLine />
          </BlockStack>
        </BlockStack>
      </Card>
    </Layout.Section>
  );
}

