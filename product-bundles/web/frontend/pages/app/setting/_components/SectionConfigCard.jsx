import {
  Autocomplete,
  BlockStack,
  Box,
  Button,
  Card,
  Checkbox,
  Divider,
  InlineStack,
  Layout,
  RadioButton,
  Tag,
  Text,
} from '@shopify/polaris';
import { SkeletonTextLine } from '../../../../components/ui';
import { useCallback, useMemo, useState } from 'react';

export const SectionConfigCard = ({ formData, loading, disabled, onChange = () => {} }) => {
  const allPages = useMemo(
    () => [
      { value: 'show_on_article', label: 'Article' },
      { value: 'show_on_blog', label: 'Blog' },
      { value: 'show_on_cart', label: 'Cart' },
      { value: 'show_on_collection', label: 'Collection' },
      { value: 'show_on_home', label: 'Home' },
      { value: 'show_on_page', label: 'Page' },
      { value: 'show_on_product', label: 'Product' },
      { value: 'show_on_search', label: 'Search' },
    ],
    []
  );

  const selectedPages = useMemo(() => {
    const pages = [];
    if (formData?.show_on_article) pages.push('show_on_article');
    if (formData?.show_on_blog) pages.push('show_on_blog');
    if (formData?.show_on_cart) pages.push('show_on_cart');
    if (formData?.show_on_collection) pages.push('show_on_collection');
    if (formData?.show_on_home) pages.push('show_on_home');
    if (formData?.show_on_page) pages.push('show_on_page');
    if (formData?.show_on_product) pages.push('show_on_product');
    if (formData?.show_on_search) pages.push('show_on_search');

    return pages;
  }, [
    formData?.show_on_article,
    formData?.show_on_blog,
    formData?.show_on_cart,
    formData?.show_on_collection,
    formData?.show_on_home,
    formData?.show_on_page,
    formData?.show_on_product,
    formData?.show_on_search,
  ]);

  const setSelectedPages = useCallback(
    (selected = []) => {
      const affected = [
        { path: 'show_on_article', value: false },
        { path: 'show_on_blog', value: false },
        { path: 'show_on_cart', value: false },
        { path: 'show_on_collection', value: false },
        { path: 'show_on_home', value: false },
        { path: 'show_on_page', value: false },
        { path: 'show_on_product', value: false },
        { path: 'show_on_search', value: false },
      ];

      affected.forEach((config) => {
        if (selected.includes(config.path)) config.value = true;
      });

      onChange(undefined, undefined, affected);
      //
    },
    [onChange]
  );

  const [pageSearch, setPageSearch] = useState('');
  const [filteredPages, setFilteredPages] = useState(allPages);

  const updatePageSearch = useCallback(
    (value) => {
      setPageSearch(value);

      if (value === '') {
        setFilteredPages(allPages);
        return;
      }

      const filterRegex = new RegExp(value, 'i');
      const resultOptions = allPages.filter((option) => option.label.match(filterRegex));

      setFilteredPages(resultOptions);
    },
    [allPages]
  );

  const verticalContentMarkup =
    selectedPages.length > 0 ? (
      <InlineStack gap="200" wrap={true}>
        {selectedPages.map((page) => {
          const matchPage = allPages.find((p) => p.value === page);

          return (
            <Tag key={`page-${page}`} onRemove={() => onChange(false, page)} disabled={disabled}>
              {matchPage.label}
            </Tag>
          );
        })}
      </InlineStack>
    ) : null;

  const pageTextField = (
    <Autocomplete.TextField
      label={<Text fontWeight="medium">Pages to show product options on card</Text>}
      value={pageSearch}
      onChange={updatePageSearch}
      helpText="Leave blank to hide on all pages"
      verticalContent={verticalContentMarkup}
      placeholder="Select page type . . ."
      autoComplete="off"
      disabled={disabled}
      onBlur={() => updatePageSearch('')}
    />
  );

  if (loading) return <SkeletonSection />;

  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="300">
          <Autocomplete
            allowMultiple
            options={filteredPages}
            selected={selectedPages}
            textField={pageTextField}
            onSelect={setSelectedPages}
          />

          <Divider />

          <BlockStack gap="100">
            <Box opacity={disabled ? 0.5 : 1}>
              <Text>
                When selecting an option on the product card, you can either update the card with the corresponding
                variant or redirect to the product page to view details and add to cart.
              </Text>
            </Box>

            <InlineStack gap="400" blockAlign="center">
              <RadioButton
                label="Change product card content"
                name="change_variant_on_card_behavior"
                id="change_content"
                checked={formData?.change_variant_on_card_behavior === 'change_content'}
                onChange={() => onChange('change_content', 'change_variant_on_card_behavior')}
                disabled={disabled}
              />
              <RadioButton
                label="Redirect to product page"
                name="change_variant_on_card_behavior"
                id="redirect_to_product_page"
                checked={formData?.change_variant_on_card_behavior === 'redirect'}
                onChange={() => onChange('redirect', 'change_variant_on_card_behavior')}
                disabled={disabled}
              />
            </InlineStack>
          </BlockStack>

          <Divider />

          <BlockStack gap="100">
            <Box opacity={disabled ? 0.5 : 1}>
              <Text>
                For single option groups displayed on the product card, showing the label may not always be necessary.
                If you&apos;d like to hide the label, check this box.
              </Text>
            </Box>

            <Checkbox
              label="Hide option label on product card"
              id="hide_card_option_label"
              checked={formData?.hide_card_option_label}
              disabled={disabled}
              onChange={onChange}
            />
          </BlockStack>

          <Divider />

          <BlockStack gap="100">
            <Box opacity={disabled ? 0.5 : 1}>
              <Text>
                If you are using the{' '}
                <Button
                  variant="plain"
                  disabled={disabled}
                  onClick={() => window.open('https://apps.shopify.com/product-filter-search', '_blank')}
                >
                  Boost AI Search & Filter
                </Button>{' '}
                app, let us know. This app modifies the structure of collection and search pages, which may affect how
                options are displayed. Letting us know will help ensure product options appear correctly on cards.
              </Text>
            </Box>

            <Checkbox
              label="I'm using Boost AI Search & Filter"
              id="use_basf_app"
              checked={formData?.use_basf_app}
              onChange={onChange}
              disabled={disabled}
            />
          </BlockStack>
        </BlockStack>
      </Card>
    </Layout.Section>
  );
};

function SkeletonSection() {
  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="400">
          <SkeletonTextLine width="240px" height="16px" margin="0" />

          <SkeletonTextLine width="100%" height="28px" margin="-4.8px" />

          <SkeletonTextLine width="180px" margin="0" />

          <Divider />

          <BlockStack gap="100">
            <SkeletonTextLine width="100%" />
            <SkeletonTextLine lastLine />
          </BlockStack>

          <SkeletonTextLine width="240px" height="28px" margin="0" />

          <Divider />

          <BlockStack gap="100">
            <SkeletonTextLine width="100%" />
            <SkeletonTextLine lastLine />
          </BlockStack>

          <SkeletonTextLine width="240px" height="28px" margin="0" />

          <Divider />

          <BlockStack gap="100">
            <SkeletonTextLine width="100%" />
            <SkeletonTextLine width="100%" />
            <SkeletonTextLine lastLine />
          </BlockStack>

          <SkeletonTextLine width="240px" height="28px" margin="0" />
        </BlockStack>
      </Card>
    </Layout.Section>
  );
}

