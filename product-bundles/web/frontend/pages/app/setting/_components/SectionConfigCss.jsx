import { BlockStack, Button, Card, Layout, Text, TextField } from '@shopify/polaris';
import { SkeletonTextLine } from '../../../../components/ui';
import { useSupport } from '../../../../hooks';

export const SectionConfigCss = ({ formData, loading, onChange = () => {} }) => {
  const { startSupport } = useSupport();
  if (loading) return <SkeletonSection />;

  return (
    <>
      <Layout.Section>
        <Card>
          <BlockStack gap="300">
            <TextField
              id="custom_prod_css"
              value={formData?.custom_prod_css}
              onChange={onChange}
              label={<Text fontWeight="medium">Custom CSS for options on product page</Text>}
              multiline={6}
              maxLength={65535}
              showCharacterCount
              placeholder={`.mpg-swatch {

}`}
            />
            <TextField
              id="custom_card_css"
              value={formData?.custom_card_css}
              onChange={onChange}
              label={<Text fontWeight="medium">Custom CSS for options on product card</Text>}
              multiline={6}
              showCharacterCount
              maxLength={65535}
              helpText={
                <Text>
                  If you need help with styles customization please{' '}
                  <Button variant="plain" onClick={() => startSupport('I need help with style customization.')}>
                    contact us
                  </Button>
                  .
                </Text>
              }
              placeholder={`.mpg-swatch.mpg-card {

}`}
            />
          </BlockStack>
        </Card>
      </Layout.Section>
    </>
  );
};

function SkeletonSection() {
  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="100">
          <SkeletonTextLine width="240px" height="16px" />
          <SkeletonTextLine width="100%" height="160px" margin="1rem" />
          <SkeletonTextLine width="240px" height="16px" />
          <SkeletonTextLine width="100%" height="160px" />
          <SkeletonTextLine width="360px" margin="0" />
        </BlockStack>
      </Card>
    </Layout.Section>
  );
}

