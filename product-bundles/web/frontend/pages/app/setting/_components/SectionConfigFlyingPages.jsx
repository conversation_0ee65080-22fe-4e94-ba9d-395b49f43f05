import { BlockStack, Card, Layout, Text, TextField } from '@shopify/polaris';
import { SkeletonTextLine } from '../../../../components/ui';

export const SectionConfigFlyingPages = ({ formData, loading, onChange = () => {} }) => {
  if (loading) return <SkeletonSection />;

  return (
    <>
      <Layout.Section>
        <Card>
          <TextField
            id="fp_ignore_keywords_str"
            value={formData?.fp_ignore_keywords_str}
            onChange={onChange}
            label={<Text fontWeight="medium">Ignore keywords</Text>}
            placeholder="Enter ignore keywords, separate by comma . . ."
          />
        </Card>
      </Layout.Section>
    </>
  );
};

function SkeletonSection() {
  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="100">
          <SkeletonTextLine width="240px" height="16px" />
          <SkeletonTextLine width="100%" height="160px" margin="1rem" />
          <SkeletonTextLine width="240px" height="16px" />
          <SkeletonTextLine width="100%" height="160px" />
          <SkeletonTextLine width="360px" margin="0" />
        </BlockStack>
      </Card>
    </Layout.Section>
  );
}

