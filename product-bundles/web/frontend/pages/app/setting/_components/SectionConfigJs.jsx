import { BlockStack, Card, Layout, Text, TextField } from '@shopify/polaris';
import { SkeletonTextLine } from '../../../../components/ui';

export const SectionConfigJs = ({ formData, loading, onChange = () => {} }) => {
  if (loading) return <SkeletonSection />;

  return (
    <>
      <Layout.Section>
        <Card>
          <BlockStack gap="300">
            <TextField
              id="custom_prod_js"
              value={formData?.custom_prod_js}
              onChange={onChange}
              label={<Text fontWeight="medium">Custom JS for options on product page</Text>}
              multiline={6}
              maxLength={65535}
              showCharacterCount
              placeholder={`console.log('custom js for product page');`}
            />
            <TextField
              id="custom_card_js"
              value={formData?.custom_card_js}
              onChange={onChange}
              label={<Text fontWeight="medium">Custom JS for options on product card</Text>}
              multiline={6}
              showCharacterCount
              maxLength={65535}
              placeholder={`console.log('custom js for product card');`}
            />
          </BlockStack>
        </Card>
      </Layout.Section>
    </>
  );
};

function SkeletonSection() {
  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="100">
          <SkeletonTextLine width="240px" height="16px" />
          <SkeletonTextLine width="100%" height="160px" margin="1rem" />
          <SkeletonTextLine width="240px" height="16px" />
          <SkeletonTextLine width="100%" height="160px" />
          <SkeletonTextLine width="360px" margin="0" />
        </BlockStack>
      </Card>
    </Layout.Section>
  );
}

