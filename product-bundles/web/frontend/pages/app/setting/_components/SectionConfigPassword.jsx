import { BlockStack, But<PERSON>, Card, Layout, Text, TextField } from '@shopify/polaris';
import { SkeletonTextLine } from '../../../../components/ui';
import { useContext } from 'react';
import { AppDataContext } from '../../../../context/AppDataContext';

export const SectionConfigPassword = ({ formData, loading, onChange = () => {} }) => {
  const { shop } = useContext(AppDataContext);
  const shopIdentifier = shop?.shopify_data?.domain?.split('.myshopify.com')?.[0];
  const passConfigUrl = `https://admin.shopify.com/store/${shopIdentifier}/online_store/preferences?tutorial=unlock`;

  if (loading) return <SkeletonSection />;

  return (
    <Layout.Section>
      <Card>
        <TextField
          id="password"
          value={formData?.password}
          placeholder="Enter password"
          onChange={onChange}
          label={<Text fontWeight="medium">Online store password</Text>}
          helpText={
            <Text>
              You can find your online store password{' '}
              <Button variant="plain" onClick={() => window.open(passConfigUrl, '_blank')}>
                here
              </Button>
            </Text>
          }
        />
      </Card>
    </Layout.Section>
  );
};

function SkeletonSection() {
  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="100">
          <SkeletonTextLine width="120px" height="16px" />
          <SkeletonTextLine width="100%" height="32px" />
          <SkeletonTextLine width="260px" margin="0" />
        </BlockStack>
      </Card>
    </Layout.Section>
  );
}
