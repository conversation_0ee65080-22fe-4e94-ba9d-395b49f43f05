import { BlockStack, Card, FormLayout, InlineStack, Layout, Select, Text } from '@shopify/polaris';
import { SkeletonTextLine } from '../../../../components/ui';
import { CustomInput } from '../../../../components/input';
import { isAdmin } from '../../../../utils/auth';
import { useContext } from 'react';
import { AppDataContext } from '../../../../context/AppDataContext';

export const SectionConfigResync = ({ formData, loading, disabled, onChange = () => {} }) => {
  const { limitation } = useContext(AppDataContext);

  if (loading) return <SkeletonSection />;

  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="300">
          <FormLayout>
            <FormLayout.Group helpText="Automatically update product details within each product group daily to ensure titles, images, prices, stock status, and more are always up to date.">
              <Select
                id="resync_group_mode"
                label={<Text fontWeight="medium">Auto-sync products</Text>}
                options={
                  isAdmin()
                    ? [
                        { label: 'Disable Auto-Sync', value: 'disable' },
                        { label: 'Enable Auto-Sync (Automatic Time)', value: 'enable' },
                        { label: 'Enable Auto-Sync (Manual Time)', value: 'enable_with_preset_time' },
                      ]
                    : [
                        { label: 'Disabled', value: 'disable' },
                        { label: 'Enabled', value: 'enable' },
                      ]
                }
                value={formData?.resync_group_mode}
                disabled={disabled}
                onChange={onChange}
              />

              <Select
                id="resync_group_times_per_day"
                label={<Text fontWeight="medium">Auto-sync frequency</Text>}
                options={[
                  { label: 'Once a day', value: '1', disabled: limitation?.max_sync_count < 1 },
                  { label: 'Twice a day', value: '2', disabled: limitation?.max_sync_count < 2 },
                  { label: 'Three times a day', value: '3', disabled: limitation?.max_sync_count < 3 },
                  { label: 'Four times a day', value: '4', disabled: limitation?.max_sync_count < 4 },
                ]}
                value={formData?.resync_group_times_per_day}
                disabled={formData?.resync_group_mode === 'disable' || disabled}
                onChange={onChange}
              />

              {isAdmin() && (
                <CustomInput
                  id="resync_group_time"
                  type="time"
                  label="Sync Execution Time (UTC)"
                  placeholder="HH:MM"
                  value={formData?.resync_group_time}
                  disabled={formData?.resync_group_mode !== 'enable_with_preset_time' || disabled}
                  onChange={onChange}
                />
              )}
            </FormLayout.Group>
          </FormLayout>
        </BlockStack>
      </Card>
    </Layout.Section>
  );
};

function SkeletonSection() {
  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="300">
          <InlineStack gap="300">
            <div style={{ flex: 1 }}>
              <SkeletonTextLine width="120px" height="16px" margin="4px" />
              <SkeletonTextLine width="100%" height="32px" margin="0" />
            </div>

            <div style={{ flex: 1 }}>
              <SkeletonTextLine width="120px" height="16px" margin="4px" />
              <SkeletonTextLine width="100%" height="32px" margin="0" />
            </div>
          </InlineStack>

          <BlockStack gap="100">
            <SkeletonTextLine width="100%" />
            <SkeletonTextLine lastLine />
          </BlockStack>
        </BlockStack>
      </Card>
    </Layout.Section>
  );
}

