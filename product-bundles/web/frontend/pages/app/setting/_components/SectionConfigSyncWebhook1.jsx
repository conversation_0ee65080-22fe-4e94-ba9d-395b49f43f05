import { BlockStack, Box, Card, Checkbox, Layout, Text } from '@shopify/polaris';
import { SkeletonTextLine } from '../../../../components/ui';

export const SectionConfigSyncWebhook1 = ({ formData, loading, disabled, onChange = () => {} }) => {
  if (loading) return <SkeletonSection />;

  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="100">
          <Box opacity={disabled ? 0.5 : 1}>
            <Text>
              When enabled, this feature automatically adds newly created products to existing automation groups if they
              match the group&apos;s rules.
            </Text>
          </Box>

          <Checkbox
            label="Enable auto-add products to automation groups"
            id="auto_update_automation_group"
            checked={formData?.auto_update_automation_group}
            disabled={disabled}
            onChange={onChange}
          />
        </BlockStack>
      </Card>
    </Layout.Section>
  );
};

function SkeletonSection() {
  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="400">
          <BlockStack gap="100">
            <SkeletonTextLine width="100%" />
            <SkeletonTextLine lastLine />
          </BlockStack>

          <SkeletonTextLine width="240px" height="28px" margin="0" />
        </BlockStack>
      </Card>
    </Layout.Section>
  );
}

