import { BlockStack, Box, Card, Checkbox, Layout, Text } from '@shopify/polaris';
import { SkeletonTextLine } from '../../../../components/ui';

export const SectionConfigSyncWebhook2 = ({ formData, loading, disabled, onChange = () => {} }) => {
  if (loading) return <SkeletonSection />;

  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="100">
          <Box opacity={disabled ? 0.5 : 1}>
            <Text>
              This feature instantly updates availability status in product groups when stock levels change. Regular
              sync updates only happen every 6+ hours, while this option provides immediate inventory accuracy.
            </Text>
          </Box>

          <Checkbox
            label="Enable auto-update product available status"
            id="auto_update_stock_instantly"
            checked={formData?.auto_update_stock_instantly}
            disabled={disabled}
            onChange={onChange}
          />
        </BlockStack>
      </Card>
    </Layout.Section>
  );
};

function SkeletonSection() {
  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="400">
          <BlockStack gap="100">
            <SkeletonTextLine width="100%" />
            <SkeletonTextLine width="100%" />
            <SkeletonTextLine lastLine />
          </BlockStack>

          <SkeletonTextLine width="240px" height="28px" margin="0" />
        </BlockStack>
      </Card>
    </Layout.Section>
  );
}

