import { <PERSON><PERSON>ta<PERSON>, But<PERSON>, Card, Grid, Layout } from '@shopify/polaris';
import { SkeletonTextLine } from '../../../../components/ui';
import { ToggleOnIcon, ToggleOffIcon } from '@shopify/polaris-icons';

export const SectionConfigThemeApp = ({ formData, loading, onChange = () => {} }) => {
  if (loading) return <SkeletonSection />;

  return (
    <>
      <Layout.Section>
        <Card>
          <Grid columns={{ xs: 3 }} gap={{ xs: '8px', sm: '8px', md: '8px', lg: '8px', xl: '8px' }}>
            <Grid.Cell columnSpan={{ xs: 3, sm: 3, md: 3, lg: 6, xl: 6 }}>
              {formData?.app_enabled_1 && (
                <Button fullWidth tone="success" icon={ToggleOnIcon} onClick={() => onChange(false, 'app_enabled_1')}>
                  Swatch Renderer is ON
                </Button>
              )}
              {!formData?.app_enabled_1 && (
                <Button fullWidth tone="critical" icon={ToggleOffIcon} onClick={() => onChange(true, 'app_enabled_1')}>
                  Swatch Renderer is OFF
                </Button>
              )}
            </Grid.Cell>

            <Grid.Cell columnSpan={{ xs: 3, sm: 3, md: 3, lg: 6, xl: 6 }}>
              {formData?.app_enabled_2 && (
                <Button fullWidth tone="success" icon={ToggleOnIcon} onClick={() => onChange(false, 'app_enabled_2')}>
                  Speed Booster is ON
                </Button>
              )}
              {!formData?.app_enabled_2 && (
                <Button fullWidth tone="critical" icon={ToggleOffIcon} onClick={() => onChange(true, 'app_enabled_2')}>
                  Speed Booster is OFF
                </Button>
              )}
            </Grid.Cell>

            <Grid.Cell columnSpan={{ xs: 3, sm: 3, md: 3, lg: 6, xl: 6 }}>
              {formData?.swatch_enabled && (
                <Button fullWidth tone="success" icon={ToggleOnIcon} onClick={() => onChange(false, 'swatch_enabled')}>
                  Global Styles is ON
                </Button>
              )}
              {!formData?.swatch_enabled && (
                <Button fullWidth tone="critical" icon={ToggleOffIcon} onClick={() => onChange(true, 'swatch_enabled')}>
                  Global Styles is OFF
                </Button>
              )}
            </Grid.Cell>

            <Grid.Cell columnSpan={{ xs: 3, sm: 3, md: 3, lg: 6, xl: 6 }}>
              {formData?.browser_select_box_enabled && (
                <Button
                  fullWidth
                  tone="success"
                  icon={ToggleOnIcon}
                  onClick={() => onChange(false, 'browser_select_box_enabled')}
                >
                  USE Browser Select Box
                </Button>
              )}
              {!formData?.browser_select_box_enabled && (
                <Button
                  fullWidth
                  tone="critical"
                  icon={ToggleOffIcon}
                  onClick={() => onChange(true, 'browser_select_box_enabled')}
                >
                  NOT USE Browser Select Box
                </Button>
              )}
            </Grid.Cell>

            <Grid.Cell columnSpan={{ xs: 3, sm: 3, md: 3, lg: 6, xl: 6 }}>
              {formData?.override_grouped_product_option && (
                <Button
                  fullWidth
                  tone="success"
                  icon={ToggleOnIcon}
                  onClick={() => onChange(false, 'override_grouped_product_option')}
                >
                  OVERRIDE grouped product option
                </Button>
              )}
              {!formData?.override_grouped_product_option && (
                <Button
                  fullWidth
                  tone="critical"
                  icon={ToggleOffIcon}
                  onClick={() => onChange(true, 'override_grouped_product_option')}
                >
                  NOT OVERRIDE grouped product option
                </Button>
              )}
            </Grid.Cell>

            <Grid.Cell columnSpan={{ xs: 3, sm: 3, md: 3, lg: 6, xl: 6 }}>
              {formData?.hidden_product_in_group_allowed && (
                <Button
                  fullWidth
                  tone="success"
                  icon={ToggleOnIcon}
                  onClick={() => onChange(false, 'hidden_product_in_group_allowed')}
                >
                  ALLOW group contains POS product
                </Button>
              )}
              {!formData?.hidden_product_in_group_allowed && (
                <Button
                  fullWidth
                  tone="critical"
                  icon={ToggleOffIcon}
                  onClick={() => onChange(true, 'hidden_product_in_group_allowed')}
                >
                  DENY group contains POS product
                </Button>
              )}
            </Grid.Cell>

            <Grid.Cell columnSpan={{ xs: 3, sm: 3, md: 3, lg: 6, xl: 6 }}>
              {formData?.draft_product_in_group_allowed && (
                <Button
                  fullWidth
                  tone="success"
                  icon={ToggleOnIcon}
                  onClick={() => onChange(false, 'draft_product_in_group_allowed')}
                >
                  ALLOW group contains draft product
                </Button>
              )}
              {!formData?.draft_product_in_group_allowed && (
                <Button
                  fullWidth
                  tone="critical"
                  icon={ToggleOffIcon}
                  onClick={() => onChange(true, 'draft_product_in_group_allowed')}
                >
                  DENY group contains draft product
                </Button>
              )}
            </Grid.Cell>

            <Grid.Cell columnSpan={{ xs: 3, sm: 3, md: 3, lg: 6, xl: 6 }}>
              {formData?.archived_product_in_group_allowed && (
                <Button
                  fullWidth
                  tone="success"
                  icon={ToggleOnIcon}
                  onClick={() => onChange(false, 'archived_product_in_group_allowed')}
                >
                  ALLOW group contains archived product
                </Button>
              )}
              {!formData?.archived_product_in_group_allowed && (
                <Button
                  fullWidth
                  tone="critical"
                  icon={ToggleOffIcon}
                  onClick={() => onChange(true, 'archived_product_in_group_allowed')}
                >
                  DENY group contains archived product
                </Button>
              )}
            </Grid.Cell>
          </Grid>
        </Card>
      </Layout.Section>
    </>
  );
};

function SkeletonSection() {
  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="100">
          <SkeletonTextLine width="240px" height="16px" />
          <SkeletonTextLine width="100%" height="160px" margin="1rem" />
          <SkeletonTextLine width="240px" height="16px" />
          <SkeletonTextLine width="100%" height="160px" />
          <SkeletonTextLine width="360px" margin="0" />
        </BlockStack>
      </Card>
    </Layout.Section>
  );
}

