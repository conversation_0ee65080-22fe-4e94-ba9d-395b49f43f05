import { BlockStack, Card, TextField, Grid, Layout, Text, Divider } from '@shopify/polaris';
import { SkeletonTextLine } from '../../../../components/ui';

export const SectionConfigTimeout = ({ formData, loading, onChange = () => {} }) => {
  if (loading) return <SkeletonSection />;

  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="300">
          <Text fontWeight="medium">Timeout when page load (ms)</Text>

          <Grid
            columns={{ xs: 4, sm: 4, md: 4, lg: 4, xl: 4 }}
            gap={{ xs: '0.5rem', sm: '0.5rem', md: '0.5rem', lg: '0.5rem', xl: '0.5rem' }}
          >
            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Home page"
                id="timeout_home"
                value={formData?.timeout_home}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Collection page"
                id="timeout_collection"
                value={formData?.timeout_collection}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Product page"
                id="timeout_product"
                value={formData?.timeout_product}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Search page"
                id="timeout_search"
                value={formData?.timeout_search}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Cart page"
                id="timeout_cart"
                value={formData?.timeout_cart}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Pages"
                id="timeout_page"
                value={formData?.timeout_page}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Blog page"
                id="timeout_blog"
                value={formData?.timeout_blog}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Article page"
                id="timeout_article"
                value={formData?.timeout_article}
                onChange={onChange}
              />
            </Grid.Cell>
          </Grid>

          <Divider />

          <Text fontWeight="medium">Timeout when override function / history change (ms)</Text>

          <Grid
            columns={{ xs: 4, sm: 4, md: 4, lg: 4, xl: 4 }}
            gap={{ xs: '0.5rem', sm: '0.5rem', md: '0.5rem', lg: '0.5rem', xl: '0.5rem' }}
          >
            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Override XHR"
                id="timeout_override_xhr"
                value={formData?.timeout_override_xhr}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Override fetch"
                id="timeout_override_fetch"
                value={formData?.timeout_override_fetch}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Override push state"
                id="timeout_override_push_state"
                value={formData?.timeout_override_push_state}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Override replace state"
                id="timeout_override_replace_state"
                value={formData?.timeout_override_replace_state}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="BASF app load"
                id="timeout_basf_app"
                value={formData?.timeout_basf_app}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="BASF history change"
                id="timeout_location_basf"
                value={formData?.timeout_location_basf}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Prod history change"
                id="timeout_location_prod"
                value={formData?.timeout_location_prod}
                onChange={onChange}
              />
            </Grid.Cell>

            <Grid.Cell>
              <TextField
                type="number"
                min="0"
                step="100"
                prefix="ms"
                label="Card history change"
                id="timeout_location_card"
                value={formData?.timeout_location_card}
                onChange={onChange}
              />
            </Grid.Cell>
          </Grid>
        </BlockStack>
      </Card>
    </Layout.Section>
  );
};

function SkeletonSection() {
  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="400">
          <SkeletonTextLine width="270px" height="16px" margin="0" />

          <Grid
            columns={{ xs: 3, sm: 3, md: 3, lg: 3, xl: 3 }}
            gap={{ xs: '0.5rem', sm: '0.5rem', md: '0.5rem', lg: '0.5rem', xl: '0.5rem' }}
          >
            <Grid.Cell>
              <SkeletonTextLine width="50%" />
              <SkeletonTextLine width="100%" height="32px" margin="0" />
            </Grid.Cell>
            <Grid.Cell>
              <SkeletonTextLine width="50%" />
              <SkeletonTextLine width="100%" height="32px" margin="0" />
            </Grid.Cell>
            <Grid.Cell>
              <SkeletonTextLine width="50%" />
              <SkeletonTextLine width="100%" height="32px" margin="0" />
            </Grid.Cell>
            <Grid.Cell>
              <SkeletonTextLine width="50%" />
              <SkeletonTextLine width="100%" height="32px" margin="0" />
            </Grid.Cell>
            <Grid.Cell>
              <SkeletonTextLine width="50%" />
              <SkeletonTextLine width="100%" height="32px" margin="0" />
            </Grid.Cell>
            <Grid.Cell>
              <SkeletonTextLine width="50%" />
              <SkeletonTextLine width="100%" height="32px" margin="0" />
            </Grid.Cell>
            <Grid.Cell>
              <SkeletonTextLine width="50%" />
              <SkeletonTextLine width="100%" height="32px" margin="-4.8px" />
            </Grid.Cell>
            <Grid.Cell>
              <SkeletonTextLine width="50%" />
              <SkeletonTextLine width="100%" height="32px" margin="-4.8px" />
            </Grid.Cell>
            <Grid.Cell>
              <SkeletonTextLine width="50%" />
              <SkeletonTextLine width="100%" height="32px" margin="-4.8px" />
            </Grid.Cell>
          </Grid>
        </BlockStack>
      </Card>
    </Layout.Section>
  );
}

