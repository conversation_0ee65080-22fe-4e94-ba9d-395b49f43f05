import { <PERSON><PERSON>tack, Card, TextField, Grid, Layout, Text, Button, Select, InlineStack } from '@shopify/polaris';
import { SkeletonTextLine } from '../../../../components/ui';
import { DeleteIcon } from '@shopify/polaris-icons';

export const SectionConfigUrl = ({ formData, loading, onChange = () => {} }) => {
  if (loading) return <SkeletonSection />;

  const handleAddUrl = () => {
    const urls = formData?.observe_urls || [];
    urls.push({ url: '', page: '', timeout: 0 });
    onChange(urls, 'observe_urls');
  };

  const handleRemoveUrl = (i) => {
    const urls = formData?.observe_urls?.filter((_, ui) => ui !== i);
    onChange(urls, 'observe_urls');
  };

  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="300">
          <Text fontWeight="medium">List of Observed URLs</Text>
          <Text>
            When a shop visitor performs actions like filtering or sorting, the product grid may re-render after certain
            requests. Providing those requests here helps ensure product options are re-rendered correctly in the
            product card.
          </Text>

          {formData?.observe_urls?.map((urlConfig, index) => (
            <Grid
              key={index}
              columns={{ xs: 4, sm: 4, md: 4, lg: 4, xl: 4 }}
              gap={{ xs: '0.5rem', sm: '0.5rem', md: '0.5rem', lg: '0.5rem', xl: '0.5rem' }}
            >
              <Grid.Cell columnSpan={{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }}>
                <TextField
                  label={`URL ${index + 1}`}
                  id={`observe_urls[${index}].url`}
                  value={urlConfig?.url}
                  placeholder={`Enter URL ${index + 1} . . .`}
                  onChange={onChange}
                />
              </Grid.Cell>

              <Grid.Cell>
                <Select
                  label="Page type"
                  id={`observe_urls[${index}].page`}
                  value={urlConfig?.page}
                  placeholder="Select page type"
                  options={[
                    { label: 'Article', value: 'article' },
                    { label: 'Blog', value: 'blog' },
                    { label: 'Collection', value: 'collection' },
                    { label: 'Cart', value: 'cart' },
                    { label: 'Home', value: 'index' },
                    { label: 'Page', value: 'page' },
                    { label: 'Product', value: 'product' },
                    { label: 'Search', value: 'search' },
                  ]}
                  onChange={onChange}
                />
              </Grid.Cell>

              <Grid.Cell>
                <InlineStack wrap={false} blockAlign="end" gap="200">
                  <TextField
                    type="number"
                    min="0"
                    step="100"
                    label="Timeout (ms)"
                    id={`observe_urls[${index}].timeout`}
                    value={urlConfig?.timeout}
                    onChange={onChange}
                  />
                  <Button
                    size="large"
                    variant="primary"
                    tone="critical"
                    icon={DeleteIcon}
                    onClick={() => handleRemoveUrl(index)}
                  />
                </InlineStack>
              </Grid.Cell>
            </Grid>
          ))}

          <InlineStack>
            <Button variant="primary" onClick={handleAddUrl}>
              Add URL
            </Button>
          </InlineStack>
        </BlockStack>
      </Card>
    </Layout.Section>
  );
};

function SkeletonSection() {
  return (
    <Layout.Section>
      <Card>
        <BlockStack gap="400">
          <SkeletonTextLine width="130px" height="16px" margin="0" />

          <BlockStack gap="100">
            <SkeletonTextLine />
            <SkeletonTextLine />
            <SkeletonTextLine lastLine />
          </BlockStack>

          <SkeletonTextLine width="75px" height="28px" margin="0" />
        </BlockStack>
      </Card>
    </Layout.Section>
  );
}

