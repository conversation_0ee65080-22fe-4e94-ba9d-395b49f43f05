import { Page, Layout, BlockStack, Text, Box, Button, InlineStack, Icon, Card } from '@shopify/polaris';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import _ from 'lodash';

import { ActionLogApi, MerchantConfigApi } from '../../../apis';
import {
  SectionConfigApiKey,
  SectionConfigCard,
  SectionConfigCss,
  SectionConfigFlyingPages,
  SectionConfigJs,
  SectionConfigPassword,
  SectionConfigResync,
  SectionConfigSyncWebhook1,
  SectionConfigSyncWebhook2,
  SectionConfigThemeApp,
  SectionConfigTimeout,
  SectionConfigUrl,
} from './_components';
import { useSaveBar, useToast } from '../../../hooks';
import { isAdmin } from '../../../utils/auth';
import { AppDataContext } from '../../../context/AppDataContext';
import { nav } from '../../../utils/mixed';
import { useNavigate } from 'react-router-dom';
import { LockIcon } from '@shopify/polaris-icons';
import { toHHMMSSDDMMYYYY } from '../../../utils/date';

export default function SettingPage() {
  const { isLoading: isAppDataLoading, merchantConfig, limitation, refetchData } = useContext(AppDataContext);
  const navigate = useNavigate();

  const { isLoading: isMerchantConfigUpdating, mutateAsync: updateMerchantConfig } = useMutation((data) =>
    MerchantConfigApi.update(data)
  );

  const {
    isFetching: isLogsGetting,
    data: logsData,
    refetch: refetchLogs,
  } = useQuery('action-logs-repo', () => ActionLogApi.get());

  const isLoading = useMemo(
    () => isMerchantConfigUpdating || isAppDataLoading || isLogsGetting,
    [isMerchantConfigUpdating, isAppDataLoading, isLogsGetting]
  );
  const showAdminConfig = isAdmin();
  const { toast } = useToast();
  const [cleanData, setCleanData] = useState(null);
  const [formData, setFormData] = useState(null);
  const isDirty = useMemo(() => JSON.stringify(cleanData) !== JSON.stringify(formData), [formData, cleanData]);

  useEffect(() => {
    const data = merchantConfig;
    data.fp_ignore_keywords_str =
      data.fp_ignore_keywords?.join(', ') || '/cart, /account/login, /account/logout, /account, /checkout';
    setCleanData(data);
    setFormData(data);
    //
  }, [merchantConfig]);

  const handleFieldChange = useCallback(
    (mainValue, mainId, affected = []) => {
      const newData = _.cloneDeep(formData);
      if (Array.isArray(affected)) affected.forEach(({ value, path }) => _.set(newData, path, value));
      if (mainId) _.set(newData, mainId, mainValue);
      setFormData(newData);
      //
    },
    [formData]
  );

  const handleSave = useCallback(async () => {
    formData.fp_ignore_keywords = formData.fp_ignore_keywords_str
      ?.split(',')
      ?.map((k) => k.trim())
      ?.filter((k) => k);
    await updateMerchantConfig(formData);
    await refetchData({ merchantConfig: true });
    await refetchLogs();
    toast.message('Settings updated');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData, updateMerchantConfig, refetchData, refetchLogs]);

  const handleDiscard = useCallback(() => setFormData(cleanData), [cleanData]);

  // contextual save bar
  const { open: saveBarOpen, show: showSaveBar, hide: hideSaveBar, SaveBar } = useSaveBar();

  useEffect(() => {
    if (isDirty) showSaveBar();
    else hideSaveBar();
    //
  }, [isDirty, hideSaveBar, showSaveBar]);

  return (
    <Page title="Settings">
      <Layout>
        <Layout.Section variant="oneThird">
          <Box paddingBlockStart="400">
            <BlockStack gap={400}>
              <Box opacity={limitation?.allowed_show_option_on_card ? 1 : 0.5}>
                <BlockStack gap="200">
                  <InlineStack gap="100">
                    <Text variant="headingMd">Show options on product cards</Text>
                    {!limitation?.allowed_show_option_on_card && (
                      <span>
                        <Icon source={LockIcon} />
                      </span>
                    )}
                  </InlineStack>
                  <Text>
                    Display product options on cards across the home page, collection page, and other relevant pages.
                  </Text>
                </BlockStack>
              </Box>

              {!limitation?.allowed_show_option_on_card && (
                <Text>
                  This feature is not included in your plan.{' '}
                  <Button variant="plain" onClick={() => navigate(nav('/pricing'))}>
                    Upgrade plan
                  </Button>{' '}
                  to unlock it.
                </Text>
              )}
            </BlockStack>
          </Box>
        </Layout.Section>
        <SectionConfigCard
          formData={formData}
          onChange={handleFieldChange}
          loading={isLoading}
          disabled={!limitation?.allowed_show_option_on_card}
        />

        <Layout.Section variant="oneThird">
          <Box paddingBlockStart="400">
            <BlockStack gap={400}>
              <Box opacity={limitation?.unlocked_auto_resync ? 1 : 0.5}>
                <BlockStack gap="200">
                  <InlineStack gap="100">
                    <Text variant="headingMd">Auto-sync product information</Text>
                    {!limitation?.unlocked_auto_resync && (
                      <span>
                        <Icon source={LockIcon} />
                      </span>
                    )}
                  </InlineStack>
                  <Text>Schedule daily product data sync to ensure accurate and up-to-date displays.</Text>
                </BlockStack>
              </Box>

              {!limitation?.unlocked_auto_resync && (
                <Text>
                  This feature is not included in your plan.{' '}
                  <Button variant="plain" onClick={() => navigate(nav('/pricing'))}>
                    Upgrade plan
                  </Button>{' '}
                  to unlock it.
                </Text>
              )}
            </BlockStack>
          </Box>
        </Layout.Section>
        <SectionConfigResync
          formData={formData}
          onChange={handleFieldChange}
          loading={isLoading}
          disabled={!limitation?.unlocked_auto_resync}
        />

        <Layout.Section variant="oneThird">
          <Box paddingBlockStart="400">
            <BlockStack gap={400}>
              <Box opacity={limitation?.allowed_update_automation_group ? 1 : 0.5}>
                <BlockStack gap="200">
                  <InlineStack gap="100">
                    <Text variant="headingMd">Auto-add products to automation groups</Text>
                    {!limitation?.allowed_update_automation_group && (
                      <span>
                        <Icon source={LockIcon} />
                      </span>
                    )}
                  </InlineStack>
                  <Text>Automatically include new products that match group rules</Text>
                </BlockStack>
              </Box>

              {!limitation?.allowed_update_automation_group && (
                <Text>
                  This feature is not included in your plan.{' '}
                  <Button variant="plain" onClick={() => navigate(nav('/pricing'))}>
                    Upgrade plan
                  </Button>{' '}
                  to unlock it.
                </Text>
              )}
            </BlockStack>
          </Box>
        </Layout.Section>
        <SectionConfigSyncWebhook1
          formData={formData}
          onChange={handleFieldChange}
          loading={isLoading}
          disabled={!limitation?.allowed_update_automation_group}
        />

        <Layout.Section variant="oneThird">
          <Box paddingBlockStart="400">
            <BlockStack gap={400}>
              <Box opacity={limitation?.allowed_update_stock_instantly ? 1 : 0.5}>
                <BlockStack gap="200">
                  <InlineStack gap="100">
                    <Text variant="headingMd">Update product available status instantly</Text>
                    {!limitation?.allowed_update_stock_instantly && (
                      <span>
                        <Icon source={LockIcon} />
                      </span>
                    )}
                  </InlineStack>
                  <Text>Keep product groups synced with real-time inventory updates </Text>
                </BlockStack>
              </Box>

              {!limitation?.allowed_update_stock_instantly && (
                <Text>
                  This feature is not included in your plan.{' '}
                  <Button variant="plain" onClick={() => navigate(nav('/pricing'))}>
                    Upgrade plan
                  </Button>{' '}
                  to unlock it.
                </Text>
              )}
            </BlockStack>
          </Box>
        </Layout.Section>
        <SectionConfigSyncWebhook2
          formData={formData}
          onChange={handleFieldChange}
          loading={isLoading}
          disabled={!limitation?.allowed_update_stock_instantly}
        />

        <Layout.Section variant="oneThird">
          <Box paddingBlockStart="400">
            <BlockStack gap={400}>
              <Box opacity={limitation?.allowed_request_api_key ? 1 : 0.5}>
                <BlockStack gap="200">
                  <InlineStack gap="100">
                    <Text variant="headingMd">Manage product groups through API</Text>
                    {!limitation?.allowed_request_api_key && (
                      <span>
                        <Icon source={LockIcon} />
                      </span>
                    )}
                  </InlineStack>
                  <Text>Enables seamless integration and efficient management of product groups through the API.</Text>
                </BlockStack>
              </Box>

              {!limitation?.allowed_request_api_key && (
                <Text>
                  This feature is not included in your plan.{' '}
                  <Button variant="plain" onClick={() => navigate(nav('/pricing'))}>
                    Upgrade plan
                  </Button>{' '}
                  to unlock it.
                </Text>
              )}
            </BlockStack>
          </Box>
        </Layout.Section>
        <SectionConfigApiKey
          formData={formData}
          onChange={handleFieldChange}
          loading={isLoading}
          disabled={!limitation?.allowed_request_api_key}
        />

        <Layout.Section variant="oneThird">
          <Box paddingBlockStart="400">
            <BlockStack gap="200">
              <Text variant="headingMd">Online store password</Text>
              <Text>Sharing your password allows us to securely access your store and provide better support.</Text>
            </BlockStack>
          </Box>
        </Layout.Section>
        <SectionConfigPassword formData={formData} onChange={handleFieldChange} loading={isLoading} />

        <Layout.Section variant="oneThird">
          <Box paddingBlockStart="400">
            <BlockStack gap="200">
              <Text variant="headingMd">Customize appearance</Text>
              <Text>Control the appearance of the app block with custom CSS styling.</Text>
            </BlockStack>
          </Box>
        </Layout.Section>
        <SectionConfigCss formData={formData} onChange={handleFieldChange} loading={isLoading} />

        {logsData?.data?.length > 0 && (
          <>
            <Layout.Section variant="oneThird">
              <Box paddingBlockStart="400">
                <BlockStack gap="200">
                  <Text variant="headingMd">Action logs</Text>
                  <Text>Latest 200 actions will be shown here.</Text>
                </BlockStack>
              </Box>
            </Layout.Section>
            <Layout.Section>
              <Card>
                <div style={{ maxHeight: 240, overflowY: 'auto' }}>
                  <BlockStack gap="300">
                    {logsData?.data?.map((log) => (
                      <Text key={log?._id}>
                        {toHHMMSSDDMMYYYY(log?.createdAt)} - {log.message}
                      </Text>
                    ))}
                  </BlockStack>
                </div>
              </Card>
            </Layout.Section>
          </>
        )}

        {showAdminConfig && (
          <>
            <Layout.Section variant="oneThird">
              <Box paddingBlockStart="400">
                <BlockStack gap="200">
                  <Text variant="headingMd">Customize behavior (ADMIN)</Text>
                  <Text>Customize the behavior of the app block with custom JavaScript.</Text>
                </BlockStack>
              </Box>
            </Layout.Section>
            <SectionConfigJs formData={formData} onChange={handleFieldChange} loading={isLoading} />

            <Layout.Section variant="oneThird">
              <Box paddingBlockStart="400">
                <BlockStack gap="200">
                  <Text variant="headingMd">Advanced config (ADMIN)</Text>
                </BlockStack>
              </Box>
            </Layout.Section>
            <SectionConfigThemeApp formData={formData} onChange={handleFieldChange} loading={isLoading} />

            <Layout.Section variant="oneThird">
              <Box paddingBlockStart="400">
                <BlockStack gap="200">
                  <Text variant="headingMd">Flying Pages config (ADMIN)</Text>
                </BlockStack>
              </Box>
            </Layout.Section>
            <SectionConfigFlyingPages formData={formData} onChange={handleFieldChange} loading={isLoading} />

            <Layout.Section variant="oneThird">
              <Box paddingBlockStart="400">
                <BlockStack gap="200">
                  <Text variant="headingMd">Card re-render timeout (ADMIN)</Text>
                  <Text>
                    To render options on product cards, we wait for the card to load and then insert the options by JS.
                    The timeout may vary depending on your shop and theme. Setting an appropriate timeout ensures
                    accurate rendering.
                  </Text>
                </BlockStack>
              </Box>
            </Layout.Section>
            <SectionConfigTimeout formData={formData} onChange={handleFieldChange} loading={isLoading} />

            <Layout.Section variant="oneThird">
              <Box paddingBlockStart="400">
                <BlockStack gap="200">
                  <Text variant="headingMd">Observed URLs (ADMIN)</Text>
                  <Text>Re-render product card options when specific URLs are requested.</Text>
                </BlockStack>
              </Box>
            </Layout.Section>
            <SectionConfigUrl formData={formData} onChange={handleFieldChange} loading={isLoading} />
          </>
        )}

        <Layout.Section />
      </Layout>

      <SaveBar
        open={saveBarOpen}
        saveAction={{
          loading: isLoading,
          onAction: handleSave,
        }}
        discardAction={{
          disabled: isLoading,
          onAction: handleDiscard,
        }}
      />
    </Page>
  );
}

