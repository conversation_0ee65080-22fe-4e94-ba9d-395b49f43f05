export const isAdmin = () => window?.location?.pathname?.includes('/admin');

// MANAGE SHOP AUTH TOKEN
const shopTokenKey = 'mtm_shop_auth_token';

export const setShopToken = (token) => {
  sessionStorage.setItem(shopTokenKey, token);
};

export const getShopToken = () => {
  return sessionStorage.getItem(shopTokenKey);
};

export const removeShopToken = () => {
  sessionStorage.removeItem(shopTokenKey);
};

// MANAGE ADMIN AUTH TOKEN
const adminTokenKey = 'mtm_admin_auth_token';

export const setAdminToken = (token) => {
  localStorage.setItem(adminTokenKey, token);
};

export const getAdminToken = () => {
  return localStorage.getItem(adminTokenKey);
};

export const removeAdminToken = () => {
  localStorage.removeItem(adminTokenKey);
};

