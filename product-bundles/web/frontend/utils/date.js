export const toHHMMSSDDMMYYYY = (date, timezone = 'Asia/Ho_Chi_Minh') => {
  date = new Date(date);

  const dateInTimezone = new Date(date.toLocaleString('en-US', { timeZone: timezone }));

  const hh = String(dateInTimezone.getHours()).padStart(2, '0');
  const mm = String(dateInTimezone.getMinutes()).padStart(2, '0');
  const ss = String(dateInTimezone.getSeconds()).padStart(2, '0');

  const DD = String(dateInTimezone.getDate()).padStart(2, '0');
  const MM = String(dateInTimezone.getMonth() + 1).padStart(2, '0');
  const YYYY = dateInTimezone.getFullYear();

  return `${hh}:${mm}:${ss} ${DD}/${MM}/${YYYY}`;
};

