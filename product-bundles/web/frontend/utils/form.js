import _ from 'lodash';

export const createErrorSchema = (oldSchema = {}, formData) => {
  if (!formData) return {};
  // creates an error schema object based on the provided form data object
  // each field in this schema will receive a value of true or false
  // a value of true indicates that the corresponding field in the form data object has an error
  const newSchema = _.cloneDeepWith(formData, (value) => {
    if (!_.isPlainObject(value) && !_.isArray(value)) {
      return false;
    }
  });

  // merge old and new schema
  function traverse(obj, path = '') {
    _.forOwn(obj, (value, key) => {
      const currentPath = path ? `${path}.${key}` : key;

      if (_.isPlainObject(value) || _.isArray(value)) {
        // if value is object or array, continue
        traverse(value, currentPath);
      } else {
        // get value from old schema and assign to new schema
        const sourceValue = _.get(oldSchema, currentPath);
        _.set(newSchema, currentPath, sourceValue ? sourceValue : value);
      }
    });
  }

  // start merge
  traverse(newSchema);
  return newSchema;
};

// loop through all field of error schema to find error
export const formHasError = (errorSchema) => {
  return _.some(errorSchema, (value) => {
    if (_.isPlainObject(value) || _.isArray(value)) {
      return formHasError(value);
    }
    return Boolean(value);
  });
};

// generate combinations from options
export const genCombinations = (options) => {
  const result = [];

  const helper = (index, currentCombo) => {
    if (index === options.length) {
      result.push([...currentCombo]);
      return;
    }

    for (const value of options[index].values) {
      currentCombo.push(value);
      helper(index + 1, currentCombo);
      currentCombo.pop();
    }
  };

  helper(0, []);
  return result;
};