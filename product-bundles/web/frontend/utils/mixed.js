import ShortUniqueId from 'short-unique-id';
import { isAdmin } from './auth';
const uid = new ShortUniqueId({ length: 10 });

// gen navigate link base on role
export const nav = (url) => {
  if (isAdmin()) return '/admin/portal' + url;
  return '/app' + url;
};

// create an random uuid
export const randomId = () => uid.rnd();

// extended falsy values
export const isFalsy = (value) => {
  return (
    (Array.isArray(value) && value.length === 0) ||
    (typeof value === 'object' && value !== null && Object.keys(value).length === 0) ||
    !value
  );
};

// check if site is run on test domain or not
export const isTesting = () =>
  window.location.host.includes('ngrok-free.app') || window.location.host.includes('trycloudflare.com');

// gen text color base on background color
export const genTextColor = (backgroundColor) => {
  if (!backgroundColor) return '#000000';

  const hex = backgroundColor.replace('#', '');

  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

  return luminance > 186 ? '#000000' : '#FFFFFF';
};

// download csv from json
export const downloadCSV = (jsonData, filename = 'data') => {
  const csvRows = [];
  const headers = Object.keys(jsonData[0]);
  csvRows.push(headers.join(','));

  jsonData.forEach((row) => {
    const values = headers.map((header) => JSON.stringify(row[header], (_, v) => v ?? ''));
    csvRows.push(values.join(','));
  });

  const csvContent = csvRows.join('\n');
  const blob = new Blob([csvContent], { type: 'text/csv' });

  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename}.csv`;
  link.click();
  window.URL.revokeObjectURL(url);
};

