import createApp from '@shopify/app-bridge';
export const getShopifyApp = () => {
  let host = new URLSearchParams(window.location.search).get('host') || window.__SHOPIFY_DEV_HOST || '';
  const apiKey = process.env.SHOPIFY_API_KEY;

  if (host) {
    sessionStorage.setItem('shopify_app_host', host);
  } else {
    host = sessionStorage.getItem('shopify_app_host');
  }

  if (!apiKey) {
    throw new Error('Missing SHOPIFY_API_KEY');
  }

  if (!host) {
    throw new Error('Missing host in URL');
  }

  return createApp({
    apiKey,
    host,
    forceRedirect: true,
  });
};

