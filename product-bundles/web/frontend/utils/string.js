export const normalizeStr = (str = '') => str.replace(/[^\p{L}\p{N}]/gu, '').toLowerCase();

export const isSimilarStr = (str1 = '', str2 = '') => {
  const normal1 = normalizeStr(str1);
  const normal2 = normalizeStr(str2);
  return normal1.includes(normal2) || normal2.includes(normal1);
};

export const isSimilarStrStrict = (str1 = '', str2 = '') => {
  const normal1 = normalizeStr(str1);
  const normal2 = normalizeStr(str2);
  return normal1 === normal2;
};
