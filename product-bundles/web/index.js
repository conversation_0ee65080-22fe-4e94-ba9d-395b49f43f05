import { join } from 'path';
import { readFileSync } from 'fs';
import express from 'express';
import serveStatic from 'serve-static';
import shopify from './shopify.js';
import PrivacyWebhookHandlers from './privacy.js';
import {
  allowCors,
  getSession,
  shopifyAuth,
  shopifyAuthCallback,
  syncShopData,
  verifyWebhook,
} from './backend/middlewares/_index.js';
import { config } from './backend/configs/_index.js';
import { connectDatabase } from './backend/database/mongo.adapter.js';
import {
  adminRoutes,
  appConfigRoutes,
  appDataRoutes,
  faqRoutes,
  fileRoutes,
  merchantConfigRoutes,
  recommendedAppRoutes,
  releaseNoteRoutes,
  shopRoutes,
  pricingPlanRoutes,
  webhookRoutes,
  discountRoutes,
  actionLogRoutes,
  reportRoutes,
} from './backend/routes/_index.js';

import './backend/crons/resync-task.cron.js';
import './backend/crons/resync-schedule.cron.js';
import './backend/crons/sync-rating.cron.js';

import dotenv from 'dotenv';
import { payment } from './backend/resolvers/payment.resolver.js';
dotenv.config();

const { APP_URL, BACKEND_PORT, STATIC_PATH } = config.env;
const { AUTH_PATH, AUTH_CALLBACK_PATH, WEBHOOK_PATH } = config.shopify;

console.info('APP_URL: ', APP_URL);

const app = express();
await connectDatabase();

/**
 * AUTHENTICATION AND WEBHOOKS WEBHOOK VERIFICATION
 */
app.use(allowCors);
app.get(AUTH_PATH, shopifyAuth);
app.get(AUTH_CALLBACK_PATH, shopifyAuthCallback, shopify.redirectToShopifyOrAppRoot());
app.post(WEBHOOK_PATH, shopify.processWebhooks({ webhookHandlers: PrivacyWebhookHandlers }));
app.use(WEBHOOK_PATH, express.raw({ type: '*/*' }), verifyWebhook);

app.post(`${WEBHOOK_PATH}/customers/data_request`, (_req, res) => {
  return res.status(200).send('OK');
});

app.post(`${WEBHOOK_PATH}/customers/redact`, (_req, res) => {
  return res.status(200).send('OK');
});

app.post(`${WEBHOOK_PATH}/shop/redact`, (_req, res) => {
  return res.status(200).send('OK');
});

app.use(WEBHOOK_PATH, webhookRoutes);

/**
 * API ROUTES
 */
app.use(express.json({ limit: '5mb' }));

app.use('/api/admin', adminRoutes);
app.use('/api/app-data', appDataRoutes);
app.use('/api/app-configs', appConfigRoutes);
app.use('/api/merchant-configs', merchantConfigRoutes);
app.use('/api/faqs', faqRoutes);
app.use('/api/discounts', discountRoutes);
app.use('/api/recommended-apps', recommendedAppRoutes);
app.use('/api/release-notes', releaseNoteRoutes);
app.use('/api/shops', shopRoutes);
app.use('/api/pricing-plans', pricingPlanRoutes);
app.use('/api/files', fileRoutes);
app.use('/api/action-logs', actionLogRoutes);
app.use('/api/reports', reportRoutes);

app.get('/api/app/payment', payment);

app.use('/api/shopify-auth/*', shopify.validateAuthenticatedSession());
app.get('/api/shopify-auth/session', syncShopData, getSession);

/**
 * SERVE STATIC RESOURCES
 */
app.use(shopify.cspHeaders());

app.use(serveStatic(STATIC_PATH, { index: false }));

app.use('/*', async (_req, res, _next) => {
  return res
    .status(200)
    .set('Content-Type', 'text/html')
    .send(
      readFileSync(join(STATIC_PATH, 'index.html'))
        .toString()
        .replace('%VITE_SHOPIFY_API_KEY%', process.env.SHOPIFY_API_KEY || '')
    );
});

app.listen(BACKEND_PORT);

