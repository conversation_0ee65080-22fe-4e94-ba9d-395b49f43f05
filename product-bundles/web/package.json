{"name": "backend-shopify-app-node", "version": "1.0.0", "private": true, "license": "UNLICENSED", "scripts": {"debug": "node --inspect-brk index.js", "dev": "cross-env NODE_ENV=development nodemon index.js --ignore ./frontend", "serve": "cross-env NODE_ENV=production node --max-old-space-size=4000 index.js", "lint": "eslint backend --ext .js,.mjs"}, "type": "module", "engines": {"node": ">=16.13.0"}, "dependencies": {"@shopify/shopify-api": "^11.3.0", "@shopify/shopify-app-express": "^2.1.3", "@shopify/shopify-app-session-storage": "^3.0.4", "@shopify/shopify-app-session-storage-sqlite": "^1.2.3", "axios": "^1.7.3", "compression": "^1.7.4", "cross-env": "^7.0.3", "crypto": "^1.0.1", "dotenv": "^16.4.5", "lodash": "^4.17.21", "moment": "^2.30.1", "mongoose": "^8.5.2", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "serve-static": "^1.14.1", "short-unique-id": "^5.2.0", "translate-google": "^1.5.0"}, "devDependencies": {"eslint": "8", "eslint-plugin-node": "^11.1.0", "nodemon": "^2.0.15", "prettier": "^2.6.2", "pretty-quick": "^3.1.3"}}